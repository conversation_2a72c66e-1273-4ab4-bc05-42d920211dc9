(()=>{var a={};a.id=477,a.ids=[477],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19080:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42100:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["projects",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,71490)),"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\admin\\projects\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\admin\\projects\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/projects/[id]/page",pathname:"/admin/projects/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/projects/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},44822:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Gautam\\\\Projects\\\\OmniPixel\\\\omnipixel-mvp\\\\app\\\\admin\\\\projects\\\\[id]\\\\client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\admin\\projects\\[id]\\client.tsx","default")},49885:(a,b,c)=>{Promise.resolve().then(c.bind(c,44822))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71490:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(37413),e=c(39916),f=c(32032),g=c(44822);async function h({params:a}){let b=await (0,f.U)(),{data:{user:c}}=await b.auth.getUser();c||(0,e.redirect)("/login");let{data:h}=await b.from("profiles").select("*").eq("id",c.id).single();if(!h||"platform_admin"!==h.role)return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Access Denied"}),(0,d.jsx)("p",{className:"text-gray-600 mt-2",children:"You need platform admin privileges to access this page."})]})});let{data:i,error:j}=await b.from("projects").select(`
      *,
      builds (
        id, filename, original_filename, s3_key, version, status, is_current, file_size,
        streampixel_build_id, streampixel_status, error_message,
        created_at, updated_at
      ),
      profiles (
        email, role
      )
    `).eq("id",a.id).single();return(j||!i)&&(0,e.notFound)(),(0,d.jsx)(g.default,{initialProject:i})}},74075:a=>{"use strict";a.exports=require("zlib")},77501:(a,b,c)=>{Promise.resolve().then(c.bind(c,87987))},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87987:(a,b,c)=>{"use strict";c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(16189),g=c(79150),h=c(95510),i=c(91634),j=c(52624),k=c(82477),l=c(24934),m=c(68988),n=c(96241);function o({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}var p=c(55192),q=c(59821),r=c(41862),s=c(28559),t=c(90131),u=c(88233),v=c(8819),w=c(11860),x=c(19080),y=c(31158),z=c(84027);function A({initialProject:a}){let b=(0,f.useRouter)(),[c,n]=(0,e.useState)(a),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(null),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(!1),[I,J]=(0,e.useState)(!1),[K,L]=(0,e.useState)({name:a.name,stream_project_id:a.stream_project_id,user_email:a.profiles.email,config:JSON.stringify(a.config||{},null,2)}),M=async()=>{B(!0),D(null);try{let a=await fetch(`/api/admin/projects/${c.id}`);if(!a.ok)throw Error("Failed to refresh project");let b=await a.json();n(b.project),L({name:b.project.name,stream_project_id:b.project.stream_project_id,user_email:b.project.profiles.email,config:JSON.stringify(b.project.config||{},null,2)})}catch(a){D(a instanceof Error?a.message:"An unknown error occurred")}finally{B(!1)}},N=async()=>{if(c){H(!0),D(null);try{let a;try{a=JSON.parse(K.config)}catch{throw Error("Invalid JSON in config field")}let b=await fetch("/api/admin/projects",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({project_id:c.id,name:K.name,stream_project_id:K.stream_project_id,user_email:K.user_email,config:a})});if(!b.ok){let a=await b.json();throw Error(a.error||"Failed to update project")}let d=await b.json();n(d.project),F(!1),alert("Project updated successfully!")}catch(a){D(a instanceof Error?a.message:"An unknown error occurred"),alert("Failed to update project: "+a)}finally{H(!1)}}},O=async()=>{if(c&&confirm(`Are you sure you want to delete "${c.name}"? This action cannot be undone.`)){J(!0),D(null);try{let a=await fetch("/api/admin/projects",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({project_id:c.id})});if(!a.ok){let b=await a.json();throw Error(b.error||"Failed to delete project")}alert("Project deleted successfully!"),b.push("/admin")}catch(a){D(a instanceof Error?a.message:"An unknown error occurred"),alert("Failed to delete project: "+a)}finally{J(!1)}}},P=async a=>{try{if(!(await fetch(`/api/projects/${c.id}/builds/revert`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({build_id:a})})).ok)throw Error("Failed to revert build");M(),alert("Build reverted successfully!")}catch(a){alert("Failed to revert build: "+(a instanceof Error?a.message:"Unknown error"))}},Q=async a=>{try{if(!(await fetch(`/api/projects/${c.id}/builds/${a}`,{method:"DELETE"})).ok)throw Error("Failed to delete build");M(),alert("Build deleted successfully!")}catch(a){alert("Failed to delete build: "+a)}},R=async a=>{try{if(!(await fetch(`/api/projects/${c.id}/builds/${a}`,{method:"PATCH"})).ok)throw Error("Failed to activate build");M(),alert("Build activated successfully!")}catch(a){alert("Failed to activate build: "+(a instanceof Error?a.message:"Unknown error"))}};return A?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(g.V,{}),(0,d.jsxs)("div",{className:"flex items-center justify-center py-16",children:[(0,d.jsx)(r.A,{className:"h-8 w-8 animate-spin"})," Loading..."]})]}):C||!c?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(g.V,{}),(0,d.jsx)("div",{className:"flex items-center justify-center py-16",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Error"}),(0,d.jsx)("p",{className:"text-red-600 mt-2",children:C||"Project not found"}),(0,d.jsxs)(l.$,{onClick:()=>b.push("/admin"),className:"mt-4",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 mr-2"}),"Back to Admin Panel"]})]})})]}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(g.V,{}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)(l.$,{variant:"ghost",onClick:()=>b.push("/admin"),className:"mb-4",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 mr-2"})," Back to Admin Panel"]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:c.name}),(0,d.jsxs)("p",{className:"text-gray-600 mt-1",children:["Owned by ",c.profiles.email," • Stream ID: ",c.stream_project_id]})]}),(0,d.jsx)("div",{className:"flex space-x-2",children:E?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(l.$,{onClick:N,disabled:G,children:[G?(0,d.jsx)(r.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]}),(0,d.jsxs)(l.$,{variant:"outline",onClick:()=>{c&&(L({name:c.name,stream_project_id:c.stream_project_id,user_email:c.profiles.email,config:JSON.stringify(c.config||{},null,2)}),F(!1))},children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-2"})," Cancel"]})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(l.$,{onClick:()=>F(!0),children:[(0,d.jsx)(t.A,{className:"h-4 w-4 mr-2"})," Edit Project"]}),(0,d.jsxs)(l.$,{variant:"destructive",onClick:O,disabled:I,children:[I?(0,d.jsx)(r.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Delete Project"]})]})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[!E&&c.stream_project_id&&(0,d.jsx)(k.StreamPlayer,{projectId:c.id,buildId:c.builds?.find(a=>a.is_current)?.id,config:c.config,showControls:!0,showHeader:!0,showEmbedButton:!0}),E&&(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"Edit Project Details"}),(0,d.jsx)(p.BT,{children:"Update project information and configuration"})]}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Project Name"}),(0,d.jsx)(m.p,{value:K.name,onChange:a=>L({...K,name:a.target.value})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Stream Project ID"}),(0,d.jsx)(m.p,{value:K.stream_project_id,onChange:a=>L({...K,stream_project_id:a.target.value})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Owner Email"}),(0,d.jsx)(m.p,{value:K.user_email,onChange:a=>L({...K,user_email:a.target.value})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Project Configuration (JSON)"}),(0,d.jsx)(o,{value:K.config,onChange:a=>L({...K,config:a.target.value}),rows:8,className:"font-mono text-sm"})]})]})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"Upload New Build"}),(0,d.jsx)(p.BT,{children:"Upload a new build file for this project as an admin."})]}),c.builds.length<2&&(0,d.jsx)(p.Wu,{children:(0,d.jsx)(h.s,{projectId:c.id,onUploadComplete:()=>{alert("Build uploaded successfully!"),M()},onUploadError:a=>{alert("Upload failed: "+a)}})})]}),(0,d.jsx)(i.g,{projectId:c.id,currentConfig:c.config||{},onConfigUpdate:a=>n({...c,config:{...a,primaryCodec:a.primaryCodec,fallBackCodec:a.fallBackCodec,resolutionMode:a.resolutionMode,appId:c.id}}),isAdmin:!0}),(0,d.jsx)(j.T,{projectId:c.id,builds:(c.builds||[]).map((a,b)=>({...a,id:a.id||`admin-build-${b}-${a.filename}`,project_id:c.id,s3_key:a.filename,status:"active",updated_at:a.created_at})),onBuildRevert:P,onBuildDelete:Q,onBuildActivate:R,onRefresh:M,isAdmin:!0})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(p.Zp,{children:[(0,d.jsx)(p.aR,{children:(0,d.jsxs)(p.ZB,{className:"flex items-center",children:[(0,d.jsx)(x.A,{className:"h-5 w-5 mr-2"}),"Project Statistics"]})}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Total Builds:"}),(0,d.jsx)(q.E,{variant:"secondary",children:c.builds?.length||0})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Latest Version:"}),(0,d.jsx)(q.E,{variant:"outline",children:c.builds?.[0]?.version||"None"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Owner Role:"}),(0,d.jsx)(q.E,{variant:"platform_admin"===c.profiles.role?"default":"secondary",children:c.profiles.role})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Created:"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:new Date(c.created_at).toLocaleDateString()})]})]})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsx)(p.aR,{children:(0,d.jsx)(p.ZB,{children:"Recent Builds"})}),(0,d.jsx)(p.Wu,{children:c.builds&&c.builds.length>0?(0,d.jsx)("div",{className:"space-y-3",children:c.builds.slice(0,5).map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-sm",children:a.original_filename||a.filename}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["Version ",a.version," • ",new Date(a.created_at).toLocaleDateString()]})]}),(0,d.jsx)(l.$,{size:"sm",variant:"outline",children:(0,d.jsx)(y.A,{className:"h-4 w-4"})})]},a.id))}):(0,d.jsx)("p",{className:"text-gray-500 text-sm",children:"No builds uploaded yet"})})]}),!E&&(0,d.jsxs)(p.Zp,{children:[(0,d.jsx)(p.aR,{children:(0,d.jsxs)(p.ZB,{className:"flex items-center",children:[(0,d.jsx)(z.A,{className:"h-5 w-5 mr-2"})," Current Configuration"]})}),(0,d.jsx)(p.Wu,{children:(0,d.jsx)("pre",{className:"text-xs bg-gray-50 p-3 rounded-lg overflow-auto",children:JSON.stringify(c.config||{},null,2)})})]})]})]})]})]})}},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,3811,4999,1161,2579,7660,2228,2121,4857,8321,9007,6813,2477,1634,3757],()=>b(b.s=42100));module.exports=c})();