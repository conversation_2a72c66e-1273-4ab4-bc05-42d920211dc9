(()=>{var a={};a.id=8733,a.ids=[8733],a.modules={31:(a,b,c)=>{Promise.resolve().then(c.bind(c,21250))},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4828:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v});var d=c(60687),e=c(43210),f=c(24301),g=c(79150),h=c(24934),i=c(68988),j=c(55192),k=c(59821),l=c(37826),m=c(28559),n=c(41312),o=c(99270),p=c(41862),q=c(99891),r=c(58869),s=c(40228),t=c(19080),u=c(16189);function v(){let{user:a,profile:b}=(0,f.A)(),c=(0,u.useRouter)(),[v,w]=(0,e.useState)([]),[x,y]=(0,e.useState)(!0),[z,A]=(0,e.useState)(null),[B,C]=(0,e.useState)(""),[D,E]=(0,e.useState)({page:1,limit:20,total:0,totalPages:0}),[F,G]=(0,e.useState)(null),H=async()=>{try{y(!0),A(null);let a=new URLSearchParams({page:D.page.toString(),limit:D.limit.toString(),...B&&{search:B}}),b=await fetch(`/api/admin/users?${a}`);if(!b.ok)throw Error("Failed to fetch users");let c=await b.json();w(c.users),E(c.pagination)}catch(a){A(a.message),console.error("Error fetching users:",a)}finally{y(!1)}},I=async(a,b)=>{try{G(a);let c=await fetch("/api/admin/users",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:a,role:b})});if(!c.ok){let a=await c.json();throw Error(a.error||"Failed to update user role")}await H(),alert("User role updated successfully!")}catch(a){console.error("Error updating user role:",a),alert("Failed to update user role: "+a.message)}finally{G(null)}};return a&&b?.role==="platform_admin"?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(g.V,{}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)(h.$,{variant:"ghost",onClick:()=>c.push("/admin"),className:"mb-4",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Admin Panel"]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,d.jsx)(n.A,{className:"h-8 w-8 mr-3"}),"User Management"]}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage user accounts and permissions"})]}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)(k.E,{variant:"secondary",children:[D.total," total users"]})})]})]}),(0,d.jsx)(j.Zp,{className:"mb-6",children:(0,d.jsx)(j.Wu,{className:"pt-6",children:(0,d.jsxs)("form",{onSubmit:a=>{a.preventDefault(),E({...D,page:1}),H()},className:"flex space-x-4",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)(i.p,{type:"text",placeholder:"Search users by email or name...",value:B,onChange:a=>C(a.target.value),className:"w-full"})}),(0,d.jsxs)(h.$,{type:"submit",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Search"]})]})})}),z&&(0,d.jsx)(j.Zp,{className:"mb-6",children:(0,d.jsx)(j.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"text-center text-red-600",children:[(0,d.jsxs)("p",{children:["Error: ",z]}),(0,d.jsx)(h.$,{onClick:H,className:"mt-2",children:"Try Again"})]})})}),x&&(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(p.A,{className:"h-8 w-8 animate-spin mr-2"}),(0,d.jsx)("span",{children:"Loading users..."})]})})}),!x&&!z&&(0,d.jsxs)("div",{className:"space-y-4",children:[v.map(a=>(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center",children:["platform_admin"===a.role?(0,d.jsx)(q.A,{className:"h-5 w-5 text-purple-500 mr-2"}):(0,d.jsx)(r.A,{className:"h-5 w-5 text-blue-500 mr-2"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:a.full_name||a.email})]}),(0,d.jsx)(k.E,{className:"platform_admin"===a.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800",children:"platform_admin"===a.role?"Admin":"User"})]}),(0,d.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,d.jsxs)("p",{className:"flex items-center",children:[(0,d.jsx)("span",{className:"font-medium mr-2",children:"Email:"}),a.email]}),(0,d.jsxs)("p",{className:"flex items-center",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 mr-2"}),"Joined ",new Date(a.created_at).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]}),(0,d.jsxs)("p",{className:"flex items-center",children:[(0,d.jsx)(t.A,{className:"h-4 w-4 mr-2"}),a.projects.length," project",1!==a.projects.length?"s":""]})]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsxs)(l.lG,{children:[(0,d.jsx)(l.zM,{asChild:!0,children:(0,d.jsx)(h.$,{variant:"outline",size:"sm",children:"Change Role"})}),(0,d.jsxs)(l.Cf,{children:[(0,d.jsxs)(l.c7,{children:[(0,d.jsx)(l.L3,{children:"Change User Role"}),(0,d.jsxs)(l.rr,{children:["Update the role for ",a.email]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Current Role"}),(0,d.jsx)("p",{className:"text-sm text-gray-900 capitalize",children:a.role.replace("_"," ")})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(h.$,{onClick:()=>I(a.id,"user"),disabled:F===a.id||"user"===a.role,variant:"user"===a.role?"default":"outline",children:[F===a.id?(0,d.jsx)(p.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,d.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"User"]}),(0,d.jsxs)(h.$,{onClick:()=>I(a.id,"platform_admin"),disabled:F===a.id||"platform_admin"===a.role||a.id===b?.id,variant:"platform_admin"===a.role?"default":"outline",children:[F===a.id?(0,d.jsx)(p.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,d.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Admin"]})]}),a.id===b?.id&&(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"You cannot change your own role"})]})]})]})})]})})},a.id)),D.totalPages>1&&(0,d.jsx)(j.Zp,{children:(0,d.jsx)(j.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(D.page-1)*D.limit+1," to ",Math.min(D.page*D.limit,D.total)," of ",D.total," users"]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(h.$,{variant:"outline",onClick:()=>E({...D,page:D.page-1}),disabled:D.page<=1,children:"Previous"}),(0,d.jsx)(h.$,{variant:"outline",onClick:()=>E({...D,page:D.page+1}),disabled:D.page>=D.totalPages,children:"Next"})]})]})})})]})]})]}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(g.V,{}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Access Denied"}),(0,d.jsx)("p",{className:"text-gray-600 mt-2",children:"You need platform admin privileges to access this page."})]})})]})}},6304:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21250)),"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\admin\\users\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\admin\\users\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/users/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},10807:(a,b,c)=>{Promise.resolve().then(c.bind(c,4828))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21250:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Gautam\\\\Projects\\\\OmniPixel\\\\omnipixel-mvp\\\\app\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\admin\\users\\page.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79150:(a,b,c)=>{"use strict";c.d(b,{V:()=>u});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(24301),i=c(24934);c(43210);var j=c(26312),k=c(96241);function l({...a}){return(0,d.jsx)(j.bL,{"data-slot":"dropdown-menu",...a})}function m({...a}){return(0,d.jsx)(j.l9,{"data-slot":"dropdown-menu-trigger",...a})}function n({className:a,sideOffset:b=4,...c}){return(0,d.jsx)(j.ZL,{children:(0,d.jsx)(j.UC,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,k.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...c})})}function o({className:a,inset:b,variant:c="default",...e}){return(0,d.jsx)(j.q7,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,k.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e})}function p({className:a,...b}){return(0,d.jsx)(j.wv,{"data-slot":"dropdown-menu-separator",className:(0,k.cn)("bg-border -mx-1 my-1 h-px",a),...b})}var q=c(99891),r=c(58869),s=c(84027),t=c(40083);function u(){let{user:a,profile:b,signOut:c}=(0,h.A)(),e=(0,g.useRouter)(),j=async()=>{await c(),e.push("/login")};return a?(0,d.jsx)("nav",{className:"border-b bg-white",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between h-16",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f(),{href:"/dashboard",className:"flex-shrink-0",children:(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Omnipixel"})}),(0,d.jsxs)("div",{className:"hidden md:ml-6 md:flex md:space-x-8",children:[(0,d.jsx)(f(),{href:"/dashboard",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium",children:"Dashboard"}),b?.role==="platform_admin"&&(0,d.jsxs)(f(),{href:"/admin",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium flex items-center gap-1",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"Admin"]})]})]}),(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)(l,{children:[(0,d.jsx)(m,{asChild:!0,children:(0,d.jsx)(i.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,d.jsx)(r.A,{className:"h-4 w-4"})})}),(0,d.jsxs)(n,{className:"w-56",align:"end",forceMount:!0,children:[(0,d.jsx)("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[(0,d.jsx)("p",{className:"font-medium",children:a.email}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:b?.role==="platform_admin"?"Platform Admin":"User"})]})}),(0,d.jsx)(p,{}),(0,d.jsxs)(o,{children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Settings"})]}),b?.role==="platform_admin"&&(0,d.jsx)(o,{asChild:!0,children:(0,d.jsxs)(f(),{href:"/admin",children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Admin Panel"})]})}),(0,d.jsx)(p,{}),(0,d.jsxs)(o,{onClick:j,children:[(0,d.jsx)(t.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Log out"})]})]})]})})]})})}):null}},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},99270:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1161,2579,7660,2228,2121,5362,6813],()=>b(b.s=6304));module.exports=c})();