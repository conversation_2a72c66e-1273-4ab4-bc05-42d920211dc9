(()=>{var a={};a.id=6040,a.ids=[6040],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},27910:a=>{"use strict";a.exports=require("stream")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(34386),e=c(44999);async function f(a,b){let c=await (0,e.UL)();return a=a??"https://qrnstvofnizsgdlubtbt.supabase.co",b=b??"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFybnN0dm9mbml6c2dkbHVidGJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzE1MDMsImV4cCI6MjA2ODg0NzUwM30.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU",(0,d.createServerClient)(a,b,{cookies:{getAll:()=>c.getAll(),setAll(a){try{a.forEach(({name:a,value:b,options:d})=>c.set(a,b,d))}catch{}}}})}},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55561:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>F,patchFetch:()=>E,routeModule:()=>A,serverHooks:()=>D,workAsyncStorage:()=>B,workUnitAsyncStorage:()=>C});var d={};c.r(d),c.d(d,{DELETE:()=>z,GET:()=>x,PATCH:()=>y,POST:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(32032);async function w(a){try{let b=await (0,v.U)(),{data:{user:c},error:d}=await b.auth.getUser();if(console.log("Admin API - user:",c?.email),d||!c)return console.log("Admin API - No session or auth error"),u.NextResponse.json({error:"Unauthorized",details:d?.message||"No session"},{status:401});let{data:e,error:f}=await b.from("profiles").select("role").eq("id",c.id).single();if(console.log("Admin API - profile:",e,"profileError:",f?.message),f||!e||"platform_admin"!==e.role)return console.log("Admin API - Access denied, not admin"),u.NextResponse.json({error:"Access denied. Platform admin role required.",details:f?.message},{status:403});let{name:g,stream_project_id:h,user_email:i,config:j={}}=await a.json();if(!g||!h||!i)return u.NextResponse.json({error:"Missing required fields: name, stream_project_id, user_email"},{status:400});let{data:k,error:l}=await b.from("profiles").select("id, email").eq("email",i).single();if(l||!k)return u.NextResponse.json({error:"User not found with the provided email"},{status:404});let{data:m,error:n}=await b.from("projects").select("id").eq("stream_project_id",h).single();if(m)return u.NextResponse.json({error:"Stream Project ID is already in use"},{status:409});let{data:o,error:p}=await b.from("projects").insert([{name:g,stream_project_id:h,user_id:k.id,config:j}]).select(`
        *,
        profiles!inner(email, role)
      `).single();if(p)return console.error("Error creating project:",p),u.NextResponse.json({error:"Failed to create project"},{status:500});return u.NextResponse.json({project:o,message:`Project "${g}" created successfully for ${i}`})}catch(a){return console.error("Error in admin project creation:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(a){try{let a=await (0,v.U)(),{data:{user:b},error:c}=await a.auth.getUser();if(console.log("Admin GET API - user:",b?.email),c||!b)return console.log("Admin GET API - No session or auth error"),u.NextResponse.json({error:"Unauthorized",details:c?.message||"No session"},{status:401});let{data:d,error:e}=await a.from("profiles").select("role").eq("id",b.id).single();if(console.log("Admin GET API - profile:",d,"profileError:",e?.message),e||!d||"platform_admin"!==d.role)return console.log("Admin GET API - Access denied, not admin"),u.NextResponse.json({error:"Access denied. Platform admin role required.",details:e?.message},{status:403});let{data:f,error:g}=await a.from("projects").select(`
        *,
        profiles!inner(email, role),
        builds(id, filename, original_filename, version, status, is_current, file_size, streampixel_build_id, streampixel_status, error_message, created_at, updated_at)
      `).order("created_at",{ascending:!1});if(g)return console.error("Error fetching projects:",g),u.NextResponse.json({error:"Failed to fetch projects"},{status:500});let h=f?.map(a=>({...a,builds:a.builds?.sort((a,b)=>b.version-a.version).slice(0,5)||[]}))||[];return u.NextResponse.json({projects:h})}catch(a){return console.error("Error in admin projects fetch:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(a){try{let b=await (0,v.U)(),{data:{user:c},error:d}=await b.auth.getUser();if(d||!c)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{data:e,error:f}=await b.from("profiles").select("role").eq("id",c.id).single();if(f||!e||"platform_admin"!==e.role)return u.NextResponse.json({error:"Access denied. Platform admin role required."},{status:403});let{project_id:g,name:h,stream_project_id:i,config:j,user_email:k}=await a.json();if(!g)return u.NextResponse.json({error:"Missing required field: project_id"},{status:400});let l={};if(h&&(l.name=h),i&&(l.stream_project_id=i),void 0!==j&&(l.config=j),k){let{data:a,error:c}=await b.from("profiles").select("id").eq("email",k).single();if(c||!a)return u.NextResponse.json({error:"User not found with the provided email"},{status:400});l.user_id=a.id}if(i){let{data:a}=await b.from("projects").select("id").eq("stream_project_id",i).neq("id",g).single();if(a)return u.NextResponse.json({error:"Stream Project ID is already in use by another project"},{status:400})}let{data:m,error:n}=await b.from("projects").update(l).eq("id",g).select(`
        *,
        profiles!projects_user_id_fkey (
          email,
          role
        )
      `).single();if(n)return console.error("Error updating project:",n),u.NextResponse.json({error:"Failed to update project"},{status:500});return u.NextResponse.json({message:"Project updated successfully",project:m})}catch(a){return console.error("Error updating project:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function z(a){try{let b=await (0,v.U)(),{data:{user:c},error:d}=await b.auth.getUser();if(d||!c)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{data:e,error:f}=await b.from("profiles").select("role").eq("id",c.id).single();if(f||!e||"platform_admin"!==e.role)return u.NextResponse.json({error:"Access denied. Platform admin role required."},{status:403});let{project_id:g}=await a.json();if(!g)return u.NextResponse.json({error:"Missing required field: project_id"},{status:400});let{error:h}=await b.from("builds").delete().eq("project_id",g);if(h)return console.error("Error deleting builds:",h),u.NextResponse.json({error:"Failed to delete project builds"},{status:500});let{error:i}=await b.from("projects").delete().eq("id",g);if(i)return console.error("Error deleting project:",i),u.NextResponse.json({error:"Failed to delete project"},{status:500});return u.NextResponse.json({message:"Project deleted successfully"})}catch(a){return console.error("Error deleting project:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}let A=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/admin/projects/route",pathname:"/api/admin/projects",filename:"route",bundlePath:"app/api/admin/projects/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\api\\admin\\projects\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:B,workUnitAsyncStorage:C,serverHooks:D}=A;function E(){return(0,g.patchFetch)({workAsyncStorage:B,workUnitAsyncStorage:C})}async function F(a,b,c){var d;let e="/api/admin/projects/route";"/index"===e&&(e="/");let g=await A.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||A.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===A.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>A.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>A.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await A.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},z),b}},l=await A.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await A.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,3811,4999,6055],()=>b(b.s=55561));module.exports=c})();