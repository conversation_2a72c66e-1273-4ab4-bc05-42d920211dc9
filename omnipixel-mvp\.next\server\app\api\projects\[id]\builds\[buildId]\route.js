(()=>{var a={};a.id=9328,a.ids=[9328],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2667:(a,b,c)=>{"use strict";c.d(b,{Wf:()=>q,dr:()=>p,mD:()=>o,co:()=>n,ZP:()=>m,J3:()=>k,js:()=>s,ix:()=>r,F4:()=>l});var d=c(91043);let e=require("@aws-sdk/s3-request-presigner");function f(){return process.env.BACKBLAZE_BUCKET_NAME||"omnipixel"}function g(){return!!(process.env.BACKBLAZE_APP_KEY_ID&&process.env.BACKBLAZE_APP_KEY&&process.env.BACKBLAZE_BUCKET_NAME)}function h(a){let b=f();return`https://f005.backblazeb2.com/file/${b}/${a}`}console.log("\uD83D\uDD27 Initializing Backblaze S3 Client..."),console.log("Environment check:"),console.log("- BACKBLAZE_APP_KEY_ID:",process.env.BACKBLAZE_APP_KEY_ID?`${process.env.BACKBLAZE_APP_KEY_ID.substring(0,10)}...`:"MISSING"),console.log("- BACKBLAZE_APP_KEY:",process.env.BACKBLAZE_APP_KEY?`${process.env.BACKBLAZE_APP_KEY.substring(0,10)}...`:"MISSING"),console.log("- BACKBLAZE_BUCKET_NAME:",process.env.BACKBLAZE_BUCKET_NAME||"MISSING");let i=function(){console.log("\uD83D\uDD27 Creating Backblaze S3 Client...");let a=process.env.BACKBLAZE_APP_KEY_ID,b=process.env.BACKBLAZE_APP_KEY;console.log("Credentials check:"),console.log("- BACKBLAZE_APP_KEY_ID:",a?`${a.substring(0,15)}...`:"MISSING"),console.log("- BACKBLAZE_APP_KEY:",b?`${b.substring(0,15)}...`:"MISSING");let c="https://s3.us-east-005.backblazeb2.com";if(console.log("Configuration:"),console.log("- Endpoint:",c),console.log("- Region: us-east-005"),console.log("- Force Path Style: true"),!a||!b)throw console.error("❌ Backblaze credentials missing!"),Error("Backblaze credentials not configured");let e=new d.S3Client({region:"us-east-005",endpoint:c,credentials:{accessKeyId:a,secretAccessKey:b},forcePathStyle:!0});return console.log("✅ Backblaze S3 Client created successfully"),e}();console.log("✅ Backblaze S3 Client created successfully");let j=f();function k(a,b,c){let d=Date.now(),e=c.replace(/[^a-zA-Z0-9.-]/g,"_");return`uploads/${a}/${b}/${d}_${e}`}async function l(a,b,c="application/octet-stream",e){if(console.log("\uD83D\uDE80 Starting uploadFileToS3..."),console.log("Parameters:"),console.log("- key:",a),console.log("- file size:",b.length,"bytes"),console.log("- contentType:",c),console.log("- metadata:",e),!g())throw console.error("❌ Backblaze not configured!"),console.error("Environment variables:"),console.error("- BACKBLAZE_APP_KEY_ID:",process.env.BACKBLAZE_APP_KEY_ID?"SET":"MISSING"),console.error("- BACKBLAZE_APP_KEY:",process.env.BACKBLAZE_APP_KEY?"SET":"MISSING"),console.error("- BACKBLAZE_BUCKET_NAME:",process.env.BACKBLAZE_BUCKET_NAME?"SET":"MISSING"),Error("Backblaze credentials not configured");console.log("✅ Backblaze configuration check passed");let f={};e&&(e.projectId&&(f["project-id"]=e.projectId),e.userId&&(f["user-id"]=e.userId),e.filename&&(f.filename=e.filename),e.version&&(f.version=e.version),void 0!==e.streamPixelProcess&&(f["streampixel-process"]=e.streamPixelProcess?"true":"false"),f["uploaded-at"]=new Date().toISOString()),console.log("\uD83D\uDCE6 Creating PutObjectCommand with:"),console.log("- Bucket:",j),console.log("- Key:",a),console.log("- ContentType:",c),console.log("- Metadata:",f);let k=new d.PutObjectCommand({Bucket:j,Key:a,Body:b,ContentType:c,Metadata:Object.keys(f).length>0?f:void 0});console.log("\uD83D\uDCE1 Sending command to Backblaze B2..."),console.log("S3 Client config:",{region:i.config.region,endpoint:i.config.endpoint});try{let b=await i.send(k);console.log("✅ Upload successful! Result:",b),console.log("File uploaded to Backblaze B2 with metadata:",f);let c=h(a);return console.log("\uD83D\uDD17 Generated public URL:",c),c}catch(b){console.error("❌ Upload failed with error:",b);let a={};throw b&&"object"==typeof b&&(a.name=b.name,a.message=b.message,a.code=b.Code,a.statusCode=b.$metadata?.httpStatusCode,a.requestId=b.$metadata?.requestId,a.fault=b.$fault,a.region=b.$metadata?.region),console.error("Error details:",a),"AccessDenied"===a.code&&(console.error("\uD83D\uDD0D AccessDenied debugging:"),console.error("- Check if App Key has writeFiles permission"),console.error("- Check if App Key is scoped to the correct bucket"),console.error("- Verify bucket name matches exactly"),console.error("- Current bucket name:",j),console.error("- App Key ID:",process.env.BACKBLAZE_APP_KEY_ID?.substring(0,10)+"...")),b}}async function m(a,b,c,f,h=3600){if(!g())throw Error("Backblaze credentials not configured");if(c&&f){let b=new d.UploadPartCommand({Bucket:j,Key:a,UploadId:c,PartNumber:f});return await (0,e.getSignedUrl)(i,b,{expiresIn:h})}let k=new d.PutObjectCommand({Bucket:j,Key:a,ContentType:b});return await (0,e.getSignedUrl)(i,k,{expiresIn:h})}async function n(a){if(!g())throw Error("Backblaze credentials not configured");let b=new d.DeleteObjectCommand({Bucket:j,Key:a});await i.send(b)}async function o(a,b="application/octet-stream"){if(!g())throw Error("Backblaze credentials not configured");let c=new d.CreateMultipartUploadCommand({Bucket:j,Key:a,ContentType:b}),e=await i.send(c);if(!e.UploadId)throw Error("Failed to create multipart upload");return{uploadId:e.UploadId}}async function p(a,b,c){if(!g())throw Error("Backblaze credentials not configured");let e=new d.CompleteMultipartUploadCommand({Bucket:j,Key:a,UploadId:b,MultipartUpload:{Parts:c.sort((a,b)=>a.PartNumber-b.PartNumber)}});return(await i.send(e)).Location||h(a)}async function q(a,b){if(!g())throw Error("Backblaze credentials not configured");let c=new d.AbortMultipartUploadCommand({Bucket:j,Key:a,UploadId:b});await i.send(c)}function r(){return g()}function s(a){return h(a)}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},26806:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>E,patchFetch:()=>D,routeModule:()=>z,serverHooks:()=>C,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>B});var d={};c.r(d),c.d(d,{DELETE:()=>x,PATCH:()=>y});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(2667),w=c(32032);async function x(a,{params:b}){try{let a=await (0,w.U)(),{data:{user:c},error:d}=await a.auth.getUser();if(d||!c)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{id:e,buildId:f}=await b,{data:g,error:h}=await a.from("profiles").select("role").eq("id",c.id).single();if(h)return u.NextResponse.json({error:"Failed to verify user profile"},{status:500});if(g?.role!=="platform_admin"){let{data:b,error:d}=await a.from("projects").select("id").eq("id",e).eq("user_id",c.id).single();if(d||!b)return u.NextResponse.json({error:"Project not found or access denied"},{status:404})}let{data:i,error:j}=await a.from("builds").select("*").eq("id",f).eq("project_id",e).single();if(j||!i)return u.NextResponse.json({error:"Build not found"},{status:404});let{data:k,error:l}=await a.from("builds").select("id").eq("project_id",e).neq("status","failed");if(l)return u.NextResponse.json({error:"Failed to check build count"},{status:500});try{i.s3_key&&await (0,v.co)(i.s3_key)}catch(a){console.error("Error deleting from Backblaze B2:",a)}let{error:m}=await a.from("builds").delete().eq("id",f);if(m)return console.error("Error deleting build from database:",m),u.NextResponse.json({error:"Failed to delete build"},{status:500});if(i.is_current&&k.length>1){let{data:b,error:c}=await a.from("builds").select("*").eq("project_id",e).neq("status","failed").order("version",{ascending:!1}).limit(1);!c&&b&&b.length>0&&await a.from("builds").update({is_current:!0,status:"active",updated_at:new Date().toISOString()}).eq("id",b[0].id)}return u.NextResponse.json({message:"Build deleted successfully",deletedBuild:{id:i.id,filename:i.filename,version:i.version}})}catch(a){return console.error("Error deleting build:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(a,{params:b}){try{let c,{id:d,buildId:e}=await b,f=await (0,w.U)(),{data:{user:g},error:h}=await f.auth.getUser();if(h||!g)return console.error("Authentication error:",h),u.NextResponse.json({error:"Unauthorized"},{status:401});console.log("Authenticated user:",g.id);let{data:i,error:j}=await f.from("profiles").select("role").eq("id",g.id).single();if(j)return u.NextResponse.json({error:"Failed to verify user profile"},{status:500});c=i?.role==="platform_admin"?f.from("projects").select("*").eq("id",d):f.from("projects").select("*").eq("id",d).eq("user_id",g.id);let{data:k,error:l}=await c.single();if(l||!k)return u.NextResponse.json({error:"Project not found or access denied"},{status:404});console.log("Looking for build:",e,"in project:",d);let{data:m,error:n}=await f.from("builds").select("*").eq("id",e).eq("project_id",d).single();if(n)return console.error("Error finding build:",n),u.NextResponse.json({error:`Build not found: ${n.message}`},{status:404});if(!m)return console.error("Build not found - no data returned"),u.NextResponse.json({error:"Build not found"},{status:404});if(console.log("Found build:",{id:m.id,project_id:m.project_id,filename:m.filename,is_current:m.is_current,status:m.status}),m.is_current)return u.NextResponse.json({error:"This build is already active"},{status:400});console.log("Updating build:",e,"in project:",d),console.log("Using regular client for build update");let{data:o,error:p}=await f.from("builds").select("*").eq("id",e).eq("project_id",d);if(console.log("Build exists check:",{exists:o,existsError:p,buildId:e,projectId:d}),p)return console.error("Error checking if build exists:",p),u.NextResponse.json({error:"Failed to verify build exists",details:p.message},{status:500});if(!o||0===o.length)return console.error("Build not found:",{buildId:e,projectId:d}),u.NextResponse.json({error:"Build not found"},{status:404});let{data:q,error:r}=await f.from("builds").update({is_current:!1,status:"processing",updated_at:new Date().toISOString()}).eq("id",e).eq("project_id",d).select();if(r)return console.error("Error activating build:",r),u.NextResponse.json({error:"Failed to activate build",details:r.message},{status:500});if(console.log("Updated builds:",q),!q||0===q.length)return console.error("No builds were updated. Build might not exist or user might not have permission."),u.NextResponse.json({error:"Build not found or permission denied"},{status:404});let s=q[0];console.log("Build updated successfully:",s);try{let b=await fetch(`${a.url.split("/api")[0]}/api/streampixel/upload`,{method:"POST",headers:{"Content-Type":"application/json",Cookie:a.headers.get("Cookie")||""},body:JSON.stringify({buildId:e,projectId:d})});if(!b.ok){let a=await b.json();return console.error("StreamPixel upload failed:",a),await f.from("builds").update({error_message:a.error||"StreamPixel upload failed",updated_at:new Date().toISOString()}).eq("id",e),u.NextResponse.json({message:"Build activated successfully, but StreamPixel upload failed",build:s,streamPixelError:a.error})}let c=await b.json();return u.NextResponse.json({message:"Build activated and uploaded to StreamPixel successfully",build:s,streamPixelResponse:c})}catch(b){console.error("Error calling StreamPixel API:",b);let a=b instanceof Error?b.message:"Unknown error";return await f.from("builds").update({error_message:"Failed to upload to StreamPixel: "+a,updated_at:new Date().toISOString()}).eq("id",e),u.NextResponse.json({message:"Build activated successfully, but StreamPixel upload failed",build:s,streamPixelError:a})}}catch(a){return console.error("Error activating build:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}let z=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/projects/[id]/builds/[buildId]/route",pathname:"/api/projects/[id]/builds/[buildId]",filename:"route",bundlePath:"app/api/projects/[id]/builds/[buildId]/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\api\\projects\\[id]\\builds\\[buildId]\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:A,workUnitAsyncStorage:B,serverHooks:C}=z;function D(){return(0,g.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:B})}async function E(a,b,c){var d;let e="/api/projects/[id]/builds/[buildId]/route";"/index"===e&&(e="/");let g=await z.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||z.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===z.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>z.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>z.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await z.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await z.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await z.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},27910:a=>{"use strict";a.exports=require("stream")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(34386),e=c(44999);async function f(a,b){let c=await (0,e.UL)();return a=a??"https://qrnstvofnizsgdlubtbt.supabase.co",b=b??"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFybnN0dm9mbml6c2dkbHVidGJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzE1MDMsImV4cCI6MjA2ODg0NzUwM30.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU",(0,d.createServerClient)(a,b,{cookies:{getAll:()=>c.getAll(),setAll(a){try{a.forEach(({name:a,value:b,options:d})=>c.set(a,b,d))}catch{}}}})}},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91043:a=>{"use strict";a.exports=require("@aws-sdk/client-s3")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,3811,4999,6055],()=>b(b.s=26806));module.exports=c})();