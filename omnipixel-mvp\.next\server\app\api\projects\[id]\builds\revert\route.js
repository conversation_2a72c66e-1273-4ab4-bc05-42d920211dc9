(()=>{var a={};a.id=643,a.ids=[643],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},26719:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{GET:()=>x,POST:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(32032);async function w(a,{params:b}){try{let c=await (0,v.U)(),{data:{user:d},error:e}=await c.auth.getUser();if(e||!d)return u.NextResponse.json({error:"Unauthorized"},{status:401});let f=b.id,{data:g,error:h}=await c.from("profiles").select("role").eq("id",d.id).single();if(h)return u.NextResponse.json({error:"Failed to verify user profile"},{status:500});if(g?.role!=="platform_admin"){let{data:a,error:b}=await c.from("projects").select("id").eq("id",f).eq("user_id",d.id).single();if(b||!a)return u.NextResponse.json({error:"Project not found or access denied"},{status:404})}let{build_id:i}=await a.json();if(!i)return u.NextResponse.json({error:"Missing required field: build_id"},{status:400});let{data:j,error:k}=await c.from("builds").select("*").eq("id",i).eq("project_id",f).single();if(k||!j)return u.NextResponse.json({error:"Build not found"},{status:404});if(j.is_current)return u.NextResponse.json({error:"This build is already the current active build"},{status:400});if("failed"===j.status)return u.NextResponse.json({error:"Cannot revert to a failed build"},{status:400});let{error:l}=await c.from("builds").update({is_current:!1,status:"archived",updated_at:new Date().toISOString()}).eq("project_id",f).eq("is_current",!0);if(l)return console.error("Error unsetting current build:",l),u.NextResponse.json({error:"Failed to unset current build"},{status:500});let{data:m,error:n}=await c.from("builds").update({is_current:!0,status:"active",updated_at:new Date().toISOString()}).eq("id",i).select().single();if(n)return console.error("Error setting build as current:",n),u.NextResponse.json({error:"Failed to set build as current"},{status:500});let{data:o,error:p}=await c.from("projects").select(`
        *,
        builds (
          id,
          filename,
          s3_key,
          version,
          status,
          is_current,
          file_size,
          streampixel_build_id,
          streampixel_status,
          error_message,
          created_at,
          updated_at
        )
      `).eq("id",f).single();return p&&console.error("Error fetching updated project:",p),u.NextResponse.json({message:`Successfully reverted to version ${j.version}`,build:m,project:o||null})}catch(a){return console.error("Error reverting build:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(a,{params:b}){try{let a=await (0,v.U)(),{data:{user:c},error:d}=await a.auth.getUser();if(d||!c)return u.NextResponse.json({error:"Unauthorized"},{status:401});let e=b.id,{data:f,error:g}=await a.from("profiles").select("role").eq("id",c.id).single();if(g)return u.NextResponse.json({error:"Failed to verify user profile"},{status:500});if(f?.role!=="platform_admin"){let{data:b,error:d}=await a.from("projects").select("id").eq("id",e).eq("user_id",c.id).single();if(d||!b)return u.NextResponse.json({error:"Project not found or access denied"},{status:404})}let{data:h,error:i}=await a.from("builds").select("*").eq("project_id",e).eq("is_current",!1).neq("status","failed").order("version",{ascending:!1});if(i)return console.error("Error fetching revertable builds:",i),u.NextResponse.json({error:"Failed to fetch build history"},{status:500});let{data:j,error:k}=await a.from("builds").select("*").eq("project_id",e).eq("is_current",!0).single();return k&&console.error("Error fetching current build:",k),u.NextResponse.json({revertableBuilds:h||[],currentBuild:j||null,canRevert:(h?.length||0)>0})}catch(a){return console.error("Error fetching build revert options:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/projects/[id]/builds/revert/route",pathname:"/api/projects/[id]/builds/revert",filename:"route",bundlePath:"app/api/projects/[id]/builds/revert/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\api\\projects\\[id]\\builds\\revert\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/projects/[id]/builds/revert/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},27910:a=>{"use strict";a.exports=require("stream")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(34386),e=c(44999);async function f(a,b){let c=await (0,e.UL)();return a=a??"https://qrnstvofnizsgdlubtbt.supabase.co",b=b??"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFybnN0dm9mbml6c2dkbHVidGJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzE1MDMsImV4cCI6MjA2ODg0NzUwM30.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU",(0,d.createServerClient)(a,b,{cookies:{getAll:()=>c.getAll(),setAll(a){try{a.forEach(({name:a,value:b,options:d})=>c.set(a,b,d))}catch{}}}})}},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,3811,4999,6055],()=>b(b.s=26719));module.exports=c})();