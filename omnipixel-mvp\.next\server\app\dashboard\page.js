(()=>{var a={};a.id=5105,a.ids=[5105],a.modules={163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(71042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8840:(a,b,c)=>{"use strict";c.d(b,{default:()=>F});var d=c(60687),e=c(43210),f=c(79150),g=c(85814),h=c.n(g),i=c(55192),j=c(24934),k=c(84027),l=c(97840),m=c(16023);let n=Symbol.for("constructDateFrom");function o(a,b){return"function"==typeof a?a(b):a&&"object"==typeof a&&n in a?a[n](b):a instanceof Date?new a.constructor(b):new Date(b)}let p={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function q(a){return (b={})=>{let c=b.width?String(b.width):a.defaultWidth;return a.formats[c]||a.formats[a.defaultWidth]}}let r={date:q({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:q({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:q({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},s={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function t(a){return(b,c)=>{let d;if("formatting"===(c?.context?String(c.context):"standalone")&&a.formattingValues){let b=a.defaultFormattingWidth||a.defaultWidth,e=c?.width?String(c.width):b;d=a.formattingValues[e]||a.formattingValues[b]}else{let b=a.defaultWidth,e=c?.width?String(c.width):a.defaultWidth;d=a.values[e]||a.values[b]}return d[a.argumentCallback?a.argumentCallback(b):b]}}function u(a){return(b,c={})=>{let d,e=c.width,f=e&&a.matchPatterns[e]||a.matchPatterns[a.defaultMatchWidth],g=b.match(f);if(!g)return null;let h=g[0],i=e&&a.parsePatterns[e]||a.parsePatterns[a.defaultParseWidth],j=Array.isArray(i)?function(a,b){for(let c=0;c<a.length;c++)if(b(a[c]))return c}(i,a=>a.test(h)):function(a,b){for(let c in a)if(Object.prototype.hasOwnProperty.call(a,c)&&b(a[c]))return c}(i,a=>a.test(h));return d=a.valueCallback?a.valueCallback(j):j,{value:d=c.valueCallback?c.valueCallback(d):d,rest:b.slice(h.length)}}}let v={code:"en-US",formatDistance:(a,b,c)=>{let d,e=p[a];if(d="string"==typeof e?e:1===b?e.one:e.other.replace("{{count}}",b.toString()),c?.addSuffix)if(c.comparison&&c.comparison>0)return"in "+d;else return d+" ago";return d},formatLong:r,formatRelative:(a,b,c,d)=>s[a],localize:{ordinalNumber:(a,b)=>{let c=Number(a),d=c%100;if(d>20||d<10)switch(d%10){case 1:return c+"st";case 2:return c+"nd";case 3:return c+"rd"}return c+"th"},era:t({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:t({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:a=>a-1}),month:t({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:t({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:t({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(a){return(b,c={})=>{let d=b.match(a.matchPattern);if(!d)return null;let e=d[0],f=b.match(a.parsePattern);if(!f)return null;let g=a.valueCallback?a.valueCallback(f[0]):f[0];return{value:g=c.valueCallback?c.valueCallback(g):g,rest:b.slice(e.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:a=>parseInt(a,10)}),era:u({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:u({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:a=>a+1}),month:u({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:u({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:u({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},w={};function x(a,b){return o(b||a,a)}function y(a){let b=x(a),c=new Date(Date.UTC(b.getFullYear(),b.getMonth(),b.getDate(),b.getHours(),b.getMinutes(),b.getSeconds(),b.getMilliseconds()));return c.setUTCFullYear(b.getFullYear()),a-c}function z(a,...b){let c=o.bind(null,a||b.find(a=>"object"==typeof a));return b.map(c)}function A(a,b){let c=x(a)-x(b);return c<0?-1:c>0?1:c}function B({project:a}){var b,c;let e=a.builds?.filter(a=>!["failed","archived"].includes(a.status||""))||[],f=e[0],g=e.length;return(0,d.jsxs)(i.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,d.jsx)(i.aR,{children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.ZB,{className:"text-lg",children:a.name}),(0,d.jsxs)(i.BT,{children:["Stream ID: ",a.stream_project_id]})]}),(0,d.jsx)("div",{className:"flex gap-2",children:(0,d.jsx)(j.$,{variant:"outline",size:"sm",asChild:!0,children:(0,d.jsxs)(h(),{href:`/projects/${a.id}`,children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-1"}),"Manage"]})})})]})}),(0,d.jsx)(i.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,d.jsxs)("span",{children:[g," build",1!==g?"s":""]}),f&&(0,d.jsxs)("span",{children:["Last updated ",(b=new Date(f.created_at),c={addSuffix:!0},function(a,b,c){let d,e=c?.locale??w.locale??v,f=A(a,b);if(isNaN(f))throw RangeError("Invalid time value");let g=Object.assign({},c,{addSuffix:c?.addSuffix,comparison:f}),[h,i]=z(c?.in,...f>0?[b,a]:[a,b]),j=function(a,b,c){var d;return(d=void 0,a=>{let b=(d?Math[d]:Math.trunc)(a);return 0===b?0:b})((x(a)-x(b))/1e3)}(i,h),k=Math.round((j-(y(i)-y(h))/1e3)/60);if(k<2)if(c?.includeSeconds)if(j<5)return e.formatDistance("lessThanXSeconds",5,g);else if(j<10)return e.formatDistance("lessThanXSeconds",10,g);else if(j<20)return e.formatDistance("lessThanXSeconds",20,g);else if(j<40)return e.formatDistance("halfAMinute",0,g);else if(j<60)return e.formatDistance("lessThanXMinutes",1,g);else return e.formatDistance("xMinutes",1,g);else if(0===k)return e.formatDistance("lessThanXMinutes",1,g);else return e.formatDistance("xMinutes",k,g);if(k<45)return e.formatDistance("xMinutes",k,g);if(k<90)return e.formatDistance("aboutXHours",1,g);if(k<1440){let a=Math.round(k/60);return e.formatDistance("aboutXHours",a,g)}if(k<2520)return e.formatDistance("xDays",1,g);else if(k<43200){let a=Math.round(k/1440);return e.formatDistance("xDays",a,g)}else if(k<86400)return d=Math.round(k/43200),e.formatDistance("aboutXMonths",d,g);if((d=function(a,b,c){let[d,e,f]=z(void 0,a,a,b),g=A(e,f),h=Math.abs(function(a,b,c){let[d,e]=z(void 0,a,b);return 12*(d.getFullYear()-e.getFullYear())+(d.getMonth()-e.getMonth())}(e,f));if(h<1)return 0;1===e.getMonth()&&e.getDate()>27&&e.setDate(30),e.setMonth(e.getMonth()-g*h);let i=A(e,f)===-g;(function(a,b){let c=x(a,void 0);return+function(a,b){let c=x(a,b?.in);return c.setHours(23,59,59,999),c}(c,void 0)==+function(a,b){let c=x(a,b?.in),d=c.getMonth();return c.setFullYear(c.getFullYear(),d+1,0),c.setHours(23,59,59,999),c}(c,b)})(d)&&1===h&&1===A(d,f)&&(i=!1);let j=g*(h-i);return 0===j?0:j}(i,h))<12){let a=Math.round(k/43200);return e.formatDistance("xMonths",a,g)}{let a=d%12,b=Math.trunc(d/12);return a<3?e.formatDistance("aboutXYears",b,g):a<9?e.formatDistance("overXYears",b,g):e.formatDistance("almostXYears",b+1,g)}}(b,o(b,Date.now()),c))]})]}),f&&(0,d.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-sm",children:"Latest Build"}),(0,d.jsx)("p",{className:"text-xs text-gray-600",children:f.original_filename||f.filename}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["Version ",f.version]})]}),(0,d.jsx)(j.$,{size:"sm",asChild:!0,children:(0,d.jsxs)(h(),{href:`/projects/${a.id}/stream`,children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-1"}),"Stream"]})})]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(j.$,{variant:"outline",className:"flex-1",asChild:!0,children:(0,d.jsxs)(h(),{href:`/projects/${a.id}`,children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Upload Build"]})}),f&&(0,d.jsx)(j.$,{className:"flex-1",asChild:!0,children:(0,d.jsxs)(h(),{href:`/projects/${a.id}/stream`,children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-1"}),"Stream"]})})]})]})})]})}var C=c(68988),D=c(96474),E=c(99270);function F({user:a,profile:b,projects:c}){let[g,h]=(0,e.useState)(""),k=b?.role==="platform_admin",l=(0,e.useMemo)(()=>c.filter(a=>a.name?.toLowerCase().includes(g.toLowerCase())||a.stream_project_id?.toLowerCase().includes(g.toLowerCase())),[c,g]);return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(f.V,{}),(0,d.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,d.jsxs)("p",{className:"text-gray-600 mt-1",children:["Welcome back, ",a.email,k&&(0,d.jsx)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:"Platform Admin"})]})]}),k&&(0,d.jsxs)(j.$,{onClick:()=>window.location.href="/admin",children:[(0,d.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Admin Panel"]})]})}),(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsxs)("div",{className:"relative max-w-md",children:[(0,d.jsx)(E.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,d.jsx)(C.p,{placeholder:"Search projects...",value:g,onChange:a=>h(a.target.value),className:"pl-10"})]})}),0===l.length?(0,d.jsx)(i.Zp,{children:(0,d.jsx)(i.Wu,{className:"py-12",children:(0,d.jsx)("div",{className:"text-center",children:0===c.length?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No projects assigned"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:k?"Use the Admin Panel to create projects for users.":"Contact your administrator to get projects assigned to your account."}),k&&(0,d.jsxs)(j.$,{onClick:()=>window.location.href="/admin",children:[(0,d.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Go to Admin Panel"]})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No projects found"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search terms."})]})})})}):(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:l.map(a=>(0,d.jsx)(B,{project:a},a.id))})]})]})}},10650:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Gautam\\\\Projects\\\\OmniPixel\\\\omnipixel-mvp\\\\app\\\\dashboard\\\\client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\dashboard\\client.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},16023:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24301:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>h,A:()=>i});var d=c(60687),e=c(43210);let f=(0,c(59522).createBrowserClient)("https://qrnstvofnizsgdlubtbt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU"),g=(0,e.createContext)(void 0);function h({children:a}){let[b,c]=(0,e.useState)(null),[h,i]=(0,e.useState)(null),[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(!0),n=async()=>{await f.auth.signOut()};return(0,d.jsx)(g.Provider,{value:{user:b,profile:h,session:j,loading:l,signOut:n},children:a})}function i(){let a=(0,e.useContext)(g);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(96241);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32032:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(34386),e=c(44999);async function f(a,b){let c=await (0,e.UL)();return a=a??"https://qrnstvofnizsgdlubtbt.supabase.co",b=b??"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU",(0,d.createServerClient)(a,b,{cookies:{getAll:()=>c.getAll(),setAll(a){try{a.forEach(({name:a,value:b,options:d})=>c.set(a,b,d))}catch{}}}})}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},39916:(a,b,c)=>{"use strict";var d=c(97576);c.o(d,"notFound")&&c.d(b,{notFound:function(){return d.notFound}}),c.o(d,"redirect")&&c.d(b,{redirect:function(){return d.redirect}})},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41837:(a,b,c)=>{Promise.resolve().then(c.bind(c,10650))},46055:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},47990:()=>{},48976:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},50588:(a,b,c)=>{Promise.resolve().then(c.bind(c,24301))},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(96241);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(37413),e=c(260),f=c.n(e),g=c(73298),h=c.n(g);c(82704);var i=c(90648);let j={title:"Omnipixel",description:"Omnipixel - Serverless project dashboard and streaming platform"};function k({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsx)(i.AuthProvider,{children:a})})})}},59116:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,64118)),"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\app\\dashboard\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},62765:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(37413),e=c(39916),f=c(10650),g=c(32032);async function h(){let a=await (0,g.U)(),{data:{user:b}}=await a.auth.getUser();b||(0,e.redirect)("/login");let{data:c}=await a.from("profiles").select("*").eq("id",b.id).single(),h=[];if(c?.role==="platform_admin"){let{data:b}=await a.from("projects").select(`
        *,
        builds (
          id,
          filename,
          original_filename,
          version,
          created_at,
          status,
          is_current
        )
      `).order("created_at",{ascending:!1});h=b||[]}else{let{data:c}=await a.from("projects").select(`
        *,
        builds (
          id,
          filename,
          original_filename,
          version,
          created_at,
          status,
          is_current
        )
      `).eq("user_id",b.id).order("created_at",{ascending:!1});h=c||[]}return(0,d.jsx)(f.default,{user:b,profile:c,projects:h})}},66311:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(96241);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},70899:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},71042:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(68388),e=c(52637),f=c(51846),g=c(31162),h=c(84971),i=c(98479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},74075:a=>{"use strict";a.exports=require("zlib")},79150:(a,b,c)=>{"use strict";c.d(b,{V:()=>u});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(24301),i=c(24934);c(43210);var j=c(26312),k=c(96241);function l({...a}){return(0,d.jsx)(j.bL,{"data-slot":"dropdown-menu",...a})}function m({...a}){return(0,d.jsx)(j.l9,{"data-slot":"dropdown-menu-trigger",...a})}function n({className:a,sideOffset:b=4,...c}){return(0,d.jsx)(j.ZL,{children:(0,d.jsx)(j.UC,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,k.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...c})})}function o({className:a,inset:b,variant:c="default",...e}){return(0,d.jsx)(j.q7,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,k.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e})}function p({className:a,...b}){return(0,d.jsx)(j.wv,{"data-slot":"dropdown-menu-separator",className:(0,k.cn)("bg-border -mx-1 my-1 h-px",a),...b})}var q=c(99891),r=c(58869),s=c(84027),t=c(40083);function u(){let{user:a,profile:b,signOut:c}=(0,h.A)(),e=(0,g.useRouter)(),j=async()=>{await c(),e.push("/login")};return a?(0,d.jsx)("nav",{className:"border-b bg-white",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between h-16",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f(),{href:"/dashboard",className:"flex-shrink-0",children:(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Omnipixel"})}),(0,d.jsxs)("div",{className:"hidden md:ml-6 md:flex md:space-x-8",children:[(0,d.jsx)(f(),{href:"/dashboard",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium",children:"Dashboard"}),b?.role==="platform_admin"&&(0,d.jsxs)(f(),{href:"/admin",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium flex items-center gap-1",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"Admin"]})]})]}),(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)(l,{children:[(0,d.jsx)(m,{asChild:!0,children:(0,d.jsx)(i.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,d.jsx)(r.A,{className:"h-4 w-4"})})}),(0,d.jsxs)(n,{className:"w-56",align:"end",forceMount:!0,children:[(0,d.jsx)("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[(0,d.jsx)("p",{className:"font-medium",children:a.email}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:b?.role==="platform_admin"?"Platform Admin":"User"})]})}),(0,d.jsx)(p,{}),(0,d.jsxs)(o,{children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Settings"})]}),b?.role==="platform_admin"&&(0,d.jsx)(o,{asChild:!0,children:(0,d.jsxs)(f(),{href:"/admin",children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Admin Panel"})]})}),(0,d.jsx)(p,{}),(0,d.jsxs)(o,{onClick:j,children:[(0,d.jsx)(t.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Log out"})]})]})]})})]})})}):null}},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},82704:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86897:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(52836),e=c(49026),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},89101:(a,b,c)=>{Promise.resolve().then(c.bind(c,8840))},90648:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\lib\\auth-context.tsx","AuthProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Gautam\\Projects\\OmniPixel\\omnipixel-mvp\\lib\\auth-context.tsx","useAuth")},91645:a=>{"use strict";a.exports=require("net")},92444:(a,b,c)=>{Promise.resolve().then(c.bind(c,90648))},94735:a=>{"use strict";a.exports=require("events")},96241:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96575:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},97576:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(86897),e=c(49026),f=c(62765),g=c(48976),h=c(70899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},97840:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},99270:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,3811,4999,1161,2579,7660,2228,2121],()=>b(b.s=59116));module.exports=c})();