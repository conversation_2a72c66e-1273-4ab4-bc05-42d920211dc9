"use strict";exports.id=1634,exports.ids=[1634],exports.modules={39390:(a,b,c)=>{c.d(b,{J:()=>g});var d=c(60687);c(43210);var e=c(78148),f=c(96241);function g({className:a,...b}){return(0,d.jsx)(e.b,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}},42902:(a,b,c)=>{c.d(b,{d:()=>h});var d=c(60687),e=c(43210),f=c(90270),g=c(96241);let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.bL,{className:(0,g.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...b,ref:c,children:(0,d.jsx)(f.zi,{className:(0,g.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));h.displayName=f.bL.displayName},91634:(a,b,c)=>{c.d(b,{g:()=>F});var d=c(60687),e=c(43210),f=c(24934),g=c(55192),h=c(68988),i=c(39390),j=c(42902),k=c(97822),l=c(78272),m=c(3589),n=c(13964),o=c(96241);let p=k.bL;k.YJ;let q=k.WT,r=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k.l9,{ref:e,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(k.In,{asChild:!0,children:(0,d.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));r.displayName=k.l9.displayName;let s=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(k.PP,{ref:c,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(m.A,{className:"h-4 w-4"})}));s.displayName=k.PP.displayName;let t=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(k.wn,{ref:c,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(l.A,{className:"h-4 w-4"})}));t.displayName=k.wn.displayName;let u=e.forwardRef(({className:a,children:b,position:c="popper",...e},f)=>(0,d.jsx)(k.ZL,{children:(0,d.jsxs)(k.UC,{ref:f,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(s,{}),(0,d.jsx)(k.LM,{className:(0,o.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(t,{})]})}));u.displayName=k.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(k.JU,{ref:c,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=k.JU.displayName;let v=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k.q7,{ref:e,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(k.VF,{children:(0,d.jsx)(n.A,{className:"h-4 w-4"})})}),(0,d.jsx)(k.p4,{children:b})]}));v.displayName=k.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(k.wv,{ref:c,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=k.wv.displayName;var w=c(55146);let x=w.bL,y=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(w.B8,{ref:c,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...b}));y.displayName=w.B8.displayName;let z=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(w.l9,{ref:c,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...b}));z.displayName=w.l9.displayName;let A=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(w.UC,{ref:c,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...b}));A.displayName=w.UC.displayName;var B=c(84027),C=c(13943),D=c(41862),E=c(8819);function F({projectId:a,currentConfig:b,onConfigUpdate:c,isAdmin:k=!1,className:l=""}){let[m,n]=(0,e.useState)(!1),[o,s]=(0,e.useState)(null),[t,w]=(0,e.useState)({autoConnect:!1,touchInput:!0,keyBoardInput:!0,resolutionMode:"Dynamic Resolution Mode",maxStreamQuality:"1080p (1920x1080)",primaryCodec:"H264",fallBackCodec:"VP8",isPasswordProtected:!1,password:"",loadingMessage:"Loading stream...",connectingMessage:"Connecting to stream...",disconnectedMessage:"Stream disconnected",reconnectingMessage:"Reconnecting...",errorMessage:"Stream error occurred",connectButtonText:"Connect to Stream",...b}),F=async()=>{try{n(!0),s(null);let b=k?"/api/admin/projects":`/api/projects/${a}`,d=k?{project_id:a,config:t}:{config:t},e=await fetch(b,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)});if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to save configuration")}c?.(t),alert("Configuration saved successfully!")}catch(a){s(a.message),console.error("Error saving config:",a)}finally{n(!1)}},G=(a,b)=>{w(c=>({...c,[a]:b}))};return(0,d.jsxs)(g.Zp,{className:l,children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)(g.ZB,{className:"flex items-center",children:[(0,d.jsx)(B.A,{className:"h-5 w-5 mr-2"}),"Stream Configuration"]}),(0,d.jsx)(g.BT,{children:"Configure stream settings, security, and user interface messages"})]}),(0,d.jsxs)(g.Wu,{children:[o&&(0,d.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,d.jsx)("p",{className:"text-sm text-red-600",children:o})}),(0,d.jsxs)(x,{defaultValue:"stream",className:"space-y-4",children:[(0,d.jsxs)(y,{className:"grid w-full grid-cols-3",children:[(0,d.jsx)(z,{value:"stream",children:"Stream Settings"}),(0,d.jsx)(z,{value:"security",children:"Security"}),(0,d.jsx)(z,{value:"messages",children:"Messages"})]}),(0,d.jsx)(A,{value:"stream",className:"space-y-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"autoConnect",children:"Auto Connect"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(j.d,{id:"autoConnect",checked:t.autoConnect,onCheckedChange:a=>G("autoConnect",a)}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:t.autoConnect?"Automatically connect on load":"Show connect button"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"touchInput",children:"Touch Input"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(j.d,{id:"touchInput",checked:t.touchInput,onCheckedChange:a=>G("touchInput",a)}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Enable touch controls"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"keyBoardInput",children:"Keyboard Input"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(j.d,{id:"keyBoardInput",checked:t.keyBoardInput,onCheckedChange:a=>G("keyBoardInput",a)}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Enable keyboard controls"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"resolutionMode",children:"Resolution Mode"}),(0,d.jsxs)(p,{value:t.resolutionMode,onValueChange:a=>G("resolutionMode",a),children:[(0,d.jsx)(r,{children:(0,d.jsx)(q,{})}),(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{value:"Dynamic Resolution Mode",children:"Dynamic Resolution"}),(0,d.jsx)(v,{value:"Fixed Resolution Mode",children:"Fixed Resolution"}),(0,d.jsx)(v,{value:"Adaptive Resolution Mode",children:"Adaptive Resolution"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"maxStreamQuality",children:"Max Stream Quality"}),(0,d.jsxs)(p,{value:t.maxStreamQuality,onValueChange:a=>G("maxStreamQuality",a),children:[(0,d.jsx)(r,{children:(0,d.jsx)(q,{})}),(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{value:"4K (3840x2160)",children:"4K (3840x2160)"}),(0,d.jsx)(v,{value:"1440p (2560x1440)",children:"1440p (2560x1440)"}),(0,d.jsx)(v,{value:"1080p (1920x1080)",children:"1080p (1920x1080)"}),(0,d.jsx)(v,{value:"720p (1280x720)",children:"720p (1280x720)"}),(0,d.jsx)(v,{value:"480p (854x480)",children:"480p (854x480)"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"primaryCodec",children:"Primary Codec"}),(0,d.jsxs)(p,{value:t.primaryCodec,onValueChange:a=>G("primaryCodec",a),children:[(0,d.jsx)(r,{children:(0,d.jsx)(q,{})}),(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{value:"H264",children:"H264"}),(0,d.jsx)(v,{value:"H265",children:"H265"}),(0,d.jsx)(v,{value:"VP8",children:"VP8"}),(0,d.jsx)(v,{value:"VP9",children:"VP9"}),(0,d.jsx)(v,{value:"AV1",children:"AV1"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"fallBackCodec",children:"Fallback Codec"}),(0,d.jsxs)(p,{value:t.fallBackCodec,onValueChange:a=>G("fallBackCodec",a),children:[(0,d.jsx)(r,{children:(0,d.jsx)(q,{})}),(0,d.jsxs)(u,{children:[(0,d.jsx)(v,{value:"H264",children:"H264"}),(0,d.jsx)(v,{value:"VP8",children:"VP8"}),(0,d.jsx)(v,{value:"VP9",children:"VP9"})]})]})]})]})}),(0,d.jsx)(A,{value:"security",className:"space-y-4",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"isPasswordProtected",children:"Password Protection"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(j.d,{id:"isPasswordProtected",checked:t.isPasswordProtected,onCheckedChange:a=>G("isPasswordProtected",a)}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:t.isPasswordProtected?"Stream is password protected":"Stream is public"})]})]}),t.isPasswordProtected&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"password",children:"Stream Password"}),(0,d.jsx)(h.p,{id:"password",type:"password",value:t.password,onChange:a=>G("password",a.target.value),placeholder:"Enter stream password"}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:"Users will need this password to access the stream"})]})]})}),(0,d.jsx)(A,{value:"messages",className:"space-y-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"loadingMessage",children:"Loading Message"}),(0,d.jsx)(h.p,{id:"loadingMessage",value:t.loadingMessage,onChange:a=>G("loadingMessage",a.target.value),placeholder:"Loading stream..."})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"connectingMessage",children:"Connecting Message"}),(0,d.jsx)(h.p,{id:"connectingMessage",value:t.connectingMessage,onChange:a=>G("connectingMessage",a.target.value),placeholder:"Connecting to stream..."})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"disconnectedMessage",children:"Disconnected Message"}),(0,d.jsx)(h.p,{id:"disconnectedMessage",value:t.disconnectedMessage,onChange:a=>G("disconnectedMessage",a.target.value),placeholder:"Stream disconnected"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"reconnectingMessage",children:"Reconnecting Message"}),(0,d.jsx)(h.p,{id:"reconnectingMessage",value:t.reconnectingMessage,onChange:a=>G("reconnectingMessage",a.target.value),placeholder:"Reconnecting..."})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"errorMessage",children:"Error Message"}),(0,d.jsx)(h.p,{id:"errorMessage",value:t.errorMessage,onChange:a=>G("errorMessage",a.target.value),placeholder:"Stream error occurred"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(i.J,{htmlFor:"connectButtonText",children:"Connect Button Text"}),(0,d.jsx)(h.p,{id:"connectButtonText",value:t.connectButtonText,onChange:a=>G("connectButtonText",a.target.value),placeholder:"Connect to Stream"})]})]})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t",children:[(0,d.jsxs)(f.$,{variant:"outline",onClick:()=>{w({autoConnect:!1,touchInput:!0,keyBoardInput:!0,resolutionMode:"Dynamic Resolution Mode",maxStreamQuality:"1080p (1920x1080)",primaryCodec:"H264",fallBackCodec:"VP8",isPasswordProtected:!1,password:"",loadingMessage:"Loading stream...",connectingMessage:"Connecting to stream...",disconnectedMessage:"Stream disconnected",reconnectingMessage:"Reconnecting...",errorMessage:"Stream error occurred",connectButtonText:"Connect to Stream",...b}),s(null)},disabled:m,children:[(0,d.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Reset"]}),(0,d.jsx)(f.$,{onClick:F,disabled:m,children:m?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(D.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Save Configuration"]})})]})]})]})}}};