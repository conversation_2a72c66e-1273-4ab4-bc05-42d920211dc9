"use strict";exports.id=2228,exports.ids=[2228],exports.modules={43:(a,b,c)=>{c.d(b,{jH:()=>f});var d=c(43210);c(60687);var e=d.createContext(void 0);function f(a){let b=d.useContext(e);return a||b||"ltr"}},9510:(a,b,c)=>{c.d(b,{N:()=>i});var d=c(43210),e=c(11273),f=c(98599),g=c(8730),h=c(60687);function i(a){let b=a+"CollectionProvider",[c,i]=(0,e.A)(b),[j,k]=c(b,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:b,children:c}=a,e=d.useRef(null),f=d.useRef(new Map).current;return(0,h.jsx)(j,{scope:b,itemMap:f,collectionRef:e,children:c})};l.displayName=b;let m=a+"CollectionSlot",n=(0,g.TL)(m),o=d.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=k(m,c),g=(0,f.s)(b,e.collectionRef);return(0,h.jsx)(n,{ref:g,children:d})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,g.TL)(p),s=d.forwardRef((a,b)=>{let{scope:c,children:e,...g}=a,i=d.useRef(null),j=(0,f.s)(b,i),l=k(p,c);return d.useEffect(()=>(l.itemMap.set(i,{ref:i,...g}),()=>void l.itemMap.delete(i))),(0,h.jsx)(r,{...{[q]:""},ref:j,children:e})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(b){let c=k(a+"CollectionConsumer",b);return d.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},i]}var j=new WeakMap;function k(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=l(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function l(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],j.set(this,!0)}set(a,b){return j.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=l(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],m=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||k[a-1]!==b||(m=!0);let c=k[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=k(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=k(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return k(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}})},18853:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43210),e=c(66156);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},55509:(a,b,c)=>{c.d(b,{Mz:()=>a1,i3:()=>a3,UC:()=>a2,bL:()=>a0,Bk:()=>aM});var d=c(43210);let e=["top","right","bottom","left"],f=Math.min,g=Math.max,h=Math.round,i=Math.floor,j=a=>({x:a,y:a}),k={left:"right",right:"left",bottom:"top",top:"bottom"},l={start:"end",end:"start"};function m(a,b){return"function"==typeof a?a(b):a}function n(a){return a.split("-")[0]}function o(a){return a.split("-")[1]}function p(a){return"x"===a?"y":"x"}function q(a){return"y"===a?"height":"width"}let r=new Set(["top","bottom"]);function s(a){return r.has(n(a))?"y":"x"}function t(a){return a.replace(/start|end/g,a=>l[a])}let u=["left","right"],v=["right","left"],w=["top","bottom"],x=["bottom","top"];function y(a){return a.replace(/left|right|bottom|top/g,a=>k[a])}function z(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function A(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function B(a,b,c){let d,{reference:e,floating:f}=a,g=s(b),h=p(s(b)),i=q(h),j=n(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,r=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(o(b)){case"start":d[h]-=r*(c&&k?-1:1);break;case"end":d[h]+=r*(c&&k?-1:1)}return d}let C=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=B(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=B(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function D(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:n=!1,padding:o=0}=m(b,a),p=z(o),q=h[n?"floating"===l?"reference":"floating":l],r=A(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(q)))||c?q:q.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),s="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,t=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),u=await (null==f.isElement?void 0:f.isElement(t))&&await (null==f.getScale?void 0:f.getScale(t))||{x:1,y:1},v=A(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:s,offsetParent:t,strategy:i}):s);return{top:(r.top-v.top+p.top)/u.y,bottom:(v.bottom-r.bottom+p.bottom)/u.y,left:(r.left-v.left+p.left)/u.x,right:(v.right-r.right+p.right)/u.x}}function E(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function F(a){return e.some(b=>a[b]>=0)}let G=new Set(["left","top"]);async function H(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=n(c),h=o(c),i="y"===s(c),j=G.has(g)?-1:1,k=f&&i?-1:1,l=m(b,a),{mainAxis:p,crossAxis:q,alignmentAxis:r}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof r&&(q="end"===h?-1*r:r),i?{x:q*k,y:p*j}:{x:p*j,y:q*k}}function I(){return"undefined"!=typeof window}function J(a){return M(a)?(a.nodeName||"").toLowerCase():"#document"}function K(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function L(a){var b;return null==(b=(M(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function M(a){return!!I()&&(a instanceof Node||a instanceof K(a).Node)}function N(a){return!!I()&&(a instanceof Element||a instanceof K(a).Element)}function O(a){return!!I()&&(a instanceof HTMLElement||a instanceof K(a).HTMLElement)}function P(a){return!!I()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof K(a).ShadowRoot)}let Q=new Set(["inline","contents"]);function R(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aa(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!Q.has(e)}let S=new Set(["table","td","th"]),T=[":popover-open",":modal"];function U(a){return T.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let V=["transform","translate","scale","rotate","perspective"],W=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function Y(a){let b=Z(),c=N(a)?aa(a):a;return V.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||W.some(a=>(c.willChange||"").includes(a))||X.some(a=>(c.contain||"").includes(a))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let $=new Set(["html","body","#document"]);function _(a){return $.has(J(a))}function aa(a){return K(a).getComputedStyle(a)}function ab(a){return N(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function ac(a){if("html"===J(a))return a;let b=a.assignedSlot||a.parentNode||P(a)&&a.host||L(a);return P(b)?b.host:b}function ad(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=ac(b);return _(c)?b.ownerDocument?b.ownerDocument.body:b.body:O(c)&&R(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=K(e);if(f){let a=ae(g);return b.concat(g,g.visualViewport||[],R(e)?e:[],a&&c?ad(a):[])}return b.concat(e,ad(e,[],c))}function ae(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function af(a){let b=aa(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=O(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,i=h(c)!==f||h(d)!==g;return i&&(c=f,d=g),{width:c,height:d,$:i}}function ag(a){return N(a)?a:a.contextElement}function ah(a){let b=ag(a);if(!O(b))return j(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=af(b),g=(f?h(c.width):c.width)/d,i=(f?h(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),i&&Number.isFinite(i)||(i=1),{x:g,y:i}}let ai=j(0);function aj(a){let b=K(a);return Z()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:ai}function ak(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=ag(a),h=j(1);b&&(d?N(d)&&(h=ah(d)):h=ah(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===K(g))&&e)?aj(g):j(0),k=(f.left+i.x)/h.x,l=(f.top+i.y)/h.y,m=f.width/h.x,n=f.height/h.y;if(g){let a=K(g),b=d&&N(d)?K(d):d,c=a,e=ae(c);for(;e&&d&&b!==c;){let a=ah(e),b=e.getBoundingClientRect(),d=aa(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;k*=a.x,l*=a.y,m*=a.x,n*=a.y,k+=f,l+=g,e=ae(c=K(e))}}return A({width:m,height:n,x:k,y:l})}function al(a,b){let c=ab(a).scrollLeft;return b?b.left+c:ak(L(a)).left+c}function am(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:al(a,d)),y:d.top+b.scrollTop}}let an=new Set(["absolute","fixed"]);function ao(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=K(a),d=L(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=Z();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=L(a),c=ab(a),d=a.ownerDocument.body,e=g(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=g(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),h=-c.scrollLeft+al(a),i=-c.scrollTop;return"rtl"===aa(d).direction&&(h+=g(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:h,y:i}}(L(a));else if(N(b))d=function(a,b){let c=ak(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=O(a)?ah(a):j(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aj(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return A(d)}function ap(a){return"static"===aa(a).position}function aq(a,b){if(!O(a)||"fixed"===aa(a).position)return null;if(b)return b(a);let c=a.offsetParent;return L(a)===c&&(c=c.ownerDocument.body),c}function ar(a,b){var c;let d=K(a);if(U(a))return d;if(!O(a)){let b=ac(a);for(;b&&!_(b);){if(N(b)&&!ap(b))return b;b=ac(b)}return d}let e=aq(a,b);for(;e&&(c=e,S.has(J(c)))&&ap(e);)e=aq(e,b);return e&&_(e)&&ap(e)&&!Y(e)?d:e||function(a){let b=ac(a);for(;O(b)&&!_(b);){if(Y(b))return b;if(U(b))break;b=ac(b)}return null}(a)||d}let as=async function(a){let b=this.getOffsetParent||ar,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=O(b),e=L(b),f="fixed"===c,g=ak(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=j(0);if(d||!d&&!f)if(("body"!==J(b)||R(e))&&(h=ab(b)),d){let a=ak(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=al(e));f&&!d&&e&&(i.x=al(e));let k=!e||d||f?j(0):am(e,h);return{x:g.left+h.scrollLeft-i.x-k.x,y:g.top+h.scrollTop-i.y-k.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},at={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=L(d),h=!!b&&U(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},k=j(1),l=j(0),m=O(d);if((m||!m&&!f)&&(("body"!==J(d)||R(g))&&(i=ab(d)),O(d))){let a=ak(d);k=ah(d),l.x=a.x+d.clientLeft,l.y=a.y+d.clientTop}let n=!g||m||f?j(0):am(g,i,!0);return{width:c.width*k.x,height:c.height*k.y,x:c.x*k.x-i.scrollLeft*k.x+l.x+n.x,y:c.y*k.y-i.scrollTop*k.y+l.y+n.y}},getDocumentElement:L,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,h=[..."clippingAncestors"===c?U(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=ad(a,[],!1).filter(a=>N(a)&&"body"!==J(a)),e=null,f="fixed"===aa(a).position,g=f?ac(a):a;for(;N(g)&&!_(g);){let b=aa(g),c=Y(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&an.has(e.position)||R(g)&&!c&&function a(b,c){let d=ac(b);return!(d===c||!N(d)||_(d))&&("fixed"===aa(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=ac(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],i=h[0],j=h.reduce((a,c)=>{let d=ao(b,c,e);return a.top=g(d.top,a.top),a.right=f(d.right,a.right),a.bottom=f(d.bottom,a.bottom),a.left=g(d.left,a.left),a},ao(b,i,e));return{width:j.right-j.left,height:j.bottom-j.top,x:j.left,y:j.top}},getOffsetParent:ar,getElementRects:as,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=af(a);return{width:b,height:c}},getScale:ah,isElement:N,isRTL:function(a){return"rtl"===aa(a).direction}};function au(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let av=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:h,platform:i,elements:j,middlewareData:k}=b,{element:l,padding:n=0}=m(a,b)||{};if(null==l)return{};let r=z(n),t={x:c,y:d},u=p(s(e)),v=q(u),w=await i.getDimensions(l),x="y"===u,y=x?"clientHeight":"clientWidth",A=h.reference[v]+h.reference[u]-t[u]-h.floating[v],B=t[u]-h.reference[u],C=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l)),D=C?C[y]:0;D&&await (null==i.isElement?void 0:i.isElement(C))||(D=j.floating[y]||h.floating[v]);let E=D/2-w[v]/2-1,F=f(r[x?"top":"left"],E),G=f(r[x?"bottom":"right"],E),H=D-w[v]-G,I=D/2-w[v]/2+(A/2-B/2),J=g(F,f(I,H)),K=!k.arrow&&null!=o(e)&&I!==J&&h.reference[v]/2-(I<F?F:G)-w[v]/2<0,L=K?I<F?I-F:I-H:0;return{[u]:t[u]+L,data:{[u]:J,centerOffset:I-J-L,...K&&{alignmentOffset:L}},reset:K}}});var aw=c(51215),ax="undefined"!=typeof document?d.useLayoutEffect:function(){};function ay(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!ay(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!ay(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function az(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function aA(a,b){let c=az(a);return Math.round(b*c)/c}function aB(a){let b=d.useRef(a);return ax(()=>{b.current=a}),b}var aC=c(14163),aD=c(60687),aE=d.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,aD.jsx)(aC.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,aD.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aE.displayName="Arrow";var aF=c(98599),aG=c(11273),aH=c(13495),aI=c(66156),aJ=c(18853),aK="Popper",[aL,aM]=(0,aG.A)(aK),[aN,aO]=aL(aK),aP=a=>{let{__scopePopper:b,children:c}=a,[e,f]=d.useState(null);return(0,aD.jsx)(aN,{scope:b,anchor:e,onAnchorChange:f,children:c})};aP.displayName=aK;var aQ="PopperAnchor",aR=d.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:e,...f}=a,g=aO(aQ,c),h=d.useRef(null),i=(0,aF.s)(b,h);return d.useEffect(()=>{g.onAnchorChange(e?.current||h.current)}),e?null:(0,aD.jsx)(aC.sG.div,{...f,ref:i})});aR.displayName=aQ;var aS="PopperContent",[aT,aU]=aL(aS),aV=d.forwardRef((a,b)=>{let{__scopePopper:c,side:e="bottom",sideOffset:h=0,align:j="center",alignOffset:k=0,arrowPadding:l=0,avoidCollisions:r=!0,collisionBoundary:z=[],collisionPadding:A=0,sticky:B="partial",hideWhenDetached:I=!1,updatePositionStrategy:J="optimized",onPlaced:K,...M}=a,N=aO(aS,c),[O,P]=d.useState(null),Q=(0,aF.s)(b,a=>P(a)),[R,S]=d.useState(null),T=(0,aJ.X)(R),U=T?.width??0,V=T?.height??0,W="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},X=Array.isArray(z)?z:[z],Y=X.length>0,Z={padding:W,boundary:X.filter(aZ),altBoundary:Y},{refs:$,floatingStyles:_,placement:aa,isPositioned:ab,middlewareData:ac}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=d.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=d.useState(e);ay(n,e)||o(e);let[p,q]=d.useState(null),[r,s]=d.useState(null),t=d.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=d.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=d.useRef(null),y=d.useRef(null),z=d.useRef(l),A=null!=j,B=aB(j),D=aB(f),E=aB(k),F=d.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:at,...c},f={...e.platform,_c:d};return C(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!ay(z.current,b)&&(z.current=b,aw.flushSync(()=>{m(b)}))})},[n,b,c,D,E]);ax(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let G=d.useRef(!1);ax(()=>(G.current=!0,()=>{G.current=!1}),[]),ax(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,F);F()}},[v,w,F,B,A]);let H=d.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),I=d.useMemo(()=>({reference:v,floating:w}),[v,w]),J=d.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=aA(I.floating,l.x),d=aA(I.floating,l.y);return i?{...a,transform:"translate("+b+"px, "+d+"px)",...az(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,i,I.floating,l.x,l.y]);return d.useMemo(()=>({...l,update:F,refs:H,elements:I,floatingStyles:J}),[l,F,H,I,J])}({strategy:"fixed",placement:e+("center"!==j?"-"+j:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:h=!0,ancestorResize:j=!0,elementResize:k="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:m=!1}=d,n=ag(a),o=h||j?[...n?ad(n):[],...ad(b)]:[];o.forEach(a=>{h&&a.addEventListener("scroll",c,{passive:!0}),j&&a.addEventListener("resize",c)});let p=n&&l?function(a,b){let c,d=null,e=L(a);function h(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function j(k,l){void 0===k&&(k=!1),void 0===l&&(l=1),h();let m=a.getBoundingClientRect(),{left:n,top:o,width:p,height:q}=m;if(k||b(),!p||!q)return;let r=i(o),s=i(e.clientWidth-(n+p)),t={rootMargin:-r+"px "+-s+"px "+-i(e.clientHeight-(o+q))+"px "+-i(n)+"px",threshold:g(0,f(1,l))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==l){if(!u)return j();d?j(!1,d):c=setTimeout(()=>{j(!1,1e-7)},1e3)}1!==d||au(m,a.getBoundingClientRect())||j(),u=!1}try{d=new IntersectionObserver(v,{...t,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(v,t)}d.observe(a)}(!0),h}(n,c):null,q=-1,r=null;k&&(r=new ResizeObserver(a=>{let[d]=a;d&&d.target===n&&r&&(r.unobserve(b),cancelAnimationFrame(q),q=requestAnimationFrame(()=>{var a;null==(a=r)||a.observe(b)})),c()}),n&&!m&&r.observe(n),r.observe(b));let s=m?ak(a):null;return m&&function b(){let d=ak(a);s&&!au(s,d)&&c(),s=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;o.forEach(a=>{h&&a.removeEventListener("scroll",c),j&&a.removeEventListener("resize",c)}),null==p||p(),null==(a=r)||a.disconnect(),r=null,m&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===J}),elements:{reference:N.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await H(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,void 0]}))({mainAxis:h+V,alignmentAxis:k}),r&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:h=!0,crossAxis:i=!1,limiter:j={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...k}=m(a,b),l={x:c,y:d},o=await D(b,k),q=s(n(e)),r=p(q),t=l[r],u=l[q];if(h){let a="y"===r?"top":"left",b="y"===r?"bottom":"right",c=t+o[a],d=t-o[b];t=g(c,f(t,d))}if(i){let a="y"===q?"top":"left",b="y"===q?"bottom":"right",c=u+o[a],d=u-o[b];u=g(c,f(u,d))}let v=j.fn({...b,[r]:t,[q]:u});return{...v,data:{x:v.x-c,y:v.y-d,enabled:{[r]:h,[q]:i}}}}}}(a),options:[a,void 0]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===B?{...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=m(a,b),k={x:c,y:d},l=s(e),o=p(l),q=k[o],r=k[l],t=m(h,b),u="number"==typeof t?{mainAxis:t,crossAxis:0}:{mainAxis:0,crossAxis:0,...t};if(i){let a="y"===o?"height":"width",b=f.reference[o]-f.floating[a]+u.mainAxis,c=f.reference[o]+f.reference[a]-u.mainAxis;q<b?q=b:q>c&&(q=c)}if(j){var v,w;let a="y"===o?"width":"height",b=G.has(n(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(v=g.offset)?void 0:v[l])||0)+(b?0:u.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(w=g.offset)?void 0:w[l])||0)-(b?u.crossAxis:0);r<c?r=c:r>d&&(r=d)}return{[o]:q,[l]:r}}}}(void 0),options:[void 0,void 0]}:void 0,...Z}),r&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:r}=b,{mainAxis:z=!0,crossAxis:A=!0,fallbackPlacements:B,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:F=!0,...G}=m(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let H=n(h),I=s(k),J=n(k)===k,K=await (null==l.isRTL?void 0:l.isRTL(r.floating)),L=B||(J||!F?[y(k)]:function(a){let b=y(a);return[t(a),b,t(b)]}(k)),M="none"!==E;!B&&M&&L.push(...function(a,b,c,d){let e=o(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?v:u;return b?u:v;case"left":case"right":return b?w:x;default:return[]}}(n(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(t)))),f}(k,F,E,K));let N=[k,...L],O=await D(b,G),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(z&&P.push(O[H]),A){let a=function(a,b,c){void 0===c&&(c=!1);let d=o(a),e=p(s(a)),f=q(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=y(g)),[g,y(g)]}(h,j,K);P.push(O[a[0]],O[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=N[a];if(b&&("alignment"!==A||I===s(b)||Q.every(a=>a.overflows[0]>0&&s(a.placement)===I)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(C){case"bestFit":{let a=null==(g=Q.filter(a=>{if(M){let b=s(a.placement);return b===I||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,void 0]}))({...Z}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,h,{placement:i,rects:j,platform:k,elements:l}=b,{apply:p=()=>{},...q}=m(a,b),r=await D(b,q),t=n(i),u=o(i),v="y"===s(i),{width:w,height:x}=j.floating;"top"===t||"bottom"===t?(e=t,h=u===(await (null==k.isRTL?void 0:k.isRTL(l.floating))?"start":"end")?"left":"right"):(h=t,e="end"===u?"top":"bottom");let y=x-r.top-r.bottom,z=w-r.left-r.right,A=f(x-r[e],y),B=f(w-r[h],z),C=!b.middlewareData.shift,E=A,F=B;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(F=z),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(E=y),C&&!u){let a=g(r.left,0),b=g(r.right,0),c=g(r.top,0),d=g(r.bottom,0);v?F=w-2*(0!==a||0!==b?a+b:g(r.left,r.right)):E=x-2*(0!==c||0!==d?c+d:g(r.top,r.bottom))}await p({...b,availableWidth:F,availableHeight:E});let G=await k.getDimensions(l.floating);return w!==G.width||x!==G.height?{reset:{rects:!0}}:{}}}}(a),options:[a,void 0]}))({...Z,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),R&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?av({element:c.current,padding:d}).fn(b):{}:c?av({element:c,padding:d}).fn(b):{}}}))(a),options:[a,void 0]}))({element:R,padding:l}),a$({arrowWidth:U,arrowHeight:V}),I&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=m(a,b);switch(d){case"referenceHidden":{let a=E(await D(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:F(a)}}}case"escaped":{let a=E(await D(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:F(a)}}}default:return{}}}}}(a),options:[a,void 0]}))({strategy:"referenceHidden",...Z})]}),[ae,af]=a_(aa),ah=(0,aH.c)(K);(0,aI.N)(()=>{ab&&ah?.()},[ab,ah]);let ai=ac.arrow?.x,aj=ac.arrow?.y,al=ac.arrow?.centerOffset!==0,[am,an]=d.useState();return(0,aI.N)(()=>{O&&an(window.getComputedStyle(O).zIndex)},[O]),(0,aD.jsx)("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{..._,transform:ab?_.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:am,"--radix-popper-transform-origin":[ac.transformOrigin?.x,ac.transformOrigin?.y].join(" "),...ac.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,aD.jsx)(aT,{scope:c,placedSide:ae,onArrowChange:S,arrowX:ai,arrowY:aj,shouldHideArrow:al,children:(0,aD.jsx)(aC.sG.div,{"data-side":ae,"data-align":af,...M,ref:Q,style:{...M.style,animation:ab?void 0:"none"}})})})});aV.displayName=aS;var aW="PopperArrow",aX={top:"bottom",right:"left",bottom:"top",left:"right"},aY=d.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=aU(aW,c),f=aX[e.placedSide];return(0,aD.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,aD.jsx)(aE,{...d,ref:b,style:{...d.style,display:"block"}})})});function aZ(a){return null!==a}aY.displayName=aW;var a$=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=a_(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function a_(a){let[b,c="center"]=a.split("-");return[b,c]}var a0=aP,a1=aR,a2=aV,a3=aY},72942:(a,b,c)=>{c.d(b,{RG:()=>v,bL:()=>E,q7:()=>F});var d=c(43210),e=c(70569),f=c(9510),g=c(98599),h=c(11273),i=c(96963),j=c(14163),k=c(13495),l=c(65551),m=c(43),n=c(60687),o="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},q="RovingFocusGroup",[r,s,t]=(0,f.N)(q),[u,v]=(0,h.A)(q,[t]),[w,x]=u(q),y=d.forwardRef((a,b)=>(0,n.jsx)(r.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(r.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(z,{...a,ref:b})})}));y.displayName=q;var z=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:h=!1,dir:i,currentTabStopId:r,defaultCurrentTabStopId:t,onCurrentTabStopIdChange:u,onEntryFocus:v,preventScrollOnEntryFocus:x=!1,...y}=a,z=d.useRef(null),A=(0,g.s)(b,z),B=(0,m.jH)(i),[C,E]=(0,l.i)({prop:r,defaultProp:t??null,onChange:u,caller:q}),[F,G]=d.useState(!1),H=(0,k.c)(v),I=s(c),J=d.useRef(!1),[K,L]=d.useState(0);return d.useEffect(()=>{let a=z.current;if(a)return a.addEventListener(o,H),()=>a.removeEventListener(o,H)},[H]),(0,n.jsx)(w,{scope:c,orientation:f,dir:B,loop:h,currentTabStopId:C,onItemFocus:d.useCallback(a=>E(a),[E]),onItemShiftTab:d.useCallback(()=>G(!0),[]),onFocusableItemAdd:d.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>L(a=>a-1),[]),children:(0,n.jsx)(j.sG.div,{tabIndex:F||0===K?-1:0,"data-orientation":f,...y,ref:A,style:{outline:"none",...a.style},onMouseDown:(0,e.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,e.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!F){let b=new CustomEvent(o,p);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);D([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),x)}}J.current=!1}),onBlur:(0,e.m)(a.onBlur,()=>G(!1))})})}),A="RovingFocusGroupItem",B=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:k,...l}=a,m=(0,i.B)(),o=h||m,p=x(A,c),q=p.currentTabStopId===o,t=s(c),{onFocusableItemAdd:u,onFocusableItemRemove:v,currentTabStopId:w}=p;return d.useEffect(()=>{if(f)return u(),()=>v()},[f,u,v]),(0,n.jsx)(r.ItemSlot,{scope:c,id:o,focusable:f,active:g,children:(0,n.jsx)(j.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...l,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f?p.onItemFocus(o):a.preventDefault()}),onFocus:(0,e.m)(a.onFocus,()=>p.onItemFocus(o)),onKeyDown:(0,e.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return C[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=t().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>D(c))}}),children:"function"==typeof k?k({isCurrentTabStop:q,hasTabStop:null!=w}):k})})});B.displayName=A;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var E=y,F=B},84027:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}};