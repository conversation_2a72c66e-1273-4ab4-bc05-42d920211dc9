"use strict";exports.id=2477,exports.ids=[2477],exports.modules={82477:(a,b,c)=>{c.d(b,{StreamPlayer:()=>w});var d=c(60687),e=c(43210),f=c(52348),g=c(24934),h=c(55192),i=c(59821),j=c(68988),k=c(37826),l=c(64021),m=c(59582),n=c(41862),o=c(93613),p=c(97840),q=c(23385),r=c(89925),s=c(91391),t=c(17740),u=c(80375),v=c(25334);function w({projectId:a,buildId:b,className:c="",config:w,showControls:x=!0,showHeader:y=!0,showEmbedButton:z=!0,width:A=1280,height:B=720,isEmbedded:C=!1,enableIframeComms:D=!1}){let[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(!1),[I,J]=(0,e.useState)(!1),[K,L]=(0,e.useState)(null),[M,N]=(0,e.useState)(null),[O,P]=(0,e.useState)(null),[Q,R]=(0,e.useState)(!1),[S,T]=(0,e.useState)(null),[U,V]=(0,e.useState)("disconnected"),[W,X]=(0,e.useState)("Loading stream..."),[Y,Z]=(0,e.useState)(!1),[$,_]=(0,e.useState)(!1),[aa,ab]=(0,e.useState)(""),[ac,ad]=(0,e.useState)(!1),[ae,af]=(0,e.useState)(!1),ag=(0,e.useRef)(null),ah=(0,e.useRef)(null),ai=(0,e.useRef)(null),aj=(0,e.useRef)(null),ak=(0,e.useRef)(null),al=(0,e.useRef)(!1),am=(0,e.useRef)(!1),an={loading:M?.loadingMessage??"Loading stream...",connecting:M?.connectingMessage??"Connecting to stream...",disconnected:M?.disconnectedMessage??"Stream disconnected",reconnecting:M?.reconnectingMessage??"Reconnecting...",error:M?.errorMessage??"Stream error occurred",connectButton:M?.connectButtonText??"Connect to Stream"},ao=async()=>{if(!am.current){am.current=!0,F(!0),T(null);try{let a=K&&M?{AutoConnect:!0,appId:K,touchInput:M?.touchInput??!0,keyBoardInput:M?.keyBoardInput??!0,resolutionMode:M?.resolutionMode??"Dynamic Resolution Mode",maxStreamQuality:M?.maxStreamQuality??"1080p (1920x1080)",primaryCodec:M?.primaryCodec??"H264",fallBackCodec:M?.fallBackCodec??"VP8"}:null;if(!a)throw Error("Cannot create stream config: missing required data");let{appStream:b,pixelStreaming:c,UIControl:d}=await (0,f.ZN)(a);ai.current=b,aj.current=c,ak.current=d,ah.current&&b.rootElement&&(b.rootElement.parentNode&&b.rootElement.parentNode.removeChild(b.rootElement),ah.current.innerHTML="",ah.current.appendChild(b.rootElement)),ar(),al.current=!0,F(!1)}catch(a){T(a.message||"Failed to initialize stream"),F(!1)}finally{am.current=!1}}},ap=()=>{try{ai.current?.stream?.disconnect&&ai.current.stream.disconnect(),aj.current?.disconnect&&aj.current.disconnect(),ah.current&&(ah.current.innerHTML="")}catch(a){}ai.current=null,aj.current=null,ak.current=null,al.current=!1,am.current=!1},aq=(a,b={})=>{},ar=()=>{ai.current&&(ai.current.onConnectAction=()=>{console.log("\uD83D\uDD17 StreamPixel: Connect action triggered"),V("connecting"),F(!0),Z(!1),X(M?.connectingMessage??"Connecting to stream..."),aq("connect-started")},ai.current.onWebRtcConnecting=()=>{console.log("\uD83C\uDF10 StreamPixel: WebRTC connecting"),V("connecting"),X(M?.connectingMessage??"Establishing connection..."),aq("webrtc-connecting")},ai.current.onWebRtcConnected=()=>{console.log("✅ StreamPixel: WebRTC connected"),V("connected"),X("Initializing video stream..."),aq("webrtc-connected")},ai.current.onVideoInitialized=()=>{console.log("\uD83C\uDFA5 StreamPixel: Video initialized and ready"),console.log("\uD83D\uDD04 Setting loading state to false - video ready"),F(!1),Z(!0),V("connected"),H(!0),aq("video-initialized")},ai.current.onDisconnect=()=>{console.log("\uD83D\uDD0C StreamPixel: Disconnected"),H(!1),F(!1),Z(!1),V("disconnected"),X(M?.disconnectedMessage??"Stream disconnected"),aq("disconnected"),requestAnimationFrame(()=>{ap()})},ai.current.onDataChannelOpen&&(ai.current.onDataChannelOpen=()=>{console.log("\uD83D\uDCE1 StreamPixel: Data channel opened"),aq("data-channel-open")}),ai.current.onDataChannelMessage&&(ai.current.onDataChannelMessage=a=>{console.log("\uD83D\uDCE8 StreamPixel: Data channel message",a),aq("data-channel-message",{message:a})}),ai.current.onUnrealMessage&&(ai.current.onUnrealMessage=a=>{console.log("\uD83C\uDFAE Unreal Engine message:",a),aq("unreal-message",{message:a})}))},as=async()=>{console.log("▶️ Play button clicked"),aq("play-requested"),console.log("\uD83D\uDD04 Setting loading state to true"),F(!0),Z(!1),X(M?.loadingMessage??"Initializing stream..."),ap(),await ao(),V("connecting"),H(!0)},at=()=>{console.log("⏸️ Pause button clicked"),aq("pause-requested"),aj.current?.disconnect(),ai.current?.stream?.disconnect(),requestAnimationFrame(()=>{H(!1),V("disconnected")}),ap(),window.location.reload()},au=()=>{console.log("\uD83D\uDD07 Mute button clicked"),ak.current&&(ak.current.toggleAudio(),J(!I),aq("mute-toggled",{isMuted:!I}))},av=()=>{ag.current&&(Q?document.exitFullscreen?.():ag.current.requestFullscreen?.(),R(!Q))},aw=()=>{aa===M?.password?(ad(!0),_(!1)):T("Invalid password")},ax=()=>{let b=new URLSearchParams;x||b.set("hideControls","true"),y||b.set("hideHeader","true"),z||b.set("hideEmbedButton","true"),M?.autoConnect&&b.set("autoConnect","true");let c=b.toString(),d=`https://your-domain.com/embed/${a}${c?`?${c}`:""}`;return`<!-- OmniPixel Interactive Stream Embed -->
<iframe
  src="${d}"
  width="${A}"
  height="${B}"
  frameborder="0"
  allowfullscreen
  allow="camera; microphone; fullscreen"
  style="border: none; border-radius: 8px;">
</iframe>

<!-- Optional: Listen for iframe events -->
<script>
window.addEventListener('message', function(event) {
  if (event.data?.type?.startsWith('omnipixel-')) {
    console.log('Stream event:', event.data.type, event.data);

    // Handle specific events
    switch(event.data.type) {
      case 'omnipixel-status-change':
        console.log('Stream status:', event.data.streamStatus);
        break;
      case 'omnipixel-webrtc-connected':
        console.log('Stream connected successfully');
        break;
      case 'omnipixel-unreal-message':
        console.log('Unreal Engine message:', event.data.message);
        break;
    }
  }
});

// Optional: Send commands to the stream
function connectStream() {
  document.querySelector('iframe').contentWindow.postMessage({
    type: 'omnipixel-connect'
  }, '*');
}

function disconnectStream() {
  document.querySelector('iframe').contentWindow.postMessage({
    type: 'omnipixel-disconnect'
  }, '*');
}
</script>`},ay=async()=>{try{await navigator.clipboard.writeText(ax()),alert("Embed code copied to clipboard!")}catch{}},az=()=>{switch(U){case"connected":return"bg-green-100 text-green-800";case"connecting":return"bg-yellow-100 text-yellow-800";case"disconnected":default:return"bg-gray-100 text-gray-800";case"error":return"bg-red-100 text-red-800"}},aA=()=>{switch(U){case"connected":return"Connected";case"connecting":return"Connecting...";case"disconnected":return"Disconnected";case"error":return"Error";default:return"Unknown"}};return M?.isPasswordProtected&&!ac&&$?(0,d.jsx)(h.Zp,{className:c,children:(0,d.jsx)(h.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"text-center space-y-4",children:[(0,d.jsx)(l.A,{className:"h-12 w-12 mx-auto text-gray-400"}),(0,d.jsx)("h3",{className:"text-lg font-medium",children:"Password Protected"}),(0,d.jsx)("p",{className:"text-gray-600",children:"This stream requires a password to access."}),(0,d.jsxs)("div",{className:"max-w-sm mx-auto space-y-3",children:[(0,d.jsx)(j.p,{type:"password",placeholder:"Enter password",value:aa,onChange:a=>ab(a.target.value),onKeyDown:a=>"Enter"===a.key&&aw()}),(0,d.jsxs)(g.$,{onClick:aw,className:"w-full",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Access Stream"]})]}),S&&(0,d.jsx)("p",{className:"text-sm text-red-600",children:S})]})})}):C&&D?(0,d.jsx)("div",{className:`relative w-full h-full ${c}`,children:(0,d.jsxs)("div",{ref:ag,className:"absolute inset-0 bg-black",children:[(0,d.jsx)("div",{ref:ah,className:"absolute inset-0 w-full h-full"}),(E||"connecting"===U)&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-20",children:(0,d.jsxs)("div",{className:"text-center text-white",children:[(0,d.jsx)(n.A,{className:"h-12 w-12 animate-spin mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-lg font-medium mb-2",children:W}),(0,d.jsxs)("p",{className:"text-sm opacity-75",children:["Status: ",aA()]})]})}),S&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-red-900 z-20",children:(0,d.jsxs)("div",{className:"text-center text-white p-4",children:[(0,d.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,d.jsx)("p",{children:S})]})}),x&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"absolute top-4 left-4 z-30 flex items-center space-x-2",children:[(0,d.jsx)(i.E,{className:az(),children:aA()}),b&&(0,d.jsxs)(i.E,{variant:"outline",className:"text-white border-white/50 bg-black/70 backdrop-blur-sm",children:["Build: ",b.slice(0,8),"..."]})]}),(0,d.jsxs)("div",{className:"absolute bottom-4 right-4 z-30 flex items-center space-x-2",children:[G?(0,d.jsxs)(g.$,{onClick:at,variant:"destructive",size:"sm",className:"bg-red-600/90 hover:bg-red-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Disconnect"]}):(0,d.jsxs)(g.$,{onClick:as,size:"sm",className:"bg-green-600/90 hover:bg-green-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Connect"]}),(0,d.jsx)(g.$,{onClick:au,variant:"outline",size:"sm",className:"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg",children:I?(0,d.jsx)(r.A,{className:"h-4 w-4"}):(0,d.jsx)(s.A,{className:"h-4 w-4"})}),(0,d.jsx)(g.$,{onClick:av,variant:"outline",size:"sm",className:"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg",children:(0,d.jsx)(t.A,{className:"h-4 w-4"})})]})]})]})}):(0,d.jsxs)(h.Zp,{className:c,children:[y&&(0,d.jsx)(h.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(h.ZB,{children:"Interactive Stream"}),(0,d.jsx)(h.BT,{children:"Real-time interactive streaming experience"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(i.E,{className:az(),children:aA()}),b&&(0,d.jsxs)(i.E,{variant:"outline",children:["Build: ",b.slice(0,8),"..."]}),z&&(0,d.jsxs)(k.lG,{open:ae,onOpenChange:af,children:[(0,d.jsx)(k.zM,{asChild:!0,children:(0,d.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Embed"]})}),(0,d.jsxs)(k.Cf,{children:[(0,d.jsxs)(k.c7,{children:[(0,d.jsx)(k.L3,{children:"Embed Stream"}),(0,d.jsx)(k.rr,{children:"Copy this code to embed the stream in your website"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"Embed Code:"}),(0,d.jsx)("textarea",{className:"w-full mt-1 p-2 border rounded text-sm font-mono",rows:6,readOnly:!0,value:ax()})]}),(0,d.jsx)(g.$,{onClick:ay,className:"w-full",children:"Copy Embed Code"})]})]})]})]})]})}),(0,d.jsxs)(h.Wu,{children:[S&&(0,d.jsx)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 text-red-500 mr-2"}),(0,d.jsx)("p",{className:"text-sm text-red-700",children:S})]})}),(0,d.jsxs)("div",{ref:ag,className:"relative bg-black rounded-lg overflow-hidden",style:{width:"100%",aspectRatio:`${A}/${B}`,minHeight:"400px"},children:[(E||"connecting"===U)&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-10",children:(0,d.jsxs)("div",{className:"text-center text-white",children:[(0,d.jsx)(n.A,{className:"h-12 w-12 animate-spin mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-lg font-medium mb-2",children:W}),(0,d.jsxs)("p",{className:"text-sm opacity-75",children:["Status: ",aA()]})]})}),!E&&!G&&!S&&"disconnected"===U&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900 z-10",children:(0,d.jsxs)("div",{className:"text-center text-white space-y-4",children:[(0,d.jsx)(p.A,{className:"h-16 w-16 mx-auto opacity-50"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-lg mb-2",children:"Ready to Connect"}),(0,d.jsx)("p",{className:"text-sm opacity-75 mb-4",children:an.disconnected}),!M?.autoConnect&&(0,d.jsxs)(g.$,{onClick:as,size:"lg",children:[(0,d.jsx)(p.A,{className:"h-5 w-5 mr-2"}),an.connectButton]})]})]})}),"error"===U&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900 z-10",children:(0,d.jsxs)("div",{className:"text-center text-white",children:[(0,d.jsx)(o.A,{className:"h-16 w-16 mx-auto mb-4 text-red-500"}),(0,d.jsx)("p",{className:"text-lg mb-2",children:"Connection Error"}),(0,d.jsx)("p",{className:"text-sm opacity-75",children:S||an.error})]})}),(0,d.jsx)("div",{ref:ah,className:"absolute inset-0 w-full h-full",style:{zIndex:0,pointerEvents:E?"none":"auto"}})]}),x&&(0,d.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:["disconnected"===U||"error"===U?(0,d.jsxs)(g.$,{onClick:as,disabled:E,children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),"Connect"]}):"connecting"===U?(0,d.jsxs)(g.$,{disabled:!0,children:[(0,d.jsx)(n.A,{className:"h-4 w-4 animate-spin"}),an.connecting]}):(0,d.jsxs)(g.$,{variant:"outline",onClick:at,children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"Disconnect"]}),(0,d.jsx)(g.$,{variant:"outline",onClick:au,children:I?(0,d.jsx)(r.A,{className:"h-4 w-4"}):(0,d.jsx)(s.A,{className:"h-4 w-4"})})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(g.$,{variant:"outline",size:"sm",onClick:av,children:(0,d.jsx)(t.A,{className:"h-4 w-4"})}),(0,d.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>{},title:"Open in new tab",children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[(0,d.jsxs)("p",{children:["Stream ID: ",K]}),b&&(0,d.jsxs)("p",{children:["Build ID: ",b]})]})]})]})}}};