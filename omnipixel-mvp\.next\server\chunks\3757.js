"use strict";exports.id=3757,exports.ids=[3757],exports.modules={32032:(a,b,c)=>{c.d(b,{U:()=>f});var d=c(34386),e=c(44999);async function f(a,b){let c=await (0,e.UL)();return a=a??"https://qrnstvofnizsgdlubtbt.supabase.co",b=b??"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFybnN0dm9mbml6c2dkbHVidGJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyNzE1MDMsImV4cCI6MjA2ODg0NzUwM30.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU",(0,d.createServerClient)(a,b,{cookies:{getAll:()=>c.getAll(),setAll(a){try{a.forEach(({name:a,value:b,options:d})=>c.set(a,b,d))}catch{}}}})}},52624:(a,b,c)=>{c.d(b,{T:()=>w});var d=c(60687),e=c(43210),f=c(24934),g=c(55192),h=c(59821),i=c(37826),j=c(5336),k=c(16023),l=c(48730),m=c(97840),n=c(77026),o=c(93613),p=c(17535),q=c(40228),r=c(62022),s=c(31158),t=c(41862),u=c(88233),v=c(45583);function w({projectId:a,builds:b,onBuildRevert:c,onBuildDelete:w,onBuildActivate:x,onRefresh:y,isAdmin:z=!1,className:A=""}){let[B,C]=(0,e.useState)(null),[D,E]=(0,e.useState)(null),[F,G]=(0,e.useState)(null),[H,I]=(0,e.useState)(null),J=a=>{switch(a){case"active":return(0,d.jsx)(j.A,{className:"h-4 w-4 text-green-500"});case"uploading":return(0,d.jsx)(k.A,{className:"h-4 w-4 text-blue-500"});case"processing":return(0,d.jsx)(l.A,{className:"h-4 w-4 text-yellow-500"});case"inactive":return(0,d.jsx)(m.A,{className:"h-4 w-4 text-gray-500"});case"archived":return(0,d.jsx)(n.A,{className:"h-4 w-4 text-gray-500"});case"failed":return(0,d.jsx)(o.A,{className:"h-4 w-4 text-red-500"});default:return(0,d.jsx)(l.A,{className:"h-4 w-4 text-gray-500"})}},K=a=>{switch(a){case"active":return"bg-green-100 text-green-800";case"uploading":return"bg-blue-100 text-blue-800";case"processing":return"bg-yellow-100 text-yellow-800";case"inactive":return"bg-gray-100 text-gray-600";case"archived":default:return"bg-gray-100 text-gray-800";case"failed":return"bg-red-100 text-red-800"}},L=a=>{if(!a)return"Unknown size";let b=Math.floor(Math.log(a)/Math.log(1024));return Math.round(a/Math.pow(1024,b)*100)/100+" "+["Bytes","KB","MB","GB"][b]},M=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),N=async a=>{if(w&&confirm(`Are you sure you want to delete version ${a.version} (${a.original_filename||a.filename})? This will permanently remove the ZIP file from storage and cannot be undone.`))try{E(a.id),await w(a.id),y?.()}catch(a){console.error("Error deleting build:",a),alert("Failed to delete build. Please try again.")}finally{E(null)}},O=async a=>{if(x&&!a.is_current&&confirm(`Are you sure you want to activate version ${a.version} (${a.original_filename||a.filename})? This will make it the current active build and upload it to StreamPixel.`))try{G(a.id),await x(a.id),y?.()}catch(a){console.error("Error activating build:",a),alert("Failed to activate build. Please try again.")}finally{G(null)}},P=b.find(a=>a.is_current),Q=b.filter(a=>!a.is_current).slice(0,10);return(0,d.jsxs)(g.Zp,{className:A,children:[(0,d.jsxs)(g.aR,{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(p.A,{className:"h-5 w-5"}),(0,d.jsx)(g.ZB,{children:"Builds"}),(0,d.jsxs)(h.E,{variant:"secondary",children:[b.length," total"]})]}),y&&(0,d.jsx)(f.$,{size:"sm",variant:"outline",onClick:y,children:"Refresh"})]}),(0,d.jsxs)(g.BT,{children:["View and manage build versions. Each project can have up to 2 active builds.",!z&&" You can revert to any previous version."]})]}),(0,d.jsxs)(g.Wu,{className:"space-y-6",children:[P&&(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"border border-green-200 rounded-lg p-4 bg-green-50",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900",children:P.original_filename||P.filename}),(0,d.jsxs)(h.E,{className:K(P.status),children:["Version ",P.version]}),(0,d.jsx)(h.E,{variant:"outline",className:"bg-green-100 text-green-800",children:"Current"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-1"}),M(P.created_at)]}),P.file_size&&(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mr-1"}),L(P.file_size)]}),(0,d.jsxs)("div",{className:"flex items-center",children:[J(P.status),(0,d.jsx)("span",{className:"ml-1 capitalize",children:P.status})]})]}),P.streampixel_status&&(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsx)(h.E,{variant:"outline",className:`text-xs ${"uploaded"===P.streampixel_status?"bg-green-50 text-green-700 border-green-200":"validation_failed"===P.streampixel_status?"bg-yellow-50 text-yellow-700 border-yellow-200":"failed"===P.streampixel_status?"bg-red-50 text-red-700 border-red-200":""}`})})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(i.lG,{children:[(0,d.jsx)(i.zM,{asChild:!0,children:(0,d.jsx)(f.$,{size:"sm",variant:"outline",onClick:()=>I(P),children:"View Details"})}),(0,d.jsxs)(i.Cf,{children:[(0,d.jsxs)(i.c7,{children:[(0,d.jsxs)(i.L3,{children:["Build Details - Version ",P.version]}),(0,d.jsx)(i.rr,{children:"Detailed information about this build"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Filename"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:P.original_filename||P.filename})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Status"}),(0,d.jsxs)("div",{className:"flex items-center mt-1",children:[J(P.status),(0,d.jsx)("span",{className:"ml-2 text-sm capitalize",children:P.status})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Upload Date"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:M(P.created_at)})]}),P.file_size&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"File Size"}),(0,d.jsx)("p",{className:"text-sm text-gray-900",children:L(P.file_size)})]}),P.streampixel_build_id&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"StreamPixel Build ID"}),(0,d.jsx)("p",{className:"text-sm text-gray-900 font-mono",children:P.streampixel_build_id})]}),P.error_message&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-red-700",children:"Error Message"}),(0,d.jsx)("p",{className:"text-sm text-red-900 bg-red-50 p-2 rounded",children:P.error_message})]})]})]})]}),(0,d.jsx)(f.$,{size:"sm",variant:"outline",children:(0,d.jsx)(s.A,{className:"h-4 w-4"})}),w&&(0,d.jsx)(f.$,{size:"sm",variant:"destructive",onClick:()=>N(P),disabled:D===P.id,title:"Delete build",children:D===P.id?(0,d.jsx)(t.A,{className:"h-4 w-4 animate-spin"}):(0,d.jsx)(u.A,{className:"h-4 w-4"})})]})]})})}),(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"space-y-3",children:Q.map((a,b)=>(0,d.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900",children:a.original_filename||a.filename}),(0,d.jsxs)(h.E,{variant:"outline",children:["Version ",a.version]}),(0,d.jsx)(h.E,{className:K(a.status),children:a.status}),a.streampixel_status&&(0,d.jsx)(h.E,{variant:"outline",className:`text-xs ${"uploaded"===a.streampixel_status?"bg-green-50 text-green-700 border-green-200":"validation_failed"===a.streampixel_status?"bg-yellow-50 text-yellow-700 border-yellow-200":"failed"===a.streampixel_status?"bg-red-50 text-red-700 border-red-200":""}`})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-1"}),M(a.created_at)]}),a.file_size&&(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mr-1"}),L(a.file_size)]})]}),a.error_message&&(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsx)("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:a.error_message})})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[("archived"===a.status||"inactive"===a.status)&&x&&(0,d.jsxs)(f.$,{size:"sm",variant:"default",onClick:()=>O(a),disabled:F===a.id,children:[F===a.id?(0,d.jsx)(t.A,{className:"h-4 w-4 animate-spin"}):(0,d.jsx)(v.A,{className:"h-4 w-4"}),F===a.id?"Activating...":"Activate"]}),(0,d.jsx)(f.$,{size:"sm",variant:"outline",children:(0,d.jsx)(s.A,{className:"h-4 w-4"})}),w&&(0,d.jsx)(f.$,{size:"sm",variant:"destructive",onClick:()=>N(a),disabled:D===a.id,title:"Delete build",children:D===a.id?(0,d.jsx)(t.A,{className:"h-4 w-4 animate-spin"}):(0,d.jsx)(u.A,{className:"h-4 w-4"})})]})]})},a.id||`build-${b}-${a.original_filename||a.filename}-${a.version}`))})}),0===b.length&&(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No builds yet"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Upload your first build to get started."})]}),(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)(o.A,{className:"h-5 w-5 text-blue-500"})}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-blue-900",children:"Build Management"}),(0,d.jsxs)("div",{className:"mt-1 text-sm text-blue-700",children:[(0,d.jsxs)("p",{children:["• Each project can have ",(0,d.jsx)("strong",{children:"maximum 2 builds"})," (active, inactive, or archived)"]}),(0,d.jsxs)("p",{children:["• New builds are ",(0,d.jsx)("strong",{children:"inactive by default"})," unless auto-release is enabled"]}),(0,d.jsxs)("p",{children:["• Only ",(0,d.jsx)("strong",{children:"ZIP files"})," are accepted for game builds"]}),(0,d.jsxs)("p",{children:["• You must ",(0,d.jsx)("strong",{children:"delete a build"})," to upload a new one when limit is reached"]}),(0,d.jsx)("p",{children:"• Activate an inactive/archived build to make it current and upload to StreamPixel"}),(0,d.jsxs)("p",{children:["• Activated builds show as ",(0,d.jsx)("strong",{children:"processing"})," until StreamPixel confirms they are live"]}),(0,d.jsxs)("p",{children:["• ",(0,d.jsx)("strong",{children:"Any build can be deleted"})," - you have full control over your builds"]}),(0,d.jsx)("p",{children:"• Deleting a build permanently removes the ZIP file from storage"})]})]})]})})]})]})}},79150:(a,b,c)=>{c.d(b,{V:()=>u});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(24301),i=c(24934);c(43210);var j=c(26312),k=c(96241);function l({...a}){return(0,d.jsx)(j.bL,{"data-slot":"dropdown-menu",...a})}function m({...a}){return(0,d.jsx)(j.l9,{"data-slot":"dropdown-menu-trigger",...a})}function n({className:a,sideOffset:b=4,...c}){return(0,d.jsx)(j.ZL,{children:(0,d.jsx)(j.UC,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,k.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...c})})}function o({className:a,inset:b,variant:c="default",...e}){return(0,d.jsx)(j.q7,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,k.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e})}function p({className:a,...b}){return(0,d.jsx)(j.wv,{"data-slot":"dropdown-menu-separator",className:(0,k.cn)("bg-border -mx-1 my-1 h-px",a),...b})}var q=c(99891),r=c(58869),s=c(84027),t=c(40083);function u(){let{user:a,profile:b,signOut:c}=(0,h.A)(),e=(0,g.useRouter)(),j=async()=>{await c(),e.push("/login")};return a?(0,d.jsx)("nav",{className:"border-b bg-white",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between h-16",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(f(),{href:"/dashboard",className:"flex-shrink-0",children:(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Omnipixel"})}),(0,d.jsxs)("div",{className:"hidden md:ml-6 md:flex md:space-x-8",children:[(0,d.jsx)(f(),{href:"/dashboard",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium",children:"Dashboard"}),b?.role==="platform_admin"&&(0,d.jsxs)(f(),{href:"/admin",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium flex items-center gap-1",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"Admin"]})]})]}),(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)(l,{children:[(0,d.jsx)(m,{asChild:!0,children:(0,d.jsx)(i.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,d.jsx)(r.A,{className:"h-4 w-4"})})}),(0,d.jsxs)(n,{className:"w-56",align:"end",forceMount:!0,children:[(0,d.jsx)("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[(0,d.jsx)("p",{className:"font-medium",children:a.email}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:b?.role==="platform_admin"?"Platform Admin":"User"})]})}),(0,d.jsx)(p,{}),(0,d.jsxs)(o,{children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Settings"})]}),b?.role==="platform_admin"&&(0,d.jsx)(o,{asChild:!0,children:(0,d.jsxs)(f(),{href:"/admin",children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Admin Panel"})]})}),(0,d.jsx)(p,{}),(0,d.jsxs)(o,{onClick:j,children:[(0,d.jsx)(t.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Log out"})]})]})]})})]})})}):null}},95510:(a,b,c)=>{c.d(b,{s:()=>A});var d=c(60687),e=c(43210);c(89436),c(41236);var f=c(55192),g=c(24934),h=c(25177),i=c(96241);function j({className:a,value:b,...c}){return(0,d.jsx)(h.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...c,children:(0,d.jsx)(h.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(b||0)}%)`}})})}var k=c(59821);let l=(0,c(24224).F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function m({className:a,variant:b,...c}){return(0,d.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(l({variant:b}),a),...c})}function n({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...b})}var o=c(62369);function p({className:a,orientation:b="horizontal",decorative:c=!0,...e}){return(0,d.jsx)(o.b,{"data-slot":"separator",decorative:c,orientation:b,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...e})}var q=c(16023),r=c(78464),s=c(10022),t=c(11860),u=c(45583),v=c(48730),w=c(97840),x=c(36058),y=c(5336),z=c(93613);function A({projectId:a,onUploadComplete:b,onUploadError:c,className:h}){let l=(0,e.useRef)(null);(0,e.useRef)(null);let o=(0,e.useRef)(null),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(0),[E,F]=(0,e.useState)(0),[G,H]=(0,e.useState)(0),[I,J]=(0,e.useState)(!1),[K,L]=(0,e.useState)(null),[M,N]=(0,e.useState)(null),[O,P]=(0,e.useState)(null),[Q,R]=(0,e.useState)(!1),S=(0,e.useCallback)(a=>{a.preventDefault(),a.stopPropagation(),R(!0)},[]),T=(0,e.useCallback)(a=>{a.preventDefault(),a.stopPropagation(),R(!1)},[]),U=(0,e.useCallback)(b=>{b.preventDefault(),b.stopPropagation(),R(!1);let c=Array.from(b.dataTransfer.files);if(c.length>0&&l.current)try{let b=c[0];l.current.getFiles().forEach(a=>{l.current?.removeFile(a.id)}),l.current.addFile({source:"drag-drop",name:b.name,type:b.type,data:b,meta:{projectId:a}})}catch(a){console.error("Error adding file:",a),L("Failed to add file. Please check the file type and size.")}},[a]),V=(0,e.useCallback)(()=>{A||O||(console.log("Upload area clicked, opening file dialog"),o.current?.click())},[A,O]),W=(0,e.useCallback)(b=>{let c=b.target.files;if(console.log("File input changed:",c),c&&c.length>0&&l.current)try{let b=c[0];console.log("Adding file to Uppy:",b.name,b.type,b.size),l.current.getFiles().forEach(a=>{l.current?.removeFile(a.id)}),l.current.addFile({source:"file-input",name:b.name,type:b.type,data:b,meta:{projectId:a}})}catch(a){console.error("Error adding file to Uppy:",a),L("Failed to add file. Please check the file type and size.")}b.target.value=""},[a]),X=a=>{if(0===a)return"0 Bytes";let b=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,b)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][b]};return(0,d.jsx)("div",{className:h,children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsxs)(f.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"Upload Game Build"})]}),(0,d.jsx)(f.BT,{children:"Upload your game build ZIP files with robust, pausable uploads supporting files up to 24GB"})]}),(0,d.jsxs)(f.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:(0,i.cn)("relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",Q?"border-primary bg-primary/5":"border-muted-foreground/25 hover:border-muted-foreground/50",O&&"border-green-500 bg-green-50"),onDragOver:S,onDragLeave:T,onDrop:U,onClick:V,children:[(0,d.jsx)("input",{ref:o,type:"file",accept:".zip,application/zip,application/x-zip-compressed",onChange:W,className:"hidden"}),O?(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center",children:(0,d.jsx)(r.A,{className:"h-6 w-6 text-green-600"})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("p",{className:"text-lg font-medium text-green-700",children:"File Selected"}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 text-muted-foreground"}),(0,d.jsx)("span",{className:"font-medium",children:O.name}),(0,d.jsx)(k.E,{variant:"secondary",children:X(O.size||0)})]}),(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 pt-2",children:[(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{l.current&&O&&(l.current.removeFile(O.id),P(null))},className:"flex items-center space-x-1",children:[(0,d.jsx)(t.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Remove"})]}),!A&&(0,d.jsxs)(g.$,{onClick:()=>{l.current&&l.current.upload()},className:"flex items-center space-x-1",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Start Upload"})]})]})]})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center",children:(0,d.jsx)(q.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("p",{className:"text-lg font-medium",children:Q?"Drop your file here":"Drag & drop your build file"}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"or click to browse files"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Supports ZIP files up to 24GB"})]})]})]}),A&&(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{className:"pb-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(f.ZB,{className:"text-base",children:["Uploading ",O?.name]}),(0,d.jsx)(k.E,{variant:I?"secondary":"default",children:I?"Paused":"Uploading"})]})}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsxs)("span",{className:"font-medium",children:[C,"% complete"]}),(0,d.jsxs)("span",{className:"text-muted-foreground",children:[X(E),"/s"]})]}),(0,d.jsx)(j,{value:C,className:"h-2"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 text-blue-500"}),(0,d.jsx)("span",{className:"text-muted-foreground",children:"Speed:"}),(0,d.jsxs)("span",{className:"font-medium",children:[X(E),"/s"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 text-orange-500"}),(0,d.jsx)("span",{className:"text-muted-foreground",children:"Time left:"}),(0,d.jsx)("span",{className:"font-medium",children:(a=>{if(!isFinite(a)||a<0)return"--";let b=Math.floor(a/3600),c=Math.floor(a%3600/60),d=Math.floor(a%60);return b>0?`${b}h ${c}m ${d}s`:c>0?`${c}m ${d}s`:`${d}s`})(G)})]})]}),(0,d.jsx)(p,{}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{l.current&&(I?(l.current.resumeAll(),J(!1)):(l.current.pauseAll(),J(!0)))},className:"flex items-center space-x-1",children:[I?(0,d.jsx)(w.A,{className:"h-4 w-4"}):(0,d.jsx)(x.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:I?"Resume":"Pause"})]}),(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{l.current&&(l.current.cancelAll(),P(null))},className:"flex items-center space-x-1",children:[(0,d.jsx)(t.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Cancel"})]})]}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Upload continues in background when tab is minimized"})]})]})]}),M&&(0,d.jsxs)(m,{className:"border-green-200 bg-green-50",children:[(0,d.jsx)(y.A,{className:"h-4 w-4 text-green-600"}),(0,d.jsx)(n,{className:"text-green-700",children:M})]}),K&&(0,d.jsxs)(m,{variant:"destructive",children:[(0,d.jsx)(z.A,{className:"h-4 w-4"}),(0,d.jsx)(n,{children:K})]})]})]})})}c(98866)}};