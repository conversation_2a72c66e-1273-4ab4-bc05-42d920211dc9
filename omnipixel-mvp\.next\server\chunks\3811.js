"use strict";exports.id=3811,exports.ids=[3811],exports.modules={34386:(a,b,c)=>{let d;c.d(b,{createBrowserClient:()=>v,createServerClient:()=>w});var e=c(66437);let f="0.6.1";var g=c(49343);function h(){return"undefined"!=typeof window&&void 0!==window.document}let i={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},j=/^(.*)[.](0|[1-9][0-9]*)$/;function k(a,b){if(a===b)return!0;let c=a.match(j);return!!c&&c[1]===b}function l(a,b,c){let d=c??3180,e=encodeURIComponent(b);if(e.length<=d)return[{name:a,value:b}];let f=[];for(;e.length>0;){let a=e.slice(0,d),b=a.lastIndexOf("%");b>d-3&&(a=a.slice(0,b));let c="";for(;a.length>0;)try{c=decodeURIComponent(a);break}catch(b){if(b instanceof URIError&&"%"===a.at(-3)&&a.length>3)a=a.slice(0,a.length-3);else throw b}f.push(c),e=e.slice(a.length)}return f.map((b,c)=>({name:`${a}.${c}`,value:b}))}async function m(a,b){let c=await b(a);if(c)return c;let d=[];for(let c=0;;c++){let e=`${a}.${c}`,f=await b(e);if(!f)break;d.push(f)}return d.length>0?d.join(""):null}let n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),o=" 	\n\r=".split(""),p=(()=>{let a=Array(128);for(let b=0;b<a.length;b+=1)a[b]=-1;for(let b=0;b<o.length;b+=1)a[o[b].charCodeAt(0)]=-2;for(let b=0;b<n.length;b+=1)a[n[b].charCodeAt(0)]=b;return a})();function q(a){let b=[],c=0,d=0;if(function(a,b){for(let c=0;c<a.length;c+=1){let d=a.charCodeAt(c);if(d>55295&&d<=56319){let b=(d-55296)*1024&65535;d=(a.charCodeAt(c+1)-56320&65535|b)+65536,c+=1}!function(a,b){if(a<=127)return b(a);if(a<=2047){b(192|a>>6),b(128|63&a);return}if(a<=65535){b(224|a>>12),b(128|a>>6&63),b(128|63&a);return}if(a<=1114111){b(240|a>>18),b(128|a>>12&63),b(128|a>>6&63),b(128|63&a);return}throw Error(`Unrecognized Unicode codepoint: ${a.toString(16)}`)}(d,b)}}(a,a=>{for(c=c<<8|a,d+=8;d>=6;){let a=c>>d-6&63;b.push(n[a]),d-=6}}),d>0)for(c<<=6-d,d=6;d>=6;){let a=c>>d-6&63;b.push(n[a]),d-=6}return b.join("")}function r(a){let b=[],c=a=>{b.push(String.fromCodePoint(a))},d={utf8seq:0,codepoint:0},e=0,f=0;for(let b=0;b<a.length;b+=1){let g=p[a.charCodeAt(b)];if(g>-1)for(e=e<<6|g,f+=6;f>=8;)(function(a,b,c){if(0===b.utf8seq){if(a<=127)return c(a);for(let c=1;c<6;c+=1)if((a>>7-c&1)==0){b.utf8seq=c;break}if(2===b.utf8seq)b.codepoint=31&a;else if(3===b.utf8seq)b.codepoint=15&a;else if(4===b.utf8seq)b.codepoint=7&a;else throw Error("Invalid UTF-8 sequence");b.utf8seq-=1}else if(b.utf8seq>0){if(a<=127)throw Error("Invalid UTF-8 sequence");b.codepoint=b.codepoint<<6|63&a,b.utf8seq-=1,0===b.utf8seq&&c(b.codepoint)}})(e>>f-8&255,d,c),f-=8;else if(-2===g)continue;else throw Error(`Invalid Base64-URL character "${a.at(b)}" at position ${b}`)}return b.join("")}let s="base64-";function t(a,b){let c,d,e=a.cookies??null,f=a.cookieEncoding,j={},n={};if(e)if("get"in e){let a=async a=>{let b=a.flatMap(a=>[a,...Array.from({length:5}).map((b,c)=>`${a}.${c}`)]),c=[];for(let a=0;a<b.length;a+=1){let d=await e.get(b[a]);(d||"string"==typeof d)&&c.push({name:b[a],value:d})}return c};if(c=async b=>await a(b),"set"in e&&"remove"in e)d=async a=>{for(let b=0;b<a.length;b+=1){let{name:c,value:d,options:f}=a[b];d?await e.set(c,d,f):await e.remove(c,f)}};else if(b)d=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in e)if(c=async()=>await e.getAll(),"setAll"in e)d=e.setAll;else if(b)d=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw Error(`@supabase/ssr: ${b?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${h()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(!b&&h())c=()=>(()=>{let a=(0,g.qg)(document.cookie);return Object.keys(a).map(b=>({name:b,value:a[b]??""}))})(),d=a=>{a.forEach(({name:a,value:b,options:c})=>{document.cookie=(0,g.lK)(a,b,c)})};else if(b)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else c=()=>[],d=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return b?{getAll:c,setAll:d,setItems:j,removedItems:n,storage:{isServer:!0,getItem:async a=>{if("string"==typeof j[a])return j[a];if(n[a])return null;let b=await c([a]),d=await m(a,async a=>{let c=b?.find(({name:b})=>b===a)||null;return c?c.value:null});if(!d)return null;let e=d;return"string"==typeof d&&d.startsWith(s)&&(e=r(d.substring(s.length))),e},setItem:async(b,e)=>{b.endsWith("-code-verifier")&&await u({getAll:c,setAll:d,setItems:{[b]:e},removedItems:{}},{cookieOptions:a?.cookieOptions??null,cookieEncoding:f}),j[b]=e,delete n[b]},removeItem:async a=>{delete j[a],n[a]=!0}}}:{getAll:c,setAll:d,setItems:j,removedItems:n,storage:{isServer:!1,getItem:async a=>{let b=await c([a]),d=await m(a,async a=>{let c=b?.find(({name:b})=>b===a)||null;return c?c.value:null});if(!d)return null;let e=d;return d.startsWith(s)&&(e=r(d.substring(s.length))),e},setItem:async(b,e)=>{let g=await c([b]),h=new Set((g?.map(({name:a})=>a)||[]).filter(a=>k(a,b))),j=e;"base64url"===f&&(j=s+q(e));let m=l(b,j);m.forEach(({name:a})=>{h.delete(a)});let n={...i,...a?.cookieOptions,maxAge:0},o={...i,...a?.cookieOptions,maxAge:i.maxAge};delete n.name,delete o.name;let p=[...[...h].map(a=>({name:a,value:"",options:n})),...m.map(({name:a,value:b})=>({name:a,value:b,options:o}))];p.length>0&&await d(p)},removeItem:async b=>{let e=await c([b]),f=(e?.map(({name:a})=>a)||[]).filter(a=>k(a,b)),g={...i,...a?.cookieOptions,maxAge:0};delete g.name,f.length>0&&await d(f.map(a=>({name:a,value:"",options:g})))}}}}async function u({getAll:a,setAll:b,setItems:c,removedItems:d},e){let f=e.cookieEncoding,g=e.cookieOptions??null,h=await a([...c?Object.keys(c):[],...d?Object.keys(d):[]]),j=h?.map(({name:a})=>a)||[],m=Object.keys(d).flatMap(a=>j.filter(b=>k(b,a))),n=Object.keys(c).flatMap(a=>{let b=new Set(j.filter(b=>k(b,a))),d=c[a];"base64url"===f&&(d=s+q(d));let e=l(a,d);return e.forEach(a=>{b.delete(a.name)}),m.push(...b),e}),o={...i,...g,maxAge:0},p={...i,...g,maxAge:i.maxAge};delete o.name,delete p.name,await b([...m.map(a=>({name:a,value:"",options:o})),...n.map(({name:a,value:b})=>({name:a,value:b,options:p}))])}function v(a,b,c){let g=c?.isSingleton===!0||(!c||!("isSingleton"in c))&&h();if(g&&d)return d;if(!a||!b)throw Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:i}=t({...c,cookieEncoding:c?.cookieEncoding??"base64url"},!1),j=(0,e.UU)(a,b,{...c,global:{...c?.global,headers:{...c?.global?.headers,"X-Client-Info":`supabase-ssr/${f} createBrowserClient`}},auth:{...c?.auth,...c?.cookieOptions?.name?{storageKey:c.cookieOptions.name}:null,flowType:"pkce",autoRefreshToken:h(),detectSessionInUrl:h(),persistSession:!0,storage:i}});return g&&(d=j),j}function w(a,b,c){if(!a||!b)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:d,getAll:g,setAll:h,setItems:i,removedItems:j}=t({...c,cookieEncoding:c?.cookieEncoding??"base64url"},!0),k=(0,e.UU)(a,b,{...c,global:{...c?.global,headers:{...c?.global?.headers,"X-Client-Info":`supabase-ssr/${f} createServerClient`}},auth:{...c?.cookieOptions?.name?{storageKey:c.cookieOptions.name}:null,...c?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:d}});return k.auth.onAuthStateChange(async a=>{(Object.keys(i).length>0||Object.keys(j).length>0)&&("SIGNED_IN"===a||"TOKEN_REFRESHED"===a||"USER_UPDATED"===a||"PASSWORD_RECOVERY"===a||"SIGNED_OUT"===a||"MFA_CHALLENGE_VERIFIED"===a)&&await u({getAll:g,setAll:h,setItems:i,removedItems:j},{cookieOptions:c?.cookieOptions??null,cookieEncoding:c?.cookieEncoding??"base64url"})}),k}},49343:(a,b)=>{b.qg=function(a,b){let c=new h,d=a.length;if(d<2)return c;let e=b?.decode||k,f=0;do{let b=a.indexOf("=",f);if(-1===b)break;let g=a.indexOf(";",f),h=-1===g?d:g;if(b>h){f=a.lastIndexOf(";",b-1)+1;continue}let k=i(a,f,b),l=j(a,b,k),m=a.slice(k,l);if(void 0===c[m]){let d=i(a,b+1,h),f=j(a,h,d),g=e(a.slice(d,f));c[m]=g}f=h+1}while(f<d);return c},b.lK=function(a,b,h){let i=h?.encode||encodeURIComponent;if(!c.test(a))throw TypeError(`argument name is invalid: ${a}`);let j=i(b);if(!d.test(j))throw TypeError(`argument val is invalid: ${b}`);let k=a+"="+j;if(!h)return k;if(void 0!==h.maxAge){if(!Number.isInteger(h.maxAge))throw TypeError(`option maxAge is invalid: ${h.maxAge}`);k+="; Max-Age="+h.maxAge}if(h.domain){if(!e.test(h.domain))throw TypeError(`option domain is invalid: ${h.domain}`);k+="; Domain="+h.domain}if(h.path){if(!f.test(h.path))throw TypeError(`option path is invalid: ${h.path}`);k+="; Path="+h.path}if(h.expires){var l;if(l=h.expires,"[object Date]"!==g.call(l)||!Number.isFinite(h.expires.valueOf()))throw TypeError(`option expires is invalid: ${h.expires}`);k+="; Expires="+h.expires.toUTCString()}if(h.httpOnly&&(k+="; HttpOnly"),h.secure&&(k+="; Secure"),h.partitioned&&(k+="; Partitioned"),h.priority)switch("string"==typeof h.priority?h.priority.toLowerCase():void 0){case"low":k+="; Priority=Low";break;case"medium":k+="; Priority=Medium";break;case"high":k+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${h.priority}`)}if(h.sameSite)switch("string"==typeof h.sameSite?h.sameSite.toLowerCase():h.sameSite){case!0:case"strict":k+="; SameSite=Strict";break;case"lax":k+="; SameSite=Lax";break;case"none":k+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${h.sameSite}`)}return k};let c=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,d=/^[\u0021-\u003A\u003C-\u007E]*$/,e=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,f=/^[\u0020-\u003A\u003D-\u007E]*$/,g=Object.prototype.toString,h=(()=>{let a=function(){};return a.prototype=Object.create(null),a})();function i(a,b,c){do{let c=a.charCodeAt(b);if(32!==c&&9!==c)return b}while(++b<c);return c}function j(a,b,c){for(;b>c;){let c=a.charCodeAt(--b);if(32!==c&&9!==c)return b+1}return c}function k(a){if(-1===a.indexOf("%"))return a;try{return decodeURIComponent(a)}catch(b){return a}}},76926:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(61120));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})}};