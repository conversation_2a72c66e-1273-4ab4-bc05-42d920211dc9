"use strict";exports.id=5362,exports.ids=[5362],exports.modules={11860:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},19080:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},26134:(a,b,c)=>{c.d(b,{UC:()=>ac,VY:()=>ae,ZL:()=>aa,bL:()=>$,bm:()=>af,hE:()=>ad,hJ:()=>ab,l9:()=>_});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(96963),i=c(65551),j=c(31355),k=c(32547),l=c(25028),m=c(46059),n=c(14163),o=c(1359),p=c(42247),q=c(63376),r=c(8730),s=c(60687),t="Dialog",[u,v]=(0,g.A)(t),[w,x]=u(t),y=a=>{let{__scopeDialog:b,children:c,open:e,defaultOpen:f,onOpenChange:g,modal:j=!0}=a,k=d.useRef(null),l=d.useRef(null),[m,n]=(0,i.i)({prop:e,defaultProp:f??!1,onChange:g,caller:t});return(0,s.jsx)(w,{scope:b,triggerRef:k,contentRef:l,contentId:(0,h.B)(),titleId:(0,h.B)(),descriptionId:(0,h.B)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:c})};y.displayName=t;var z="DialogTrigger",A=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,g=x(z,c),h=(0,f.s)(b,g.triggerRef);return(0,s.jsx)(n.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":g.open,"aria-controls":g.contentId,"data-state":U(g.open),...d,ref:h,onClick:(0,e.m)(a.onClick,g.onOpenToggle)})});A.displayName=z;var B="DialogPortal",[C,D]=u(B,{forceMount:void 0}),E=a=>{let{__scopeDialog:b,forceMount:c,children:e,container:f}=a,g=x(B,b);return(0,s.jsx)(C,{scope:b,forceMount:c,children:d.Children.map(e,a=>(0,s.jsx)(m.C,{present:c||g.open,children:(0,s.jsx)(l.Z,{asChild:!0,container:f,children:a})}))})};E.displayName=B;var F="DialogOverlay",G=d.forwardRef((a,b)=>{let c=D(F,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(F,a.__scopeDialog);return f.modal?(0,s.jsx)(m.C,{present:d||f.open,children:(0,s.jsx)(I,{...e,ref:b})}):null});G.displayName=F;var H=(0,r.TL)("DialogOverlay.RemoveScroll"),I=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(F,c);return(0,s.jsx)(p.A,{as:H,allowPinchZoom:!0,shards:[e.contentRef],children:(0,s.jsx)(n.sG.div,{"data-state":U(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),J="DialogContent",K=d.forwardRef((a,b)=>{let c=D(J,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(J,a.__scopeDialog);return(0,s.jsx)(m.C,{present:d||f.open,children:f.modal?(0,s.jsx)(L,{...e,ref:b}):(0,s.jsx)(M,{...e,ref:b})})});K.displayName=J;var L=d.forwardRef((a,b)=>{let c=x(J,a.__scopeDialog),g=d.useRef(null),h=(0,f.s)(b,c.contentRef,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,q.Eq)(a)},[]),(0,s.jsx)(N,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:(0,e.m)(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault())})}),M=d.forwardRef((a,b)=>{let c=x(J,a.__scopeDialog),e=d.useRef(!1),f=d.useRef(!1);return(0,s.jsx)(N,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(e.current||c.triggerRef.current?.focus(),b.preventDefault()),e.current=!1,f.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(e.current=!0,"pointerdown"===b.detail.originalEvent.type&&(f.current=!0));let d=b.target;c.triggerRef.current?.contains(d)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&f.current&&b.preventDefault()}})}),N=d.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:e,onOpenAutoFocus:g,onCloseAutoFocus:h,...i}=a,l=x(J,c),m=d.useRef(null),n=(0,f.s)(b,m);return(0,o.Oh)(),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.n,{asChild:!0,loop:!0,trapped:e,onMountAutoFocus:g,onUnmountAutoFocus:h,children:(0,s.jsx)(j.qW,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":U(l.open),...i,ref:n,onDismiss:()=>l.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(Y,{titleId:l.titleId}),(0,s.jsx)(Z,{contentRef:m,descriptionId:l.descriptionId})]})]})}),O="DialogTitle",P=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(O,c);return(0,s.jsx)(n.sG.h2,{id:e.titleId,...d,ref:b})});P.displayName=O;var Q="DialogDescription",R=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(Q,c);return(0,s.jsx)(n.sG.p,{id:e.descriptionId,...d,ref:b})});R.displayName=Q;var S="DialogClose",T=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,f=x(S,c);return(0,s.jsx)(n.sG.button,{type:"button",...d,ref:b,onClick:(0,e.m)(a.onClick,()=>f.onOpenChange(!1))})});function U(a){return a?"open":"closed"}T.displayName=S;var V="DialogTitleWarning",[W,X]=(0,g.q)(V,{contentName:J,titleName:O,docsSlug:"dialog"}),Y=({titleId:a})=>{let b=X(V),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return d.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},Z=({contentRef:a,descriptionId:b})=>{let c=X("DialogDescriptionWarning"),e=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return d.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(e))},[e,a,b]),null},$=y,_=A,aa=E,ab=G,ac=K,ad=P,ae=R,af=T},28559:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40228:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41862:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},76926:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(61120));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})}};