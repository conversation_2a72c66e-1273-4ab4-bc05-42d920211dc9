"use strict";exports.id=8321,exports.ids=[8321],exports.modules={3589:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8819:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},13943:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},55146:(a,b,c)=>{c.d(b,{B8:()=>D,UC:()=>F,bL:()=>C,l9:()=>E});var d=c(43210),e=c(70569),f=c(11273),g=c(72942),h=c(46059),i=c(14163),j=c(43),k=c(65551),l=c(96963),m=c(60687),n="Tabs",[o,p]=(0,f.A)(n,[g.RG]),q=(0,g.RG)(),[r,s]=o(n),t=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:e,defaultValue:f,orientation:g="horizontal",dir:h,activationMode:o="automatic",...p}=a,q=(0,j.jH)(h),[s,t]=(0,k.i)({prop:d,onChange:e,defaultProp:f??"",caller:n});return(0,m.jsx)(r,{scope:c,baseId:(0,l.B)(),value:s,onValueChange:t,orientation:g,dir:q,activationMode:o,children:(0,m.jsx)(i.sG.div,{dir:q,"data-orientation":g,...p,ref:b})})});t.displayName=n;var u="TabsList",v=d.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...e}=a,f=s(u,c),h=q(c);return(0,m.jsx)(g.bL,{asChild:!0,...h,orientation:f.orientation,dir:f.dir,loop:d,children:(0,m.jsx)(i.sG.div,{role:"tablist","aria-orientation":f.orientation,...e,ref:b})})});v.displayName=u;var w="TabsTrigger",x=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:f=!1,...h}=a,j=s(w,c),k=q(c),l=A(j.baseId,d),n=B(j.baseId,d),o=d===j.value;return(0,m.jsx)(g.q7,{asChild:!0,...k,focusable:!f,active:o,children:(0,m.jsx)(i.sG.button,{type:"button",role:"tab","aria-selected":o,"aria-controls":n,"data-state":o?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:l,...h,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():j.onValueChange(d)}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&j.onValueChange(d)}),onFocus:(0,e.m)(a.onFocus,()=>{let a="manual"!==j.activationMode;o||f||!a||j.onValueChange(d)})})})});x.displayName=w;var y="TabsContent",z=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,forceMount:f,children:g,...j}=a,k=s(y,c),l=A(k.baseId,e),n=B(k.baseId,e),o=e===k.value,p=d.useRef(o);return d.useEffect(()=>{let a=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,m.jsx)(h.C,{present:f||o,children:({present:c})=>(0,m.jsx)(i.sG.div,{"data-state":o?"active":"inactive","data-orientation":k.orientation,role:"tabpanel","aria-labelledby":l,hidden:!c,id:n,tabIndex:0,...j,ref:b,style:{...a.style,animationDuration:p.current?"0s":void 0},children:c&&g})})});function A(a,b){return`${a}-trigger-${b}`}function B(a,b){return`${a}-content-${b}`}z.displayName=y;var C=t,D=v,E=x,F=z},78148:(a,b,c)=>{c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},78272:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(a,b,c)=>{c.d(b,{Z:()=>e});var d=c(43210);function e(a){let b=d.useRef({value:a,previous:a});return d.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}},90270:(a,b,c)=>{c.d(b,{bL:()=>w,zi:()=>x});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(65551),i=c(83721),j=c(18853),k=c(14163),l=c(60687),m="Switch",[n,o]=(0,g.A)(m),[p,q]=n(m),r=d.forwardRef((a,b)=>{let{__scopeSwitch:c,name:g,checked:i,defaultChecked:j,required:n,disabled:o,value:q="on",onCheckedChange:r,form:s,...t}=a,[w,x]=d.useState(null),y=(0,f.s)(b,a=>x(a)),z=d.useRef(!1),A=!w||s||!!w.closest("form"),[B,C]=(0,h.i)({prop:i,defaultProp:j??!1,onChange:r,caller:m});return(0,l.jsxs)(p,{scope:c,checked:B,disabled:o,children:[(0,l.jsx)(k.sG.button,{type:"button",role:"switch","aria-checked":B,"aria-required":n,"data-state":v(B),"data-disabled":o?"":void 0,disabled:o,value:q,...t,ref:y,onClick:(0,e.m)(a.onClick,a=>{C(a=>!a),A&&(z.current=a.isPropagationStopped(),z.current||a.stopPropagation())})}),A&&(0,l.jsx)(u,{control:w,bubbles:!z.current,name:g,value:q,checked:B,required:n,disabled:o,form:s,style:{transform:"translateX(-100%)"}})]})});r.displayName=m;var s="SwitchThumb",t=d.forwardRef((a,b)=>{let{__scopeSwitch:c,...d}=a,e=q(s,c);return(0,l.jsx)(k.sG.span,{"data-state":v(e.checked),"data-disabled":e.disabled?"":void 0,...d,ref:b})});t.displayName=s;var u=d.forwardRef(({__scopeSwitch:a,control:b,checked:c,bubbles:e=!0,...g},h)=>{let k=d.useRef(null),m=(0,f.s)(k,h),n=(0,i.Z)(c),o=(0,j.X)(b);return d.useEffect(()=>{let a=k.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(n!==c&&b){let d=new Event("click",{bubbles:e});b.call(a,c),a.dispatchEvent(d)}},[n,c,e]),(0,l.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:c,...g,tabIndex:-1,ref:m,style:{...g.style,...o,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function v(a){return a?"checked":"unchecked"}u.displayName="SwitchBubbleInput";var w=r,x=t},97822:(a,b,c)=>{c.d(b,{UC:()=>aJ,YJ:()=>aL,In:()=>aH,q7:()=>aN,VF:()=>aP,p4:()=>aO,JU:()=>aM,ZL:()=>aI,bL:()=>aE,wn:()=>aR,PP:()=>aQ,wv:()=>aS,l9:()=>aF,WT:()=>aG,LM:()=>aK});var d=c(43210),e=c(51215);function f(a,[b,c]){return Math.min(c,Math.max(b,a))}var g=c(70569),h=c(9510),i=c(98599),j=c(11273),k=c(43),l=c(31355),m=c(1359),n=c(32547),o=c(96963),p=c(55509),q=c(25028),r=c(14163),s=c(8730),t=c(13495),u=c(65551),v=c(66156),w=c(83721),x=c(60687),y=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});d.forwardRef((a,b)=>(0,x.jsx)(r.sG.span,{...a,ref:b,style:{...y,...a.style}})).displayName="VisuallyHidden";var z=c(63376),A=c(42247),B=[" ","Enter","ArrowUp","ArrowDown"],C=[" ","Enter"],D="Select",[E,F,G]=(0,h.N)(D),[H,I]=(0,j.A)(D,[G,p.Bk]),J=(0,p.Bk)(),[K,L]=H(D),[M,N]=H(D),O=a=>{let{__scopeSelect:b,children:c,open:e,defaultOpen:f,onOpenChange:g,value:h,defaultValue:i,onValueChange:j,dir:l,name:m,autoComplete:n,disabled:q,required:r,form:s}=a,t=J(b),[v,w]=d.useState(null),[y,z]=d.useState(null),[A,B]=d.useState(!1),C=(0,k.jH)(l),[F,G]=(0,u.i)({prop:e,defaultProp:f??!1,onChange:g,caller:D}),[H,I]=(0,u.i)({prop:h,defaultProp:i,onChange:j,caller:D}),L=d.useRef(null),N=!v||s||!!v.closest("form"),[O,P]=d.useState(new Set),Q=Array.from(O).map(a=>a.props.value).join(";");return(0,x.jsx)(p.bL,{...t,children:(0,x.jsxs)(K,{required:r,scope:b,trigger:v,onTriggerChange:w,valueNode:y,onValueNodeChange:z,valueNodeHasChildren:A,onValueNodeHasChildrenChange:B,contentId:(0,o.B)(),value:H,onValueChange:I,open:F,onOpenChange:G,dir:C,triggerPointerDownPosRef:L,disabled:q,children:[(0,x.jsx)(E.Provider,{scope:b,children:(0,x.jsx)(M,{scope:a.__scopeSelect,onNativeOptionAdd:d.useCallback(a=>{P(b=>new Set(b).add(a))},[]),onNativeOptionRemove:d.useCallback(a=>{P(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),N?(0,x.jsxs)(aA,{"aria-hidden":!0,required:r,tabIndex:-1,name:m,autoComplete:n,value:H,onChange:a=>I(a.target.value),disabled:q,form:s,children:[void 0===H?(0,x.jsx)("option",{value:""}):null,Array.from(O)]},Q):null]})})};O.displayName=D;var P="SelectTrigger",Q=d.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:e=!1,...f}=a,h=J(c),j=L(P,c),k=j.disabled||e,l=(0,i.s)(b,j.onTriggerChange),m=F(c),n=d.useRef("touch"),[o,q,s]=aC(a=>{let b=m().filter(a=>!a.disabled),c=b.find(a=>a.value===j.value),d=aD(b,a,c);void 0!==d&&j.onValueChange(d.value)}),t=a=>{k||(j.onOpenChange(!0),s()),a&&(j.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,x.jsx)(p.Mz,{asChild:!0,...h,children:(0,x.jsx)(r.sG.button,{type:"button",role:"combobox","aria-controls":j.contentId,"aria-expanded":j.open,"aria-required":j.required,"aria-autocomplete":"none",dir:j.dir,"data-state":j.open?"open":"closed",disabled:k,"data-disabled":k?"":void 0,"data-placeholder":aB(j.value)?"":void 0,...f,ref:l,onClick:(0,g.m)(f.onClick,a=>{a.currentTarget.focus(),"mouse"!==n.current&&t(a)}),onPointerDown:(0,g.m)(f.onPointerDown,a=>{n.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(t(a),a.preventDefault())}),onKeyDown:(0,g.m)(f.onKeyDown,a=>{let b=""!==o.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||q(a.key),(!b||" "!==a.key)&&B.includes(a.key)&&(t(),a.preventDefault())})})})});Q.displayName=P;var R="SelectValue",S=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:g="",...h}=a,j=L(R,c),{onValueNodeHasChildrenChange:k}=j,l=void 0!==f,m=(0,i.s)(b,j.onValueNodeChange);return(0,v.N)(()=>{k(l)},[k,l]),(0,x.jsx)(r.sG.span,{...h,ref:m,style:{pointerEvents:"none"},children:aB(j.value)?(0,x.jsx)(x.Fragment,{children:g}):f})});S.displayName=R;var T=d.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,x.jsx)(r.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});T.displayName="SelectIcon";var U=a=>(0,x.jsx)(q.Z,{asChild:!0,...a});U.displayName="SelectPortal";var V="SelectContent",W=d.forwardRef((a,b)=>{let c=L(V,a.__scopeSelect),[f,g]=d.useState();return((0,v.N)(()=>{g(new DocumentFragment)},[]),c.open)?(0,x.jsx)($,{...a,ref:b}):f?e.createPortal((0,x.jsx)(X,{scope:a.__scopeSelect,children:(0,x.jsx)(E.Slot,{scope:a.__scopeSelect,children:(0,x.jsx)("div",{children:a.children})})}),f):null});W.displayName=V;var[X,Y]=H(V),Z=(0,s.TL)("SelectContent.RemoveScroll"),$=d.forwardRef((a,b)=>{let{__scopeSelect:c,position:e="item-aligned",onCloseAutoFocus:f,onEscapeKeyDown:h,onPointerDownOutside:j,side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w,...y}=a,B=L(V,c),[C,D]=d.useState(null),[E,G]=d.useState(null),H=(0,i.s)(b,a=>D(a)),[I,J]=d.useState(null),[K,M]=d.useState(null),N=F(c),[O,P]=d.useState(!1),Q=d.useRef(!1);d.useEffect(()=>{if(C)return(0,z.Eq)(C)},[C]),(0,m.Oh)();let R=d.useCallback(a=>{let[b,...c]=N().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&E&&(E.scrollTop=0),c===d&&E&&(E.scrollTop=E.scrollHeight),c?.focus(),document.activeElement!==e))return},[N,E]),S=d.useCallback(()=>R([I,C]),[R,I,C]);d.useEffect(()=>{O&&S()},[O,S]);let{onOpenChange:T,triggerPointerDownPosRef:U}=B;d.useEffect(()=>{if(C){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(U.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():C.contains(c.target)||T(!1),document.removeEventListener("pointermove",b),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[C,T,U]),d.useEffect(()=>{let a=()=>T(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[T]);let[W,Y]=aC(a=>{let b=N().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=aD(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),$=d.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&(J(a),d&&(Q.current=!0))},[B.value]),ab=d.useCallback(()=>C?.focus(),[C]),ac=d.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&M(a)},[B.value]),ad="popper"===e?aa:_,ae=ad===aa?{side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w}:{};return(0,x.jsx)(X,{scope:c,content:C,viewport:E,onViewportChange:G,itemRefCallback:$,selectedItem:I,onItemLeave:ab,itemTextRefCallback:ac,focusSelectedItem:S,selectedItemText:K,position:e,isPositioned:O,searchRef:W,children:(0,x.jsx)(A.A,{as:Z,allowPinchZoom:!0,children:(0,x.jsx)(n.n,{asChild:!0,trapped:B.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,g.m)(f,a=>{B.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,x.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:h,onPointerDownOutside:j,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>B.onOpenChange(!1),children:(0,x.jsx)(ad,{role:"listbox",id:B.contentId,"data-state":B.open?"open":"closed",dir:B.dir,onContextMenu:a=>a.preventDefault(),...y,...ae,onPlaced:()=>P(!0),ref:H,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:(0,g.m)(y.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||Y(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=N().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>R(b)),a.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var _=d.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:e,...g}=a,h=L(V,c),j=Y(V,c),[k,l]=d.useState(null),[m,n]=d.useState(null),o=(0,i.s)(b,a=>n(a)),p=F(c),q=d.useRef(!1),s=d.useRef(!0),{viewport:t,selectedItem:u,selectedItemText:w,focusSelectedItem:y}=j,z=d.useCallback(()=>{if(h.trigger&&h.valueNode&&k&&m&&t&&u&&w){let a=h.trigger.getBoundingClientRect(),b=m.getBoundingClientRect(),c=h.valueNode.getBoundingClientRect(),d=w.getBoundingClientRect();if("rtl"!==h.dir){let e=d.left-b.left,g=c.left-e,h=a.left-g,i=a.width+h,j=Math.max(i,b.width),l=f(g,[10,Math.max(10,window.innerWidth-10-j)]);k.style.minWidth=i+"px",k.style.left=l+"px"}else{let e=b.right-d.right,g=window.innerWidth-c.right-e,h=window.innerWidth-a.right-g,i=a.width+h,j=Math.max(i,b.width),l=f(g,[10,Math.max(10,window.innerWidth-10-j)]);k.style.minWidth=i+"px",k.style.right=l+"px"}let g=p(),i=window.innerHeight-20,j=t.scrollHeight,l=window.getComputedStyle(m),n=parseInt(l.borderTopWidth,10),o=parseInt(l.paddingTop,10),r=parseInt(l.borderBottomWidth,10),s=n+o+j+parseInt(l.paddingBottom,10)+r,v=Math.min(5*u.offsetHeight,s),x=window.getComputedStyle(t),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=u.offsetHeight/2,C=n+o+(u.offsetTop+B);if(C<=A){let a=g.length>0&&u===g[g.length-1].ref.current;k.style.bottom="0px";let b=Math.max(i-A,B+(a?z:0)+(m.clientHeight-t.offsetTop-t.offsetHeight)+r);k.style.height=C+b+"px"}else{let a=g.length>0&&u===g[0].ref.current;k.style.top="0px";let b=Math.max(A,n+t.offsetTop+(a?y:0)+B);k.style.height=b+(s-C)+"px",t.scrollTop=C-A+t.offsetTop}k.style.margin="10px 0",k.style.minHeight=v+"px",k.style.maxHeight=i+"px",e?.(),requestAnimationFrame(()=>q.current=!0)}},[p,h.trigger,h.valueNode,k,m,t,u,w,h.dir,e]);(0,v.N)(()=>z(),[z]);let[A,B]=d.useState();(0,v.N)(()=>{m&&B(window.getComputedStyle(m).zIndex)},[m]);let C=d.useCallback(a=>{a&&!0===s.current&&(z(),y?.(),s.current=!1)},[z,y]);return(0,x.jsx)(ab,{scope:c,contentWrapper:k,shouldExpandOnScrollRef:q,onScrollButtonChange:C,children:(0,x.jsx)("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:A},children:(0,x.jsx)(r.sG.div,{...g,ref:o,style:{boxSizing:"border-box",maxHeight:"100%",...g.style}})})})});_.displayName="SelectItemAlignedPosition";var aa=d.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,g=J(c);return(0,x.jsx)(p.UC,{...g,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});aa.displayName="SelectPopperPosition";var[ab,ac]=H(V,{}),ad="SelectViewport",ae=d.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:e,...f}=a,h=Y(ad,c),j=ac(ad,c),k=(0,i.s)(b,h.onViewportChange),l=d.useRef(0);return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:e}),(0,x.jsx)(E.Slot,{scope:c,children:(0,x.jsx)(r.sG.div,{"data-radix-select-viewport":"",role:"presentation",...f,ref:k,style:{position:"relative",flex:1,overflow:"hidden auto",...f.style},onScroll:(0,g.m)(f.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=j;if(d?.current&&c){let a=Math.abs(l.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}l.current=b.scrollTop})})})]})});ae.displayName=ad;var af="SelectGroup",[ag,ah]=H(af),ai=d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=(0,o.B)();return(0,x.jsx)(ag,{scope:c,id:e,children:(0,x.jsx)(r.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})});ai.displayName=af;var aj="SelectLabel",ak=d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=ah(aj,c);return(0,x.jsx)(r.sG.div,{id:e.id,...d,ref:b})});ak.displayName=aj;var al="SelectItem",[am,an]=H(al),ao=d.forwardRef((a,b)=>{let{__scopeSelect:c,value:e,disabled:f=!1,textValue:h,...j}=a,k=L(al,c),l=Y(al,c),m=k.value===e,[n,p]=d.useState(h??""),[q,s]=d.useState(!1),t=(0,i.s)(b,a=>l.itemRefCallback?.(a,e,f)),u=(0,o.B)(),v=d.useRef("touch"),w=()=>{f||(k.onValueChange(e),k.onOpenChange(!1))};if(""===e)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,x.jsx)(am,{scope:c,value:e,disabled:f,textId:u,isSelected:m,onItemTextChange:d.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,x.jsx)(E.ItemSlot,{scope:c,value:e,disabled:f,textValue:n,children:(0,x.jsx)(r.sG.div,{role:"option","aria-labelledby":u,"data-highlighted":q?"":void 0,"aria-selected":m&&q,"data-state":m?"checked":"unchecked","aria-disabled":f||void 0,"data-disabled":f?"":void 0,tabIndex:f?void 0:-1,...j,ref:t,onFocus:(0,g.m)(j.onFocus,()=>s(!0)),onBlur:(0,g.m)(j.onBlur,()=>s(!1)),onClick:(0,g.m)(j.onClick,()=>{"mouse"!==v.current&&w()}),onPointerUp:(0,g.m)(j.onPointerUp,()=>{"mouse"===v.current&&w()}),onPointerDown:(0,g.m)(j.onPointerDown,a=>{v.current=a.pointerType}),onPointerMove:(0,g.m)(j.onPointerMove,a=>{v.current=a.pointerType,f?l.onItemLeave?.():"mouse"===v.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,g.m)(j.onPointerLeave,a=>{a.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:(0,g.m)(j.onKeyDown,a=>{(l.searchRef?.current===""||" "!==a.key)&&(C.includes(a.key)&&w()," "===a.key&&a.preventDefault())})})})})});ao.displayName=al;var ap="SelectItemText",aq=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:f,style:g,...h}=a,j=L(ap,c),k=Y(ap,c),l=an(ap,c),m=N(ap,c),[n,o]=d.useState(null),p=(0,i.s)(b,a=>o(a),l.onItemTextChange,a=>k.itemTextRefCallback?.(a,l.value,l.disabled)),q=n?.textContent,s=d.useMemo(()=>(0,x.jsx)("option",{value:l.value,disabled:l.disabled,children:q},l.value),[l.disabled,l.value,q]),{onNativeOptionAdd:t,onNativeOptionRemove:u}=m;return(0,v.N)(()=>(t(s),()=>u(s)),[t,u,s]),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(r.sG.span,{id:l.textId,...h,ref:p}),l.isSelected&&j.valueNode&&!j.valueNodeHasChildren?e.createPortal(h.children,j.valueNode):null]})});aq.displayName=ap;var ar="SelectItemIndicator",as=d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return an(ar,c).isSelected?(0,x.jsx)(r.sG.span,{"aria-hidden":!0,...d,ref:b}):null});as.displayName=ar;var at="SelectScrollUpButton",au=d.forwardRef((a,b)=>{let c=Y(at,a.__scopeSelect),e=ac(at,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,v.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){g(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,x.jsx)(ax,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});au.displayName=at;var av="SelectScrollDownButton",aw=d.forwardRef((a,b)=>{let c=Y(av,a.__scopeSelect),e=ac(av,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,v.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;g(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,x.jsx)(ax,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});aw.displayName=av;var ax=d.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:e,...f}=a,h=Y("SelectScrollButton",c),i=d.useRef(null),j=F(c),k=d.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return d.useEffect(()=>()=>k(),[k]),(0,v.N)(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,x.jsx)(r.sG.div,{"aria-hidden":!0,...f,ref:b,style:{flexShrink:0,...f.style},onPointerDown:(0,g.m)(f.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(e,50))}),onPointerMove:(0,g.m)(f.onPointerMove,()=>{h.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(e,50))}),onPointerLeave:(0,g.m)(f.onPointerLeave,()=>{k()})})}),ay=d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,x.jsx)(r.sG.div,{"aria-hidden":!0,...d,ref:b})});ay.displayName="SelectSeparator";var az="SelectArrow";d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=J(c),f=L(az,c),g=Y(az,c);return f.open&&"popper"===g.position?(0,x.jsx)(p.i3,{...e,...d,ref:b}):null}).displayName=az;var aA=d.forwardRef(({__scopeSelect:a,value:b,...c},e)=>{let f=d.useRef(null),g=(0,i.s)(e,f),h=(0,w.Z)(b);return d.useEffect(()=>{let a=f.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(h!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[h,b]),(0,x.jsx)(r.sG.select,{...c,style:{...y,...c.style},ref:g,defaultValue:b})});function aB(a){return""===a||void 0===a}function aC(a){let b=(0,t.c)(a),c=d.useRef(""),e=d.useRef(0),f=d.useCallback(a=>{let d=c.current+a;b(d),function a(b){c.current=b,window.clearTimeout(e.current),""!==b&&(e.current=window.setTimeout(()=>a(""),1e3))}(d)},[b]),g=d.useCallback(()=>{c.current="",window.clearTimeout(e.current)},[]);return d.useEffect(()=>()=>window.clearTimeout(e.current),[]),[c,f,g]}function aD(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}aA.displayName="SelectBubbleInput";var aE=O,aF=Q,aG=S,aH=T,aI=U,aJ=W,aK=ae,aL=ai,aM=ak,aN=ao,aO=aq,aP=as,aQ=au,aR=aw,aS=ay}};