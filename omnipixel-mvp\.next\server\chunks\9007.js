exports.id=9007,exports.ids=[9007],exports.modules={163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(71042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6053:a=>{var b=/\s/;a.exports=function(a){for(var c=a.length;c--&&b.test(a.charAt(c)););return c}},6120:a=>{function b(a,b){"boolean"==typeof b&&(b={forever:b}),this._originalTimeouts=JSON.parse(JSON.stringify(a)),this._timeouts=a,this._options=b||{},this._maxRetryTime=b&&b.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}a.exports=b,b.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)},b.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null},b.prototype.retry=function(a){if(this._timeout&&clearTimeout(this._timeout),!a)return!1;var b=new Date().getTime();if(a&&b-this._operationStart>=this._maxRetryTime)return this._errors.push(a),this._errors.unshift(Error("RetryOperation timeout occurred")),!1;this._errors.push(a);var c=this._timeouts.shift();if(void 0===c)if(!this._cachedTimeouts)return!1;else this._errors.splice(0,this._errors.length-1),c=this._cachedTimeouts.slice(-1);var d=this;return this._timer=setTimeout(function(){d._attempts++,d._operationTimeoutCb&&(d._timeout=setTimeout(function(){d._operationTimeoutCb(d._attempts)},d._operationTimeout),d._options.unref&&d._timeout.unref()),d._fn(d._attempts)},c),this._options.unref&&this._timer.unref(),!0},b.prototype.attempt=function(a,b){this._fn=a,b&&(b.timeout&&(this._operationTimeout=b.timeout),b.cb&&(this._operationTimeoutCb=b.cb));var c=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){c._operationTimeoutCb()},c._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)},b.prototype.try=function(a){console.log("Using RetryOperation.try() is deprecated"),this.attempt(a)},b.prototype.start=function(a){console.log("Using RetryOperation.start() is deprecated"),this.attempt(a)},b.prototype.start=b.prototype.try,b.prototype.errors=function(){return this._errors},b.prototype.attempts=function(){return this._attempts},b.prototype.mainError=function(){if(0===this._errors.length)return null;for(var a={},b=null,c=0,d=0;d<this._errors.length;d++){var e=this._errors[d],f=e.message,g=(a[f]||0)+1;a[f]=g,g>=c&&(b=e,c=g)}return b}},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10663:a=>{a.exports="object"==typeof global&&global&&global.Object===Object&&global},11539:(a,b,c)=>{var d=c(37643),e=c(55048),f=c(49227),g=0/0,h=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,j=/^0o[0-7]+$/i,k=parseInt;a.exports=function(a){if("number"==typeof a)return a;if(f(a))return g;if(e(a)){var b="function"==typeof a.valueOf?a.valueOf():a;a=e(b)?b+"":b}if("string"!=typeof a)return 0===a?a:+a;a=d(a);var c=i.test(a);return c||j.test(a)?k(a.slice(2),c?2:8):h.test(a)?g:+a}},14024:(a,b,c)=>{a.exports=c(46810)},16023:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},17535:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},20540:(a,b,c)=>{var d=c(55048),e=c(70151),f=c(11539),g=Math.max,h=Math.min;a.exports=function(a,b,c){var i,j,k,l,m,n,o=0,p=!1,q=!1,r=!0;if("function"!=typeof a)throw TypeError("Expected a function");function s(b){var c=i,d=j;return i=j=void 0,o=b,l=a.apply(d,c)}function t(a){var c=a-n,d=a-o;return void 0===n||c>=b||c<0||q&&d>=k}function u(){var a,c,d,f=e();if(t(f))return v(f);m=setTimeout(u,(a=f-n,c=f-o,d=b-a,q?h(d,k-c):d))}function v(a){return(m=void 0,r&&i)?s(a):(i=j=void 0,l)}function w(){var a,c=e(),d=t(c);if(i=arguments,j=this,n=c,d){if(void 0===m)return o=a=n,m=setTimeout(u,b),p?s(a):l;if(q)return clearTimeout(m),m=setTimeout(u,b),s(n)}return void 0===m&&(m=setTimeout(u,b)),l}return b=f(b)||0,d(c)&&(p=!!c.leading,k=(q="maxWait"in c)?g(f(c.maxWait)||0,b):k,r="trailing"in c?!!c.trailing:r),w.cancel=function(){void 0!==m&&clearTimeout(m),o=0,i=n=j=m=void 0},w.flush=function(){return void 0===m?l:v(e())},w}},25177:(a,b,c)=>{"use strict";c.d(b,{C1:()=>v,bL:()=>u});var d=c(43210),e=c(11273),f=c(14163),g=c(60687),h="Progress",[i,j]=(0,e.A)(h),[k,l]=i(h),m=d.forwardRef((a,b)=>{var c,d;let{__scopeProgress:e,value:h=null,max:i,getValueLabel:j=p,...l}=a;(i||0===i)&&!s(i)&&console.error((c=`${i}`,`Invalid prop \`max\` of value \`${c}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=s(i)?i:100;null===h||t(h,m)||console.error((d=`${h}`,`Invalid prop \`value\` of value \`${d}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let n=t(h,m)?h:null,o=r(n)?j(n,m):void 0;return(0,g.jsx)(k,{scope:e,value:n,max:m,children:(0,g.jsx)(f.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":r(n)?n:void 0,"aria-valuetext":o,role:"progressbar","data-state":q(n,m),"data-value":n??void 0,"data-max":m,...l,ref:b})})});m.displayName=h;var n="ProgressIndicator",o=d.forwardRef((a,b)=>{let{__scopeProgress:c,...d}=a,e=l(n,c);return(0,g.jsx)(f.sG.div,{"data-state":q(e.value,e.max),"data-value":e.value??void 0,"data-max":e.max,...d,ref:b})});function p(a,b){return`${Math.round(a/b*100)}%`}function q(a,b){return null==a?"indeterminate":a===b?"complete":"loading"}function r(a){return"number"==typeof a}function s(a){return r(a)&&!isNaN(a)&&a>0}function t(a,b){return r(a)&&!isNaN(a)&&a<=b&&a>=0}o.displayName=n;var u=m,v=o},25834:a=>{a.exports=function(){var a={},b=a._fns={};return a.emit=function(a,c,d,e,f,g,h){var i=function(a){for(var c=b[a]?b[a]:[],d=a.indexOf(":"),e=-1===d?[a]:[a.substring(0,d),a.substring(d+1)],f=Object.keys(b),g=0,h=f.length;g<h;g++){var i=f[g];if("*"===i&&(c=c.concat(b[i])),2===e.length&&e[0]===i){c=c.concat(b[i]);break}}return c}(a);i.length&&function(a,b,c){for(var d=0,e=b.length;d<e&&b[d];d++)b[d].event=a,b[d].apply(b[d],c)}(a,i,[c,d,e,f,g,h])},a.on=function(a,c){b[a]||(b[a]=[]),b[a].push(c)},a.once=function(b,c){this.on(b,function d(){c.apply(this,arguments),a.off(b,d)})},a.off=function(a,b){var c=[];if(a&&b)for(var d=this._fns[a],e=0,f=d?d.length:0;e<f;e++)d[e]!==b&&c.push(d[e]);c.length?this._fns[a]=c:delete this._fns[a]},a}},27467:a=>{a.exports=function(a){return null!=a&&"object"==typeof a}},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29395:(a,b,c)=>{var d=c(79474),e=c(70222),f=c(84713),g=d?d.toStringTag:void 0;a.exports=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":g&&g in Object(a)?e(a):f(a)}},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},36058:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},37643:(a,b,c)=>{var d=c(6053),e=/^\s+/;a.exports=function(a){return a?a.slice(0,d(a)+1).replace(e,""):a}},39440:a=>{"use strict";function b(a,b){this.text=a=a||"",this.hasWild=~a.indexOf("*"),this.separator=b,this.parts=a.split(b)}b.prototype.match=function(a){var b,c,d=!0,e=this.parts,f=e.length;if("string"==typeof a||a instanceof String)if(this.hasWild||this.text==a){for(b=0,c=(a||"").split(this.separator);d&&b<f;b++)if("*"===e[b])continue;else d=b<c.length&&e[b]===c[b];d=d&&c}else d=!1;else if("function"==typeof a.splice)for(d=[],b=a.length;b--;)this.match(a[b])&&(d[d.length]=a[b]);else if("object"==typeof a)for(var g in d={},a)this.match(g)&&(d[g]=a[g]);return d},a.exports=function(a,c,d){var e=new b(a,d||/[\/\.]/);return void 0!==c?e.match(c):e}},39916:(a,b,c)=>{"use strict";var d=c(97576);c.o(d,"notFound")&&c.d(b,{notFound:function(){return d.notFound}}),c.o(d,"redirect")&&c.d(b,{redirect:function(){return d.redirect}})},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41236:(a,b,c)=>{"use strict";c.d(b,{A:()=>bx});var d=c(93028);class e{constructor(a,b){this.uppy=a,this.opts=null!=b?b:{}}getPluginState(){let{plugins:a}=this.uppy.getState();return(null==a?void 0:a[this.id])||{}}setPluginState(a){let{plugins:b}=this.uppy.getState();this.uppy.setState({plugins:{...b,[this.id]:{...b[this.id],...a}}})}setOptions(a){this.opts={...this.opts,...a},this.setPluginState(void 0),this.i18nInit()}i18nInit(){let a=new d.A([this.defaultLocale,this.uppy.locale,this.opts.locale]);this.i18n=a.translate.bind(a),this.i18nArray=a.translateArray.bind(a),this.setPluginState(void 0)}addTarget(a){throw Error("Extend the addTarget method to add your plugin to another plugin's target")}install(){}uninstall(){}update(a){}afterUpdate(){}}class f extends Error{constructor(){super(...arguments),this.name="UserFacingApiError"}}var g=c(14024);let h=Object.prototype.toString,i=new Set(["network error","Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Load failed","Network request failed","fetch failed","terminated"]);class j extends Error{constructor(a){super(),a instanceof Error?(this.originalError=a,{message:a}=a):(this.originalError=Error(a),this.originalError.stack=this.stack),this.name="AbortError",this.message=a}}let k=(a,b,c)=>{let d=c.retries-(b-1);return a.attemptNumber=b,a.retriesLeft=d,a};async function l(a,b){return new Promise((c,d)=>{b={...b},b.onFailedAttempt??=()=>{},b.shouldRetry??=()=>!0,b.retries??=10;let e=g.operation(b),f=()=>{e.stop(),d(b.signal?.reason)};b.signal&&!b.signal.aborted&&b.signal.addEventListener("abort",f,{once:!0});let l=()=>{b.signal?.removeEventListener("abort",f),e.stop()};e.attempt(async f=>{try{let b=await a(f);l(),c(b)}catch(a){try{if(!(a instanceof Error))throw TypeError(`Non-error was thrown: "${a}". You should only throw errors.`);if(a instanceof j)throw a.originalError;if(a instanceof TypeError&&!(a&&"[object Error]"===h.call(a)&&"TypeError"===a.name&&"string"==typeof a.message&&("Load failed"===a.message?void 0===a.stack:i.has(a.message))))throw a;if(k(a,f,b),await b.shouldRetry(a)||(e.stop(),d(a)),await b.onFailedAttempt(a),!e.retry(a))throw e.mainError()}catch(a){k(a,f,b),l(),d(a)}}})})}class m extends Error{constructor(a,b){void 0===b&&(b=null),super("This looks like a network error, the endpoint might be blocked by an internet provider or a firewall."),this.cause=a,this.isNetworkError=!0,this.request=b}}function n(a,b){return Object.prototype.hasOwnProperty.call(a,b)}class o extends Error{constructor(a,b){super(a),this.cause=null==b?void 0:b.cause,this.cause&&n(this.cause,"isNetworkError")?this.isNetworkError=this.cause.isNetworkError:this.isNetworkError=!1}}class p extends Error{constructor(){super("Authorization required"),this.name="AuthError",this.isAuthError=!0}}function q(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var r=0;function s(a){return"__private_"+r+++"_"+a}class t extends Error{constructor(a){let{statusCode:b,message:c}=a;super(c),this.name="HttpError",this.statusCode=b}}async function u(a){let b;if(401===a.status)throw new p;if(a.ok)return a.json();let c=`Failed request with status: ${a.status}. ${a.statusText}`;try{(b=await a.json()).message&&(c=`${c} message: ${b.message}`),b.requestId&&(c=`${c} request-Id: ${b.requestId}`)}catch(a){throw Error(c,{cause:a})}if(a.status>=400&&a.status<=499&&b.message)throw new f(b.message);throw new t({statusCode:a.status,message:c})}var v=s("companionHeaders"),w=s("getUrl"),x=s("requestSocketToken"),y=s("awaitRemoteFileUpload");class z{constructor(a,b){Object.defineProperty(this,y,{value:B}),Object.defineProperty(this,w,{value:A}),Object.defineProperty(this,v,{writable:!0,value:void 0}),Object.defineProperty(this,x,{writable:!0,value:async a=>{var b;let{file:c,postBody:d,signal:e}=a;if((null==(b=c.remote)?void 0:b.url)==null)throw Error("Cannot connect to an undefined URL");return(await this.post(c.remote.url,{...c.remote.body,...d},{signal:e})).token}}),this.uppy=a,this.opts=b,this.onReceiveResponse=this.onReceiveResponse.bind(this),q(this,v)[v]=b.companionHeaders}setCompanionHeaders(a){q(this,v)[v]=a}[Symbol.for("uppy test: getCompanionHeaders")](){return q(this,v)[v]}get hostname(){let{companion:a}=this.uppy.getState(),b=this.opts.companionUrl;return(a&&a[b]?a[b]:b).replace(/\/$/,"")}async headers(a){return void 0===a&&(a=!1),{Accept:"application/json",...a?void 0:{"Content-Type":"application/json"},...q(this,v)[v]}}onReceiveResponse(a){let{headers:b}=a,c=this.uppy.getState().companion||{},d=this.opts.companionUrl;b.has("i-am")&&b.get("i-am")!==c[d]&&this.uppy.setState({companion:{...c,[d]:b.get("i-am")}})}async request(a){let{path:b,method:c="GET",data:d,skipPostResponse:e,signal:f}=a;try{let a=await this.headers(!d),g=await function(){return fetch(...arguments).catch(a=>{if("AbortError"===a.name)throw a;throw new m(a)})}(q(this,w)[w](b),{method:c,signal:f,headers:a,credentials:this.opts.companionCookiesRule||"same-origin",body:d?JSON.stringify(d):null});return e||this.onReceiveResponse(g),await u(g)}catch(a){if(a.isAuthError||"UserFacingApiError"===a.name||"AbortError"===a.name)throw a;throw new o(`Could not ${c} ${q(this,w)[w](b)}`,{cause:a})}}async get(a,b){return this.request({...b,path:a})}async post(a,b,c){return this.request({...c,path:a,method:"POST",data:b})}async delete(a,b,c){return this.request({...c,path:a,method:"DELETE",data:b})}async uploadRemoteFile(a,b,c){var d=this;try{let{signal:e,getQueue:f}=c||{};return await l(async()=>{var c;let g=null==(c=this.uppy.getFile(a.id))?void 0:c.serverToken;if(null!=g)return this.uppy.log(`Connecting to exiting websocket ${g}`),q(this,y)[y]({file:a,queue:f(),signal:e});let h=f().wrapPromiseFunction(async function(){try{return await q(d,x)[x](...arguments)}catch(b){if(b.isAuthError)throw new j(b);if(null==b.cause)throw b;let a=b.cause;if("HttpError"===a.name&&!([408,409,429,418,423].includes(a.statusCode)||a.statusCode>=500&&a.statusCode<=599&&![501,505].includes(a.statusCode)))throw new j(a);throw a}},{priority:-1}),i=await h({file:a,postBody:b,signal:e}).abortOn(e);if(this.uppy.getFile(a.id))return this.uppy.setFileState(a.id,{serverToken:i}),q(this,y)[y]({file:this.uppy.getFile(a.id),queue:f(),signal:e})},{retries:10,signal:e,onFailedAttempt:a=>this.uppy.log(`Retrying upload due to: ${a.message}`,"warning")})}catch(b){if("AbortError"===b.name)return;throw this.uppy.emit("upload-error",a,b),b}}}function A(a){return/^(https?:|)\/\//.test(a)?a:`${this.hostname}/${a}`}async function B(a){let b,{file:c,queue:d,signal:e}=a,{capabilities:f}=this.uppy.getState();try{return await new Promise((a,g)=>{let h,i,j,k=c.serverToken,m=function(a){var b;let c=null==(b=/^(?:https?:\/\/|\/\/)?(?:[^@\n]+@)?([^\n]+)/i.exec(a))?void 0:b[1],d=/^http:\/\//i.test(a)?"ws":"wss";return`${d}://${c}`}(c.remote.companionUrl),{isPaused:n}=c,o=(a,b)=>{if(null==h||h.readyState!==h.OPEN){var d;this.uppy.log(`Cannot send "${a}" to socket ${c.id} because the socket state was ${String(null==(d=h)?void 0:d.readyState)}`,"warning");return}h.send(JSON.stringify({action:a,payload:null!=b?b:{}}))};function p(){f.resumableUploads&&(n?o("pause"):o("resume"))}let q=async()=>{i&&i.abort(),i=new AbortController;let b=a=>{var b;this.uppy.setFileState(c.id,{serverToken:null}),null==(b=i)||null==b.abort||b.abort(),g(a)};function e(){clearTimeout(j),n||(j=setTimeout(()=>b(Error("Timeout waiting for message from Companion socket")),3e5))}try{await d.wrapPromiseFunction(async()=>{let d=async()=>new Promise((d,f)=>{h=new WebSocket(`${m}/api/${k}`),e(),h.addEventListener("close",()=>{h=void 0,f(Error("Socket closed unexpectedly"))}),h.addEventListener("error",a=>{var b;this.uppy.log(`Companion socket error ${JSON.stringify(a)}, closing socket`,"warning"),null==(b=h)||b.close()}),h.addEventListener("open",()=>{p()}),h.addEventListener("message",d=>{e();try{let{action:b,payload:e}=JSON.parse(d.data);switch(b){case"progress":!function(a,b,c){let{progress:d,bytesUploaded:e,bytesTotal:f}=b;if(d){var g;a.uppy.log(`Upload progress: ${d}`),a.uppy.emit("upload-progress",c,{uploadStarted:null!=(g=c.progress.uploadStarted)?g:0,bytesUploaded:e,bytesTotal:f})}}(this,e,this.uppy.getFile(c.id));break;case"success":{var f,g,h,j;let b=null==(f=e.response)?void 0:f.responseText;this.uppy.emit("upload-success",this.uppy.getFile(c.id),{uploadURL:e.url,status:null!=(g=null==(h=e.response)?void 0:h.status)?g:200,body:b?JSON.parse(b):void 0}),null==(j=i)||null==j.abort||j.abort(),a();break}case"error":{let{message:a}=e.error;throw Object.assign(Error(a),{cause:e.error})}default:this.uppy.log(`Companion socket unknown action ${b}`,"warning")}}catch(a){b(a)}}),i.signal.addEventListener("abort",()=>{this.uppy.log(`Closing socket ${c.id}`),clearTimeout(j),h&&h.close(),h=void 0})});await l(d,{retries:10,signal:i.signal,onFailedAttempt:()=>{i.signal.aborted||this.uppy.log(`Retrying websocket ${c.id}`)}})})().abortOn(i.signal)}catch(a){if(i.signal.aborted)return;b(a)}},r=a=>{f.resumableUploads&&(n=a,h&&p())},s=b=>{var d;f.individualCancellation&&b.id===c.id&&(o("cancel"),null==(d=i)||null==d.abort||d.abort(),this.uppy.log(`upload ${c.id} was removed`),a())},t=()=>{var b;o("cancel"),null==(b=i)||null==b.abort||b.abort(),this.uppy.log(`upload ${c.id} was canceled`),a()},u=(a,b)=>{(null==a?void 0:a.id)===c.id&&r(b)},v=()=>r(!0),w=()=>r(!1);this.uppy.on("file-removed",s),this.uppy.on("cancel-all",t),this.uppy.on("upload-pause",u),this.uppy.on("pause-all",v),this.uppy.on("resume-all",w),b=()=>{this.uppy.off("file-removed",s),this.uppy.off("cancel-all",t),this.uppy.off("upload-pause",u),this.uppy.off("pause-all",v),this.uppy.off("resume-all",w)},e.addEventListener("abort",()=>{var a;null==(a=i)||a.abort()}),q()})}finally{null==b||b()}}z.VERSION="4.4.2";function C(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var D=0;function E(a){return"__private_"+D+++"_"+a}var F=E("uppy"),G=E("events");class H{constructor(a){Object.defineProperty(this,F,{writable:!0,value:void 0}),Object.defineProperty(this,G,{writable:!0,value:[]}),C(this,F)[F]=a}on(a,b){return C(this,G)[G].push([a,b]),C(this,F)[F].on(a,b)}remove(){for(let[a,b]of C(this,G)[G].splice(0))C(this,F)[F].off(a,b)}onFilePause(a,b){this.on("upload-pause",(c,d)=>{a===(null==c?void 0:c.id)&&b(d)})}onFileRemove(a,b){this.on("file-removed",c=>{a===c.id&&b(c.id)})}onPause(a,b){this.on("upload-pause",(c,d)=>{a===(null==c?void 0:c.id)&&b(d)})}onRetry(a,b){this.on("upload-retry",c=>{a===(null==c?void 0:c.id)&&b()})}onRetryAll(a,b){this.on("retry-all",()=>{C(this,F)[F].getFile(a)&&b()})}onPauseAll(a,b){this.on("pause-all",()=>{C(this,F)[F].getFile(a)&&b()})}onCancelAll(a,b){var c=this;this.on("cancel-all",function(){C(c,F)[F].getFile(a)&&b(...arguments)})}onResumeAll(a,b){this.on("resume-all",()=>{C(this,F)[F].getFile(a)&&b()})}}function I(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var J=0;function K(a){return"__private_"+J+++"_"+a}function L(a){if(null!=a){var b;let c=()=>this.abort(a.reason);a.addEventListener("abort",c,{once:!0});let d=()=>{a.removeEventListener("abort",c)};null==(b=this.then)||b.call(this,d,d)}return this}var M=K("activeRequests"),N=K("queuedHandlers"),O=K("paused"),P=K("pauseTimer"),Q=K("downLimit"),R=K("upperLimit"),S=K("rateLimitingTimer"),T=K("call"),U=K("queueNext"),V=K("next"),W=K("queue"),X=K("dequeue"),Y=K("resume"),Z=K("increaseLimit");class ${constructor(a){Object.defineProperty(this,X,{value:ad}),Object.defineProperty(this,W,{value:ac}),Object.defineProperty(this,V,{value:ab}),Object.defineProperty(this,U,{value:aa}),Object.defineProperty(this,T,{value:_}),Object.defineProperty(this,M,{writable:!0,value:0}),Object.defineProperty(this,N,{writable:!0,value:[]}),Object.defineProperty(this,O,{writable:!0,value:!1}),Object.defineProperty(this,P,{writable:!0,value:void 0}),Object.defineProperty(this,Q,{writable:!0,value:1}),Object.defineProperty(this,R,{writable:!0,value:void 0}),Object.defineProperty(this,S,{writable:!0,value:void 0}),Object.defineProperty(this,Y,{writable:!0,value:()=>this.resume()}),Object.defineProperty(this,Z,{writable:!0,value:()=>{if(I(this,O)[O]){I(this,S)[S]=setTimeout(I(this,Z)[Z],0);return}I(this,Q)[Q]=this.limit,this.limit=Math.ceil((I(this,R)[R]+I(this,Q)[Q])/2);for(let a=I(this,Q)[Q];a<=this.limit;a++)I(this,U)[U]();I(this,R)[R]-I(this,Q)[Q]>3?I(this,S)[S]=setTimeout(I(this,Z)[Z],2e3):I(this,Q)[Q]=Math.floor(I(this,Q)[Q]/2)}}),"number"!=typeof a||0===a?this.limit=1/0:this.limit=a}run(a,b){return!I(this,O)[O]&&I(this,M)[M]<this.limit?I(this,T)[T](a):I(this,W)[W](a,b)}wrapSyncFunction(a,b){var c=this;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];let g=c.run(()=>(a(...e),queueMicrotask(()=>g.done()),()=>{}),b);return{abortOn:L,abort(){g.abort()}}}}wrapPromiseFunction(a,b){var c=this;return function(){let d;for(var e=arguments.length,f=Array(e),g=0;g<e;g++)f[g]=arguments[g];let h=new Promise((e,g)=>{d=c.run(()=>{let b,c;try{c=Promise.resolve(a(...f))}catch(a){c=Promise.reject(a)}return c.then(a=>{b?g(b):(d.done(),e(a))},a=>{b?g(b):(d.done(),g(a))}),a=>{b=Error("Cancelled",{cause:a})}},b)});return h.abort=a=>{d.abort(a)},h.abortOn=L,h}}resume(){I(this,O)[O]=!1,clearTimeout(I(this,P)[P]);for(let a=0;a<this.limit;a++)I(this,U)[U]()}pause(a){void 0===a&&(a=null),I(this,O)[O]=!0,clearTimeout(I(this,P)[P]),null!=a&&(I(this,P)[P]=setTimeout(I(this,Y)[Y],a))}rateLimit(a){clearTimeout(I(this,S)[S]),this.pause(a),this.limit>1&&Number.isFinite(this.limit)&&(I(this,R)[R]=this.limit-1,this.limit=I(this,Q)[Q],I(this,S)[S]=setTimeout(I(this,Z)[Z],a))}get isPaused(){return I(this,O)[O]}}function _(a){let b;I(this,M)[M]+=1;let c=!1;try{b=a()}catch(a){throw I(this,M)[M]-=1,a}return{abort:a=>{c||(c=!0,I(this,M)[M]-=1,null==b||b(a),I(this,U)[U]())},done:()=>{c||(c=!0,I(this,M)[M]-=1,I(this,U)[U]())}}}function aa(){queueMicrotask(()=>I(this,V)[V]())}function ab(){if(I(this,O)[O]||I(this,M)[M]>=this.limit||0===I(this,N)[N].length)return;let a=I(this,N)[N].shift();if(null==a)throw Error("Invariant violation: next is null");let b=I(this,T)[T](a.fn);a.abort=b.abort,a.done=b.done}function ac(a,b){let c={fn:a,priority:(null==b?void 0:b.priority)||0,abort:()=>{I(this,X)[X](c)},done:()=>{throw Error("Cannot mark a queued request as done: this indicates a bug")}},d=I(this,N)[N].findIndex(a=>c.priority>a.priority);return -1===d?I(this,N)[N].push(c):I(this,N)[N].splice(d,0,c),c}function ad(a){let b=I(this,N)[N].indexOf(a);-1!==b&&I(this,N)[N].splice(b,1)}Symbol("__queue");let{AbortController:ae}=globalThis,{AbortSignal:af}=globalThis,ag=function(a,b){void 0===a&&(a="Aborted");let c=new DOMException(a,"AbortError");return null!=b&&n(b,"cause")&&Object.defineProperty(c,"cause",{__proto__:null,configurable:!0,writable:!0,value:b.cause}),c};function ah(a,b){return!0===a?Object.keys(b):Array.isArray(a)?a:[]}function ai(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var aj=0;function ak(a){return"__private_"+aj+++"_"+a}let al={getChunkSize:a=>Math.ceil(a.size/1e4),onProgress(){},onPartComplete(){},onSuccess(){},onError(a){throw a}},am=Symbol("pausing upload, not an actual error");var an=ak("abortController"),ao=ak("chunks"),ap=ak("chunkState"),aq=ak("data"),ar=ak("file"),as=ak("uploadHasStarted"),at=ak("onError"),au=ak("onSuccess"),av=ak("shouldUseMultipart"),aw=ak("isRestoring"),ax=ak("onReject"),ay=ak("maxMultipartParts"),az=ak("minPartSize"),aA=ak("initChunks"),aB=ak("createUpload"),aC=ak("resumeUpload"),aD=ak("onPartProgress"),aE=ak("onPartComplete"),aF=ak("abortUpload");class aG{constructor(a,b){var c;Object.defineProperty(this,aF,{value:aK}),Object.defineProperty(this,aC,{value:aJ}),Object.defineProperty(this,aB,{value:aI}),Object.defineProperty(this,aA,{value:aH}),Object.defineProperty(this,an,{writable:!0,value:new ae}),Object.defineProperty(this,ao,{writable:!0,value:[]}),Object.defineProperty(this,ap,{writable:!0,value:[]}),Object.defineProperty(this,aq,{writable:!0,value:void 0}),Object.defineProperty(this,ar,{writable:!0,value:void 0}),Object.defineProperty(this,as,{writable:!0,value:!1}),Object.defineProperty(this,at,{writable:!0,value:void 0}),Object.defineProperty(this,au,{writable:!0,value:void 0}),Object.defineProperty(this,av,{writable:!0,value:void 0}),Object.defineProperty(this,aw,{writable:!0,value:void 0}),Object.defineProperty(this,ax,{writable:!0,value:a=>(null==a?void 0:a.cause)===am?null:ai(this,at)[at](a)}),Object.defineProperty(this,ay,{writable:!0,value:1e4}),Object.defineProperty(this,az,{writable:!0,value:5242880}),Object.defineProperty(this,aD,{writable:!0,value:a=>b=>{if(!b.lengthComputable)return;ai(this,ap)[ap][a].uploaded=function(a){if("string"==typeof a)return parseInt(a,10);if("number"==typeof a)return a;throw TypeError("Expected a number")}(b.loaded);let c=ai(this,ap)[ap].reduce((a,b)=>a+b.uploaded,0);this.options.onProgress(c,ai(this,aq)[aq].size)}}),Object.defineProperty(this,aE,{writable:!0,value:a=>b=>{ai(this,ao)[ao][a]=null,ai(this,ap)[ap][a].etag=b,ai(this,ap)[ap][a].done=!0,this.options.onPartComplete({PartNumber:a+1,ETag:b})}}),this.options={...al,...b},null!=(c=this.options).getChunkSize||(c.getChunkSize=al.getChunkSize),ai(this,aq)[aq]=a,ai(this,ar)[ar]=b.file,ai(this,au)[au]=this.options.onSuccess,ai(this,at)[at]=this.options.onError,ai(this,av)[av]=this.options.shouldUseMultipart,ai(this,aw)[aw]=b.uploadId&&b.key,ai(this,aA)[aA]()}start(){ai(this,as)[as]?(ai(this,an)[an].signal.aborted||ai(this,an)[an].abort(am),ai(this,an)[an]=new ae,ai(this,aC)[aC]()):ai(this,aw)[aw]?(this.options.companionComm.restoreUploadFile(ai(this,ar)[ar],{uploadId:this.options.uploadId,key:this.options.key}),ai(this,aC)[aC]()):ai(this,aB)[aB]()}pause(){ai(this,an)[an].abort(am),ai(this,an)[an]=new ae}abort(a){null!=a&&a.really?ai(this,aF)[aF]():this.pause()}[Symbol.for("uppy test: getChunkState")](){return ai(this,ap)[ap]}}function aH(){let a=ai(this,aq)[aq].size,b="function"==typeof ai(this,av)[av]?ai(this,av)[av](ai(this,ar)[ar]):!!ai(this,av)[av];if(b&&a>ai(this,az)[az]){let c=Math.max(this.options.getChunkSize(ai(this,aq)[aq]),ai(this,az)[az]),d=Math.floor(a/c);d>ai(this,ay)[ay]&&(d=ai(this,ay)[ay],c=a/ai(this,ay)[ay]),ai(this,ao)[ao]=Array(d);for(let d=0,e=0;d<a;d+=c,e++){let f=Math.min(a,d+c),g=()=>{let a=d;return ai(this,aq)[aq].slice(a,f)};if(ai(this,ao)[ao][e]={getData:g,onProgress:ai(this,aD)[aD](e),onComplete:ai(this,aE)[aE](e),shouldUseMultipart:b},ai(this,aw)[aw]){let b=d+c>a?a-d:c;ai(this,ao)[ao][e].setAsUploaded=()=>{ai(this,ao)[ao][e]=null,ai(this,ap)[ap][e].uploaded=b}}}}else ai(this,ao)[ao]=[{getData:()=>ai(this,aq)[aq],onProgress:ai(this,aD)[aD](0),onComplete:ai(this,aE)[aE](0),shouldUseMultipart:b}];ai(this,ap)[ap]=ai(this,ao)[ao].map(()=>({uploaded:0}))}function aI(){this.options.companionComm.uploadFile(ai(this,ar)[ar],ai(this,ao)[ao],ai(this,an)[an].signal).then(ai(this,au)[au],ai(this,ax)[ax]),ai(this,as)[as]=!0}function aJ(){this.options.companionComm.resumeUploadFile(ai(this,ar)[ar],ai(this,ao)[ao],ai(this,an)[an].signal).then(ai(this,au)[au],ai(this,ax)[ax])}function aK(){ai(this,an)[an].abort(),this.options.companionComm.abortFileUpload(ai(this,ar)[ar]).catch(a=>this.options.log(a))}function aL(a){if(null!=a&&a.aborted)throw ag("The operation was aborted",{cause:a.reason})}let aM=new TextEncoder,aN={name:"HMAC",hash:"SHA-256"};async function aO(a){let{subtle:b}=globalThis.crypto;return b.digest(aN.hash,aM.encode(a))}async function aP(a){let{subtle:b}=globalThis.crypto;return b.importKey("raw","string"==typeof a?aM.encode(a):a,aN,!1,["sign"])}function aQ(a){let b=new Uint8Array(a),c="";for(let a=0;a<b.length;a++)c+=b[a].toString(16).padStart(2,"0");return c}async function aR(a,b){let{subtle:c}=globalThis.crypto;return c.sign(aN,await aP(a),aM.encode(b))}async function aS(a){let{accountKey:b,accountSecret:c,sessionToken:d,bucketName:e,Key:f,Region:g,expires:h,uploadId:i,partNumber:j}=a,k=`s3.${g}.amazonaws.com`,l=`/${e}/${encodeURI(f).replace(/[;?:@&=+$,#!'()*]/g,a=>`%${a.charCodeAt(0).toString(16).toUpperCase()}`)}`,m="UNSIGNED-PAYLOAD",n=new Date().toISOString().replace(/[-:]|\.\d+/g,""),o=n.slice(0,8),p=`${o}/${g}/s3/aws4_request`,q=new URL(`https://${k}${l}`);q.searchParams.set("X-Amz-Algorithm","AWS4-HMAC-SHA256"),q.searchParams.set("X-Amz-Content-Sha256",m),q.searchParams.set("X-Amz-Credential",`${b}/${p}`),q.searchParams.set("X-Amz-Date",n),q.searchParams.set("X-Amz-Expires",h),q.searchParams.set("X-Amz-Security-Token",d),q.searchParams.set("X-Amz-SignedHeaders","host"),j&&q.searchParams.set("partNumber",j),i&&q.searchParams.set("uploadId",i),q.searchParams.set("x-id",j&&i?"UploadPart":"PutObject");let r=function(a){let{method:b="PUT",CanonicalUri:c="/",CanonicalQueryString:d="",SignedHeaders:e,HashedPayload:f}=a,g=Object.keys(e).map(a=>a.toLowerCase()).sort();return[b,c,d,...g.map(a=>`${a}:${e[a]}`),"",g.join(";"),f].join("\n")}({CanonicalUri:l,CanonicalQueryString:q.search.slice(1),SignedHeaders:{host:k},HashedPayload:m}),s=["AWS4-HMAC-SHA256",n,p,aQ(await aO(r))].join("\n"),t=await aR(`AWS4${c}`,o),u=await aR(t,g),v=await aR(u,"s3"),w=await aR(v,"aws4_request"),x=aQ(await aR(w,s));return q.searchParams.set("X-Amz-Signature",x),q}function aT(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var aU=0;function aV(a){return"__private_"+aU+++"_"+a}var aW=aV("abortMultipartUpload"),aX=aV("cache"),aY=aV("createMultipartUpload"),aZ=aV("fetchSignature"),a$=aV("getUploadParameters"),a_=aV("listParts"),a0=aV("previousRetryDelay"),a1=aV("requests"),a2=aV("retryDelays"),a3=aV("sendCompletionRequest"),a4=aV("setS3MultipartState"),a5=aV("uploadPartBytes"),a6=aV("getFile"),a7=aV("shouldRetry"),a8=aV("nonMultipartUpload");class a9{constructor(a,b,c,d){Object.defineProperty(this,a8,{value:bb}),Object.defineProperty(this,a7,{value:ba}),Object.defineProperty(this,aW,{writable:!0,value:void 0}),Object.defineProperty(this,aX,{writable:!0,value:new WeakMap}),Object.defineProperty(this,aY,{writable:!0,value:void 0}),Object.defineProperty(this,aZ,{writable:!0,value:void 0}),Object.defineProperty(this,a$,{writable:!0,value:void 0}),Object.defineProperty(this,a_,{writable:!0,value:void 0}),Object.defineProperty(this,a0,{writable:!0,value:void 0}),Object.defineProperty(this,a1,{writable:!0,value:void 0}),Object.defineProperty(this,a2,{writable:!0,value:void 0}),Object.defineProperty(this,a3,{writable:!0,value:void 0}),Object.defineProperty(this,a4,{writable:!0,value:void 0}),Object.defineProperty(this,a5,{writable:!0,value:void 0}),Object.defineProperty(this,a6,{writable:!0,value:void 0}),aT(this,a1)[a1]=a,aT(this,a4)[a4]=c,aT(this,a6)[a6]=d,this.setOptions(b)}setOptions(a){let b=aT(this,a1)[a1];if("abortMultipartUpload"in a&&(aT(this,aW)[aW]=b.wrapPromiseFunction(a.abortMultipartUpload,{priority:1})),"createMultipartUpload"in a&&(aT(this,aY)[aY]=b.wrapPromiseFunction(a.createMultipartUpload,{priority:-1})),"signPart"in a&&(aT(this,aZ)[aZ]=b.wrapPromiseFunction(a.signPart)),"listParts"in a&&(aT(this,a_)[a_]=b.wrapPromiseFunction(a.listParts)),"completeMultipartUpload"in a&&(aT(this,a3)[a3]=b.wrapPromiseFunction(a.completeMultipartUpload,{priority:1})),"retryDelays"in a){var c;aT(this,a2)[a2]=null!=(c=a.retryDelays)?c:[]}"uploadPartBytes"in a&&(aT(this,a5)[a5]=b.wrapPromiseFunction(a.uploadPartBytes,{priority:1/0})),"getUploadParameters"in a&&(aT(this,a$)[a$]=b.wrapPromiseFunction(a.getUploadParameters))}async getUploadId(a,b){let c;for(;null!=(c=aT(this,aX)[aX].get(a.data));)try{return await c}catch{}let d=aT(this,aY)[aY](aT(this,a6)[a6](a),b),e=()=>{d.abort(b.reason),aT(this,aX)[aX].delete(a.data)};return b.addEventListener("abort",e,{once:!0}),aT(this,aX)[aX].set(a.data,d),d.then(async c=>{b.removeEventListener("abort",e),aT(this,a4)[a4](a,c),aT(this,aX)[aX].set(a.data,c)},()=>{b.removeEventListener("abort",e),aT(this,aX)[aX].delete(a.data)}),d}async abortFileUpload(a){let b,c=aT(this,aX)[aX].get(a.data);if(null!=c){aT(this,aX)[aX].delete(a.data),aT(this,a4)[a4](a,Object.create(null));try{b=await c}catch{return}await aT(this,aW)[aW](aT(this,a6)[a6](a),b)}}async uploadFile(a,b,c){if(aL(c),1===b.length&&!b[0].shouldUseMultipart)return aT(this,a8)[a8](a,b[0],c);let{uploadId:d,key:e}=await this.getUploadId(a,c);aL(c);try{let f=await Promise.all(b.map((b,d)=>this.uploadChunk(a,d+1,b,c)));return aL(c),await aT(this,a3)[a3](aT(this,a6)[a6](a),{key:e,uploadId:d,parts:f,signal:c},c).abortOn(c)}catch(b){throw(null==b?void 0:b.cause)!==am&&(null==b?void 0:b.name)!=="AbortError"&&this.abortFileUpload(a),b}}restoreUploadFile(a,b){aT(this,aX)[aX].set(a.data,b)}async resumeUploadFile(a,b,c){if(aL(c),1===b.length&&null!=b[0]&&!b[0].shouldUseMultipart)return aT(this,a8)[a8](a,b[0],c);let{uploadId:d,key:e}=await this.getUploadId(a,c);aL(c);let f=await aT(this,a_)[a_](aT(this,a6)[a6](a),{uploadId:d,key:e,signal:c},c).abortOn(c);aL(c);let g=await Promise.all(b.map((b,d)=>{let e=d+1,g=f.find(a=>{let{PartNumber:b}=a;return b===e});return null==g?this.uploadChunk(a,e,b,c):(null==b||null==b.setAsUploaded||b.setAsUploaded(),{PartNumber:e,ETag:g.ETag})}));return aL(c),aT(this,a3)[a3](aT(this,a6)[a6](a),{key:e,uploadId:d,parts:g,signal:c},c).abortOn(c)}async uploadChunk(a,b,c,d){aL(d);let{uploadId:e,key:f}=await this.getUploadId(a,d),g=aT(this,a2)[a2].values(),h=aT(this,a2)[a2].values(),i=()=>{let a=g.next();return null==a||a.done?null:a.value};for(;;){let g;aL(d);let j=c.getData(),{onProgress:k,onComplete:l}=c;try{g=await aT(this,aZ)[aZ](aT(this,a6)[a6](a),{uploadId:e,key:f,partNumber:b,body:j,signal:d}).abortOn(d)}catch(b){let a=i();if(null==a||d.aborted)throw b;await new Promise(b=>setTimeout(b,a));continue}aL(d);try{return{PartNumber:b,...await aT(this,a5)[a5]({signature:g,body:j,size:j.size,onProgress:k,onComplete:l,signal:d}).abortOn(d)}}catch(a){if(!await aT(this,a7)[a7](a,h))throw a}}}}async function ba(a,b){var c;let d=aT(this,a1)[a1],e=null==a||null==(c=a.source)?void 0:c.status;if(null==e)return!1;if(403===e&&"Request has expired"===a.message){if(!d.isPaused){if(1===d.limit||null==aT(this,a0)[a0]){let a=b.next();if(null==a||a.done)return!1;aT(this,a0)[a0]=a.value}d.rateLimit(0),await new Promise(a=>setTimeout(a,aT(this,a0)[a0]))}}else if(429===e){if(!d.isPaused){let a=b.next();if(null==a||a.done)return!1;d.rateLimit(a.value)}}else if(e>400&&e<500&&409!==e)return!1;else if("undefined"!=typeof navigator&&!1===navigator.onLine)d.isPaused||(d.pause(),window.addEventListener("online",()=>{d.resume()},{once:!0}));else{let a=b.next();if(null==a||a.done)return!1;await new Promise(b=>setTimeout(b,a.value))}return!0}async function bb(a,b,c){var d;let e,{method:f="POST",url:g,fields:h,headers:i}=await aT(this,a$)[a$](aT(this,a6)[a6](a),{signal:c}).abortOn(c),j=b.getData();if("POST"===f.toUpperCase()){let a=new FormData;Object.entries(h).forEach(b=>{let[c,d]=b;return a.set(c,d)}),a.set("file",j),e=a}else e=j;let{onProgress:k,onComplete:l}=b,m=await aT(this,a5)[a5]({signature:{url:g,headers:i,method:f},body:e,size:j.size,onProgress:k,onComplete:l,signal:c}).abortOn(c),n=null==h?void 0:h.key;return aT(this,a4)[a4](a,{key:n}),{...m,location:null!=(d=m.location)?d:function(a){let b=new URL(a);return b.search="",b.hash="",b.href}(g),bucket:null==h?void 0:h.bucket,key:n}}function bc(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var bd=0;function be(a){return"__private_"+bd+++"_"+a}function bf(a){if(null!=a&&a.error){let b=Error(a.message);throw Object.assign(b,a.error),b}return a}function bg(a){let b=a.Expiration;if(b){let a=Math.floor((new Date(b)-Date.now())/1e3);if(a>9)return a}}function bh(a){let{meta:b,allowedMetaFields:c,querify:d=!1}=a,e=null!=c?c:Object.keys(b);return b?Object.fromEntries(e.filter(a=>null!=b[a]).map(a=>[d?`metadata[${a}]`:a,String(b[a])])):{}}let bi={allowedMetaFields:!0,limit:6,getTemporarySecurityCredentials:!1,shouldUseMultipart:a=>(a.size||0)>0x6400000,retryDelays:[0,1e3,3e3,5e3]};var bj=be("companionCommunicationQueue"),bk=be("client"),bl=be("setClient"),bm=be("assertHost"),bn=be("cachedTemporaryCredentials"),bo=be("getTemporarySecurityCredentials"),bp=be("setS3MultipartState"),bq=be("getFile"),br=be("uploadLocalFile"),bs=be("getCompanionClientArgs"),bt=be("upload"),bu=be("setCompanionHeaders"),bv=be("setResumableUploadsCapability"),bw=be("resetResumableCapability");class bx extends e{constructor(a,b){var c;super(a,{...bi,uploadPartBytes:bx.uploadPartBytes,createMultipartUpload:null,listParts:null,abortMultipartUpload:null,completeMultipartUpload:null,signPart:null,getUploadParameters:null,...b}),Object.defineProperty(this,bs,{value:bC}),Object.defineProperty(this,br,{value:bB}),Object.defineProperty(this,bo,{value:bA}),Object.defineProperty(this,bm,{value:bz}),Object.defineProperty(this,bl,{value:by}),Object.defineProperty(this,bj,{writable:!0,value:void 0}),Object.defineProperty(this,bk,{writable:!0,value:void 0}),Object.defineProperty(this,bn,{writable:!0,value:void 0}),Object.defineProperty(this,bp,{writable:!0,value:(a,b)=>{let{key:c,uploadId:d}=b,e=this.uppy.getFile(a.id);null!=e&&this.uppy.setFileState(a.id,{s3Multipart:{...e.s3Multipart,key:c,uploadId:d}})}}),Object.defineProperty(this,bq,{writable:!0,value:a=>this.uppy.getFile(a.id)||a}),Object.defineProperty(this,bt,{writable:!0,value:async a=>{if(0===a.length)return;let b=this.uppy.getFilesByIds(a).filter(a=>!("error"in a&&a.error)),c=b.filter(a=>{var b;return!(null!=(b=a.progress)&&b.uploadStarted)||!a.isRestored});this.uppy.emit("upload-start",c);let d=b.map(a=>{if(a.isRemote){bc(this,bv)[bv](!1);let b=new AbortController,c=c=>{c.id===a.id&&b.abort()};this.uppy.on("file-removed",c);let d=this.uppy.getRequestClientForFile(a).uploadRemoteFile(a,bc(this,bs)[bs](a),{signal:b.signal,getQueue:()=>this.requests});return this.requests.wrapSyncFunction(()=>{this.uppy.off("file-removed",c)},{priority:-1})(),d}return bc(this,br)[br](a)}),e=await Promise.allSettled(d);return bc(this,bv)[bv](!0),e}}),Object.defineProperty(this,bu,{writable:!0,value:()=>{var a;null==(a=bc(this,bk)[bk])||a.setCompanionHeaders(this.opts.headers)}}),Object.defineProperty(this,bv,{writable:!0,value:a=>{let{capabilities:b}=this.uppy.getState();this.uppy.setState({capabilities:{...b,resumableUploads:a}})}}),Object.defineProperty(this,bw,{writable:!0,value:()=>{bc(this,bv)[bv](!0)}}),this.type="uploader",this.id=this.opts.id||"AwsS3Multipart",bc(this,bl)[bl](b);let d={createMultipartUpload:this.createMultipartUpload,listParts:this.listParts,abortMultipartUpload:this.abortMultipartUpload,completeMultipartUpload:this.completeMultipartUpload,signPart:null!=b&&b.getTemporarySecurityCredentials?this.createSignedURL:this.signPart,getUploadParameters:null!=b&&b.getTemporarySecurityCredentials?this.createSignedURL:this.getUploadParameters};for(let a of Object.keys(d))null==this.opts[a]&&(this.opts[a]=d[a].bind(this));this.requests=null!=(c=this.opts.rateLimitedQueue)?c:new $(this.opts.limit),bc(this,bj)[bj]=new a9(this.requests,this.opts,bc(this,bp)[bp],bc(this,bq)[bq]),this.uploaders=Object.create(null),this.uploaderEvents=Object.create(null)}[Symbol.for("uppy test: getClient")](){return bc(this,bk)[bk]}setOptions(a){bc(this,bj)[bj].setOptions(a),super.setOptions(a),bc(this,bl)[bl](a)}resetUploaderReferences(a,b){this.uploaders[a]&&(this.uploaders[a].abort({really:(null==b?void 0:b.abort)||!1}),this.uploaders[a]=null),this.uploaderEvents[a]&&(this.uploaderEvents[a].remove(),this.uploaderEvents[a]=null)}createMultipartUpload(a,b){bc(this,bm)[bm]("createMultipartUpload"),aL(b);let c=ah(this.opts.allowedMetaFields,a.meta),d=bh({meta:a.meta,allowedMetaFields:c});return bc(this,bk)[bk].post("s3/multipart",{filename:a.name,type:a.type,metadata:d},{signal:b}).then(bf)}listParts(a,b,c){let{key:d,uploadId:e,signal:f}=b;null!=f||(f=c),bc(this,bm)[bm]("listParts"),aL(f);let g=encodeURIComponent(d);return bc(this,bk)[bk].get(`s3/multipart/${encodeURIComponent(e)}?key=${g}`,{signal:f}).then(bf)}completeMultipartUpload(a,b,c){let{key:d,uploadId:e,parts:f,signal:g}=b;null!=g||(g=c),bc(this,bm)[bm]("completeMultipartUpload"),aL(g);let h=encodeURIComponent(d),i=encodeURIComponent(e);return bc(this,bk)[bk].post(`s3/multipart/${i}/complete?key=${h}`,{parts:f.map(a=>{let{ETag:b,PartNumber:c}=a;return{ETag:b,PartNumber:c}})},{signal:g}).then(bf)}async createSignedURL(a,b){let c=await bc(this,bo)[bo](b),d=bg(c.credentials)||604800,{uploadId:e,key:f,partNumber:g}=b;return{method:"PUT",expires:d,fields:{},url:`${await aS({accountKey:c.credentials.AccessKeyId,accountSecret:c.credentials.SecretAccessKey,sessionToken:c.credentials.SessionToken,expires:d,bucketName:c.bucket,Region:c.region,Key:null!=f?f:`${crypto.randomUUID()}-${a.name}`,uploadId:e,partNumber:g})}`,headers:{"Content-Type":a.type}}}signPart(a,b){let{uploadId:c,key:d,partNumber:e,signal:f}=b;if(bc(this,bm)[bm]("signPart"),aL(f),null==c||null==d||null==e)throw Error("Cannot sign without a key, an uploadId, and a partNumber");let g=encodeURIComponent(d);return bc(this,bk)[bk].get(`s3/multipart/${encodeURIComponent(c)}/${e}?key=${g}`,{signal:f}).then(bf)}abortMultipartUpload(a,b){let{key:c,uploadId:d,signal:e}=b;bc(this,bm)[bm]("abortMultipartUpload");let f=encodeURIComponent(c),g=encodeURIComponent(d);return bc(this,bk)[bk].delete(`s3/multipart/${g}?key=${f}`,void 0,{signal:e}).then(bf)}getUploadParameters(a,b){bc(this,bm)[bm]("getUploadParameters");let{meta:c}=a,{type:d,name:e}=c,f=new URLSearchParams({filename:e,type:d,...bh({meta:c,allowedMetaFields:ah(this.opts.allowedMetaFields,a.meta),querify:!0})});return bc(this,bk)[bk].get(`s3/params?${f}`,b)}static async uploadPartBytes(a){let{signature:{url:b,expires:c,headers:d,method:e="PUT"},body:f,size:g=f.size,onProgress:h,onComplete:i,signal:j}=a;if(aL(j),null==b)throw Error("Cannot upload to an undefined URL");return new Promise((a,k)=>{let l=new XMLHttpRequest;function m(){l.abort()}function n(){null==j||j.removeEventListener("abort",m)}l.open(e,b,!0),d&&Object.keys(d).forEach(a=>{l.setRequestHeader(a,d[a])}),l.responseType="text","number"==typeof c&&(l.timeout=1e3*c),null==j||j.addEventListener("abort",m),l.upload.addEventListener("progress",a=>{h(a)}),l.addEventListener("abort",()=>{n(),k(ag())}),l.addEventListener("timeout",()=>{n();let a=Error("Request has expired");a.source={status:403},k(a)}),l.addEventListener("load",()=>{if(n(),403===l.status&&l.responseText.includes("<Message>Request has expired</Message>")){let a=Error("Request has expired");a.source=l,k(a);return}if(l.status<200||l.status>=300){let a=Error("Non 2xx");a.source=l,k(a);return}null==h||h({loaded:g,lengthComputable:!0});let b=l.getAllResponseHeaders().trim().split(/[\r\n]+/),c={__proto__:null};for(let a of b){let b=a.split(": "),d=b.shift(),e=b.join(": ");c[d]=e}let{etag:d,location:f}=c;if("POST"===e.toUpperCase()&&null==f&&console.error("@uppy/aws-s3: Could not read the Location header. This likely means CORS is not configured correctly on the S3 Bucket. See https://uppy.io/docs/aws-s3/#setting-up-your-s3-bucket"),null==d)return void console.error("@uppy/aws-s3: Could not read the ETag header. This likely means CORS is not configured correctly on the S3 Bucket. See https://uppy.io/docs/aws-s3/#setting-up-your-s3-bucket");null==i||i(d),a({...c,ETag:d})}),l.addEventListener("error",a=>{n();let b=Error("Unknown error");b.source=a.target,k(b)}),l.send(f)})}install(){bc(this,bv)[bv](!0),this.uppy.addPreProcessor(bc(this,bu)[bu]),this.uppy.addUploader(bc(this,bt)[bt]),this.uppy.on("cancel-all",bc(this,bw)[bw])}uninstall(){this.uppy.removePreProcessor(bc(this,bu)[bu]),this.uppy.removeUploader(bc(this,bt)[bt]),this.uppy.off("cancel-all",bc(this,bw)[bw])}}function by(a){null!=a&&("endpoint"in a||"companionUrl"in a||"headers"in a||"companionHeaders"in a||"cookiesRule"in a||"companionCookiesRule"in a)&&("companionUrl"in a&&!("endpoint"in a)&&this.uppy.log("`companionUrl` option has been removed in @uppy/aws-s3, use `endpoint` instead.","warning"),"companionHeaders"in a&&!("headers"in a)&&this.uppy.log("`companionHeaders` option has been removed in @uppy/aws-s3, use `headers` instead.","warning"),"companionCookiesRule"in a&&!("cookiesRule"in a)&&this.uppy.log("`companionCookiesRule` option has been removed in @uppy/aws-s3, use `cookiesRule` instead.","warning"),"endpoint"in a?bc(this,bk)[bk]=new z(this.uppy,{pluginId:this.id,provider:"AWS",companionUrl:this.opts.endpoint,companionHeaders:this.opts.headers,companionCookiesRule:this.opts.cookiesRule}):("headers"in a&&bc(this,bu)[bu](),"cookiesRule"in a&&(bc(this,bk)[bk].opts.companionCookiesRule=a.cookiesRule)))}function bz(a){if(!bc(this,bk)[bk])throw Error(`Expected a \`endpoint\` option containing a URL, or if you are not using Companion, a custom \`${a}\` implementation.`)}async function bA(a){if(aL(null==a?void 0:a.signal),null==bc(this,bn)[bn]){let{getTemporarySecurityCredentials:b}=this.opts;!0===b?(bc(this,bm)[bm]("getTemporarySecurityCredentials"),bc(this,bn)[bn]=bc(this,bk)[bk].get("s3/sts",a).then(bf)):bc(this,bn)[bn]=b(a),bc(this,bn)[bn]=await bc(this,bn)[bn],setTimeout(()=>{bc(this,bn)[bn]=null},500*(bg(bc(this,bn)[bn].credentials)||0))}return bc(this,bn)[bn]}function bB(a){var b=this;return new Promise((c,d)=>{let e=new aG(a.data,{companionComm:bc(this,bj)[bj],log:function(){return b.uppy.log(...arguments)},getChunkSize:this.opts.getChunkSize?this.opts.getChunkSize.bind(this):void 0,onProgress:(b,c)=>{var d;let e=this.uppy.getFile(a.id);this.uppy.emit("upload-progress",e,{uploadStarted:null!=(d=e.progress.uploadStarted)?d:0,bytesUploaded:b,bytesTotal:c})},onError:b=>{this.uppy.log(b),this.uppy.emit("upload-error",a,b),this.resetUploaderReferences(a.id),d(b)},onSuccess:b=>{let d={body:{...b},status:200,uploadURL:b.location};this.resetUploaderReferences(a.id),this.uppy.emit("upload-success",bc(this,bq)[bq](a),d),b.location&&this.uppy.log(`Download ${a.name} from ${b.location}`),c()},onPartComplete:b=>{this.uppy.emit("s3-multipart:part-uploaded",bc(this,bq)[bq](a),b)},file:a,shouldUseMultipart:this.opts.shouldUseMultipart,...a.s3Multipart});this.uploaders[a.id]=e;let f=new H(this.uppy);this.uploaderEvents[a.id]=f,f.onFileRemove(a.id,b=>{e.abort(),this.resetUploaderReferences(a.id,{abort:!0}),c(`upload ${b} was removed`)}),f.onCancelAll(a.id,()=>{e.abort(),this.resetUploaderReferences(a.id,{abort:!0}),c(`upload ${a.id} was canceled`)}),f.onFilePause(a.id,a=>{a?e.pause():e.start()}),f.onPauseAll(a.id,()=>{e.pause()}),f.onResumeAll(a.id,()=>{e.start()}),e.start()})}function bC(a){var b;return{...null==(b=a.remote)?void 0:b.body,protocol:"s3-multipart",size:a.data.size,metadata:a.meta}}bx.VERSION="4.2.3"},45250:a=>{"use strict";a.exports=function(a){if("number"!=typeof a||Number.isNaN(a))throw TypeError(`Expected a number, got ${typeof a}`);let b=Math.abs(a);if(a<0&&(b=-b),0===b)return"0 B";let c=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],d=Math.min(Math.floor(Math.log(b)/Math.log(1024)),c.length-1),e=Number(b/1024**d),f=c[d];return`${e>=10||e%1==0?Math.round(e):e.toFixed(1)} ${f}`}},45583:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45603:(a,b,c)=>{var d=c(20540),e=c(55048);a.exports=function(a,b,c){var f=!0,g=!0;if("function"!=typeof a)throw TypeError("Expected a function");return e(c)&&(f="leading"in c?!!c.leading:f,g="trailing"in c?!!c.trailing:g),d(a,b,{leading:f,maxWait:b,trailing:g})}},46810:(a,b,c)=>{var d=c(6120);b.operation=function(a){return new d(b.timeouts(a),{forever:a&&(a.forever||a.retries===1/0),unref:a&&a.unref,maxRetryTime:a&&a.maxRetryTime})},b.timeouts=function(a){if(a instanceof Array)return[].concat(a);var b={retries:10,factor:2,minTimeout:1e3,maxTimeout:1/0,randomize:!1};for(var c in a)b[c]=a[c];if(b.minTimeout>b.maxTimeout)throw Error("minTimeout is greater than maxTimeout");for(var d=[],e=0;e<b.retries;e++)d.push(this.createTimeout(e,b));return a&&a.forever&&!d.length&&d.push(this.createTimeout(e,b)),d.sort(function(a,b){return a-b}),d},b.createTimeout=function(a,b){var c=Math.round((b.randomize?Math.random()+1:1)*Math.max(b.minTimeout,1)*Math.pow(b.factor,a));return Math.min(c,b.maxTimeout)},b.wrap=function(a,c,d){if(c instanceof Array&&(d=c,c=null),!d)for(var e in d=[],a)"function"==typeof a[e]&&d.push(e);for(var f=0;f<d.length;f++){var g=d[f],h=a[g];a[g]=(function(d){var e=b.operation(c),f=Array.prototype.slice.call(arguments,1),g=f.pop();f.push(function(a){e.retry(a)||(a&&(arguments[0]=e.mainError()),g.apply(this,arguments))}),e.attempt(function(){d.apply(a,f)})}).bind(a,h),a[g].options=c}}},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},48976:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},49227:(a,b,c)=>{var d=c(29395),e=c(27467);a.exports=function(a){return"symbol"==typeof a||e(a)&&"[object Symbol]"==d(a)}},55048:a=>{a.exports=function(a){var b=typeof a;return null!=a&&("object"==b||"function"==b)}},62022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]])},62369:(a,b,c)=>{"use strict";c.d(b,{b:()=>j});var d=c(43210),e=c(14163),f=c(60687),g="horizontal",h=["horizontal","vertical"],i=d.forwardRef((a,b)=>{var c;let{decorative:d,orientation:i=g,...j}=a,k=(c=i,h.includes(c))?i:g;return(0,f.jsx)(e.sG.div,{"data-orientation":k,...d?{role:"none"}:{"aria-orientation":"vertical"===k?k:void 0,role:"separator"},...j,ref:b})});i.displayName="Separator";var j=i},62765:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},70151:(a,b,c)=>{var d=c(85718);a.exports=function(){return d.Date.now()}},70222:(a,b,c)=>{var d=c(79474),e=Object.prototype,f=e.hasOwnProperty,g=e.toString,h=d?d.toStringTag:void 0;a.exports=function(a){var b=f.call(a,h),c=a[h];try{a[h]=void 0;var d=!0}catch(a){}var e=g.call(a);return d&&(b?a[h]=c:delete a[h]),e}},70899:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},71042:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(68388),e=c(52637),f=c(51846),g=c(31162),h=c(84971),i=c(98479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},72758:(a,b,c)=>{var d=c(39440),e=/[\/\+\.]/;a.exports=function(a,b){function c(b){var c=d(b,a,e);return c&&c.length>=2}return b?c(b.split(";")[0]):c}},77026:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},78464:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},79474:(a,b,c)=>{a.exports=c(85718).Symbol},84713:a=>{var b=Object.prototype.toString;a.exports=function(a){return b.call(a)}},85718:(a,b,c)=>{var d=c(10663),e="object"==typeof self&&self&&self.Object===Object&&self;a.exports=d||e||Function("return this")()},86897:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(52836),e=c(49026),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89436:(a,b,c)=>{"use strict";c.d(b,{A:()=>av});var d=c(93028),e=c(25834),f=c(45603);function g(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var h=0;function i(a){return"__private_"+h+++"_"+a}var j=i("callbacks"),k=i("publish");class l{constructor(){Object.defineProperty(this,k,{value:m}),this.state={},Object.defineProperty(this,j,{writable:!0,value:new Set})}getState(){return this.state}setState(a){let b={...this.state},c={...this.state,...a};this.state=c,g(this,k)[k](b,c,a)}subscribe(a){return g(this,j)[j].add(a),()=>{g(this,j)[j].delete(a)}}}function m(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];g(this,j)[j].forEach(a=>{a(...b)})}function n(a){let b=a.lastIndexOf(".");return -1===b||b===a.length-1?{name:a,extension:void 0}:{name:a.slice(0,b),extension:a.slice(b+1)}}l.VERSION="4.2.0";let o={__proto__:null,md:"text/markdown",markdown:"text/markdown",mp4:"video/mp4",mp3:"audio/mp3",svg:"image/svg+xml",jpg:"image/jpeg",png:"image/png",webp:"image/webp",gif:"image/gif",heic:"image/heic",heif:"image/heif",yaml:"text/yaml",yml:"text/yaml",csv:"text/csv",tsv:"text/tab-separated-values",tab:"text/tab-separated-values",avi:"video/x-msvideo",mks:"video/x-matroska",mkv:"video/x-matroska",mov:"video/quicktime",dicom:"application/dicom",doc:"application/msword",msg:"application/vnd.ms-outlook",docm:"application/vnd.ms-word.document.macroenabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroenabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",xla:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlc:"application/vnd.ms-excel",xlf:"application/x-xliff+xml",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroenabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xlw:"application/vnd.ms-excel",txt:"text/plain",text:"text/plain",conf:"text/plain",log:"text/plain",pdf:"application/pdf",zip:"application/zip","7z":"application/x-7z-compressed",rar:"application/x-rar-compressed",tar:"application/x-tar",gz:"application/gzip",dmg:"application/x-apple-diskimage"};function p(a){var b;if(a.type)return a.type;let c=a.name?null==(b=n(a.name).extension)?void 0:b.toLowerCase():null;return c&&c in o?o[c]:"application/octet-stream"}function q(a){let b="";return a.replace(/[^A-Z0-9]/gi,a=>(b+=`-${a.charCodeAt(0).toString(32)}`,"/"))+b}function r(a){return a<10?`0${a}`:a.toString()}function s(){let a=new Date,b=r(a.getHours()),c=r(a.getMinutes()),d=r(a.getSeconds());return`${b}:${c}:${d}`}let t={debug:()=>{},warn:()=>{},error:function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return console.error(`[Uppy] [${s()}]`,...b)}},u={debug:function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return console.debug(`[Uppy] [${s()}]`,...b)},warn:function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return console.warn(`[Uppy] [${s()}]`,...b)},error:function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return console.error(`[Uppy] [${s()}]`,...b)}};var v=c(45250),w=c(72758);let x={maxFileSize:null,minFileSize:null,maxTotalFileSize:null,maxNumberOfFiles:null,minNumberOfFiles:null,allowedFileTypes:null,requiredMetaFields:[]};class y extends Error{constructor(a,b){var c;super(a),this.isRestriction=!0,this.isUserFacing=null==(c=null==b?void 0:b.isUserFacing)||c,null!=b&&b.file&&(this.file=b.file)}}class z{constructor(a,b){this.getI18n=b,this.getOpts=()=>{var b;let c=a();if((null==(b=c.restrictions)?void 0:b.allowedFileTypes)!=null&&!Array.isArray(c.restrictions.allowedFileTypes))throw TypeError("`restrictions.allowedFileTypes` must be an array");return c}}validateAggregateRestrictions(a,b){let{maxTotalFileSize:c,maxNumberOfFiles:d}=this.getOpts().restrictions;if(d&&a.filter(a=>!a.isGhost).length+b.length>d)throw new y(`${this.getI18n()("youCanOnlyUploadX",{smart_count:d})}`);if(c){let d=[...a,...b].reduce((a,b)=>{var c;return a+(null!=(c=b.size)?c:0)},0);if(d>c)throw new y(this.getI18n()("aggregateExceedsSize",{sizeAllowed:v(c),size:v(d)}))}}validateSingleFile(a){let{maxFileSize:b,minFileSize:c,allowedFileTypes:d}=this.getOpts().restrictions;if(d&&!d.some(b=>b.includes("/")?!!a.type&&w(a.type.replace(/;.*?$/,""),b):"."===b[0]&&!!a.extension&&a.extension.toLowerCase()===b.slice(1).toLowerCase())){let b=d.join(", ");throw new y(this.getI18n()("youCanOnlyUploadFileTypes",{types:b}),{file:a})}if(b&&null!=a.size&&a.size>b){var e;throw new y(this.getI18n()("exceedsSize",{size:v(b),file:null!=(e=a.name)?e:this.getI18n()("unnamed")}),{file:a})}if(c&&null!=a.size&&a.size<c)throw new y(this.getI18n()("inferiorSize",{size:v(c)}),{file:a})}validate(a,b){b.forEach(a=>{this.validateSingleFile(a)}),this.validateAggregateRestrictions(a,b)}validateMinNumberOfFiles(a){let{minNumberOfFiles:b}=this.getOpts().restrictions;if(b&&Object.keys(a).length<b)throw new y(this.getI18n()("youHaveToAtLeastSelectX",{smart_count:b}))}getMissingRequiredMetaFields(a){var b;let c=new y(this.getI18n()("missingRequiredMetaFieldOnFile",{fileName:null!=(b=a.name)?b:this.getI18n()("unnamed")})),{requiredMetaFields:d}=this.getOpts().restrictions,e=[];for(let b of d)Object.hasOwn(a.meta,b)&&""!==a.meta[b]||e.push(b);return{missingFields:e,error:c}}}let A={strings:{addBulkFilesFailed:{0:"Failed to add %{smart_count} file due to an internal error",1:"Failed to add %{smart_count} files due to internal errors"},youCanOnlyUploadX:{0:"You can only upload %{smart_count} file",1:"You can only upload %{smart_count} files"},youHaveToAtLeastSelectX:{0:"You have to select at least %{smart_count} file",1:"You have to select at least %{smart_count} files"},aggregateExceedsSize:"You selected %{size} of files, but maximum allowed size is %{sizeAllowed}",exceedsSize:"%{file} exceeds maximum allowed size of %{size}",missingRequiredMetaField:"Missing required meta fields",missingRequiredMetaFieldOnFile:"Missing required meta fields in %{fileName}",inferiorSize:"This file is smaller than the allowed size of %{size}",youCanOnlyUploadFileTypes:"You can only upload: %{types}",noMoreFilesAllowed:"Cannot add more files",noDuplicates:"Cannot add the duplicate file '%{fileName}', it already exists",companionError:"Connection with Companion failed",authAborted:"Authentication aborted",companionUnauthorizeHint:"To unauthorize to your %{provider} account, please go to %{url}",failedToUpload:"Failed to upload %{file}",noInternetConnection:"No Internet connection",connectedToInternet:"Connected to the Internet",noFilesFound:"You have no files or folders here",noSearchResults:"Unfortunately, there are no results for this search",selectX:{0:"Select %{smart_count}",1:"Select %{smart_count}"},allFilesFromFolderNamed:"All files from folder %{name}",openFolderNamed:"Open folder %{name}",cancel:"Cancel",logOut:"Log out",logIn:"Log in",pickFiles:"Pick files",pickPhotos:"Pick photos",filter:"Filter",resetFilter:"Reset filter",loading:"Loading...",loadedXFiles:"Loaded %{numFiles} files",authenticateWithTitle:"Please authenticate with %{pluginName} to select files",authenticateWith:"Connect to %{pluginName}",signInWithGoogle:"Sign in with Google",searchImages:"Search for images",enterTextToSearch:"Enter text to search for images",search:"Search",resetSearch:"Reset search",emptyFolderAdded:"No files were added from empty folder",addedNumFiles:"Added %{numFiles} file(s)",folderAlreadyAdded:'The folder "%{folder}" was already added',folderAdded:{0:"Added %{smart_count} file from %{folder}",1:"Added %{smart_count} files from %{folder}"},additionalRestrictionsFailed:"%{count} additional restrictions were not fulfilled",unnamed:"Unnamed",pleaseWait:"Please wait"}};function B(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}var C=0;function D(a){return"__private_"+C+++"_"+a}let E={totalProgress:0,allowNewUpload:!0,error:null,recoveredState:null};var F=D("plugins"),G=D("restricter"),H=D("storeUnsubscribe"),I=D("emitter"),J=D("preProcessors"),K=D("uploaders"),L=D("postProcessors"),M=D("informAndEmit"),N=D("checkRequiredMetaFieldsOnFile"),O=D("checkRequiredMetaFields"),P=D("assertNewUploadAllowed"),Q=D("transformFile"),R=D("startIfAutoProceed"),S=D("checkAndUpdateFileState"),T=D("getFilesToRetry"),U=D("doRetryAll"),V=D("handleUploadProgress"),W=D("updateTotalProgress"),X=D("updateTotalProgressThrottled"),Y=D("calculateTotalProgress"),Z=D("addListeners"),$=D("updateOnlineStatus"),_=D("requestClientById"),aa=D("createUpload"),ab=D("getUpload"),ac=D("removeUpload"),ad=D("runUpload");class ae{constructor(a){Object.defineProperty(this,ad,{value:au}),Object.defineProperty(this,ac,{value:at}),Object.defineProperty(this,ab,{value:as}),Object.defineProperty(this,aa,{value:ar}),Object.defineProperty(this,Z,{value:aq}),Object.defineProperty(this,Y,{value:ap}),Object.defineProperty(this,W,{value:ao}),Object.defineProperty(this,U,{value:an}),Object.defineProperty(this,T,{value:am}),Object.defineProperty(this,S,{value:al}),Object.defineProperty(this,R,{value:ak}),Object.defineProperty(this,Q,{value:aj}),Object.defineProperty(this,P,{value:ai}),Object.defineProperty(this,O,{value:ah}),Object.defineProperty(this,N,{value:ag}),Object.defineProperty(this,M,{value:af}),Object.defineProperty(this,F,{writable:!0,value:Object.create(null)}),Object.defineProperty(this,G,{writable:!0,value:void 0}),Object.defineProperty(this,H,{writable:!0,value:void 0}),Object.defineProperty(this,I,{writable:!0,value:e()}),Object.defineProperty(this,J,{writable:!0,value:new Set}),Object.defineProperty(this,K,{writable:!0,value:new Set}),Object.defineProperty(this,L,{writable:!0,value:new Set}),this.scheduledAutoProceed=null,this.wasOffline=!1,Object.defineProperty(this,V,{writable:!0,value:(a,b)=>{let c=a?this.getFile(a.id):void 0;if(null==a||!c)return void this.log(`Not setting progress for a file that has been removed: ${null==a?void 0:a.id}`);if(100===c.progress.percentage)return void this.log(`Not setting progress for a file that has been already uploaded: ${a.id}`);let d={bytesTotal:b.bytesTotal,percentage:null!=b.bytesTotal&&Number.isFinite(b.bytesTotal)&&b.bytesTotal>0?Math.round(b.bytesUploaded/b.bytesTotal*100):void 0};null!=c.progress.uploadStarted?this.setFileState(a.id,{progress:{...c.progress,...d,bytesUploaded:b.bytesUploaded}}):this.setFileState(a.id,{progress:{...c.progress,...d}}),B(this,X)[X]()}}),Object.defineProperty(this,X,{writable:!0,value:f(()=>B(this,W)[W](),500,{leading:!0,trailing:!0})}),Object.defineProperty(this,$,{writable:!0,value:this.updateOnlineStatus.bind(this)}),Object.defineProperty(this,_,{writable:!0,value:new Map}),this.defaultLocale=A;let b={id:"uppy",autoProceed:!1,allowMultipleUploadBatches:!0,debug:!1,restrictions:x,meta:{},onBeforeFileAdded:(a,b)=>!Object.hasOwn(b,a.id),onBeforeUpload:a=>a,store:new l,logger:t,infoTimeout:5e3},c={...b,...a};this.opts={...c,restrictions:{...b.restrictions,...a&&a.restrictions}},a&&a.logger&&a.debug?this.log("You are using a custom `logger`, but also set `debug: true`, which uses built-in logger to output logs to console. Ignoring `debug: true` and using your custom `logger`.","warning"):a&&a.debug&&(this.opts.logger=u),this.log(`Using Core v${ae.VERSION}`),this.i18nInit(),this.store=this.opts.store,this.setState({...E,plugins:{},files:{},currentUploads:{},capabilities:{uploadProgress:function(a){if(null==a&&"undefined"!=typeof navigator&&(a=navigator.userAgent),!a)return!0;let b=/Edge\/(\d+\.\d+)/.exec(a);if(!b)return!0;let c=b[1].split(".",2),d=parseInt(c[0],10),e=parseInt(c[1],10);return!!(d<15)||15===d&&!!(e<15063)||!!(d>18)||18===d&&!!(e>=18218)}(),individualCancellation:!0,resumableUploads:!1},meta:{...this.opts.meta},info:[]}),B(this,G)[G]=new z(()=>this.opts,()=>this.i18n),B(this,H)[H]=this.store.subscribe((a,b,c)=>{this.emit("state-update",a,b,c),this.updateAll(b)}),this.opts.debug&&"undefined"!=typeof window&&(window[this.opts.id]=this),B(this,Z)[Z]()}emit(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];B(this,I)[I].emit(a,...c)}on(a,b){return B(this,I)[I].on(a,b),this}once(a,b){return B(this,I)[I].once(a,b),this}off(a,b){return B(this,I)[I].off(a,b),this}updateAll(a){this.iteratePlugins(b=>{b.update(a)})}setState(a){this.store.setState(a)}getState(){return this.store.getState()}patchFilesState(a){let b=this.getState().files;this.setState({files:{...b,...Object.fromEntries(Object.entries(a).map(a=>{let[c,d]=a;return[c,{...b[c],...d}]}))}})}setFileState(a,b){if(!this.getState().files[a])throw Error(`Can’t set state for ${a} (the file could have been removed)`);this.patchFilesState({[a]:b})}i18nInit(){let a=new d.A([this.defaultLocale,this.opts.locale],{onMissingKey:a=>this.log(`Missing i18n string: ${a}`,"error")});this.i18n=a.translate.bind(a),this.i18nArray=a.translateArray.bind(a),this.locale=a.locale}setOptions(a){this.opts={...this.opts,...a,restrictions:{...this.opts.restrictions,...null==a?void 0:a.restrictions}},a.meta&&this.setMeta(a.meta),this.i18nInit(),a.locale&&this.iteratePlugins(b=>{b.setOptions(a)}),this.setState(void 0)}resetProgress(){let a={percentage:0,bytesUploaded:!1,uploadComplete:!1,uploadStarted:null},b={...this.getState().files},c=Object.create(null);Object.keys(b).forEach(d=>{c[d]={...b[d],progress:{...b[d].progress,...a},tus:void 0,transloadit:void 0}}),this.setState({files:c,...E})}clear(){let{capabilities:a,currentUploads:b}=this.getState();if(Object.keys(b).length>0&&!a.individualCancellation)throw Error("The installed uploader plugin does not allow removing files during an upload.");this.setState({...E,files:{}})}addPreProcessor(a){B(this,J)[J].add(a)}removePreProcessor(a){return B(this,J)[J].delete(a)}addPostProcessor(a){B(this,L)[L].add(a)}removePostProcessor(a){return B(this,L)[L].delete(a)}addUploader(a){B(this,K)[K].add(a)}removeUploader(a){return B(this,K)[K].delete(a)}setMeta(a){let b={...this.getState().meta,...a},c={...this.getState().files};Object.keys(c).forEach(b=>{c[b]={...c[b],meta:{...c[b].meta,...a}}}),this.log("Adding metadata:"),this.log(a),this.setState({meta:b,files:c})}setFileMeta(a,b){let c={...this.getState().files};if(!c[a])return void this.log(`Was trying to set metadata for a file that has been removed: ${a}`);let d={...c[a].meta,...b};c[a]={...c[a],meta:d},this.setState({files:c})}getFile(a){return this.getState().files[a]}getFiles(){let{files:a}=this.getState();return Object.values(a)}getFilesByIds(a){return a.map(a=>this.getFile(a))}getObjectOfFilesPerState(){let{files:a,totalProgress:b,error:c}=this.getState(),d=Object.values(a),e=[],f=[],g=[],h=[],i=[],j=[],k=[],l=[],m=[];for(let a of d){let{progress:b}=a;!b.uploadComplete&&b.uploadStarted&&(e.push(a),a.isPaused||l.push(a)),b.uploadStarted||f.push(a),(b.uploadStarted||b.preprocess||b.postprocess)&&g.push(a),b.uploadStarted&&h.push(a),a.isPaused&&i.push(a),b.uploadComplete&&j.push(a),a.error&&k.push(a),(b.preprocess||b.postprocess)&&m.push(a)}return{newFiles:f,startedFiles:g,uploadStartedFiles:h,pausedFiles:i,completeFiles:j,erroredFiles:k,inProgressFiles:e,inProgressNotPausedFiles:l,processingFiles:m,isUploadStarted:h.length>0,isAllComplete:100===b&&j.length===d.length&&0===m.length,isAllErrored:!!c&&k.length===d.length,isAllPaused:0!==e.length&&i.length===e.length,isUploadInProgress:e.length>0,isSomeGhost:d.some(a=>a.isGhost)}}validateRestrictions(a,b){void 0===b&&(b=this.getFiles());try{B(this,G)[G].validate(b,[a])}catch(a){return a}return null}validateSingleFile(a){try{B(this,G)[G].validateSingleFile(a)}catch(a){return a.message}return null}validateAggregateRestrictions(a){let b=this.getFiles();try{B(this,G)[G].validateAggregateRestrictions(b,a)}catch(a){return a.message}return null}checkIfFileAlreadyExists(a){let{files:b}=this.getState();return!!b[a]&&!b[a].isGhost}addFile(a){B(this,P)[P](a);let{nextFilesState:b,validFilesToAdd:c,errors:d}=B(this,S)[S]([a]),e=d.filter(a=>a.isRestriction);if(B(this,M)[M](e),d.length>0)throw d[0];this.setState({files:b});let[f]=c;return this.emit("file-added",f),this.emit("files-added",c),this.log(`Added file: ${f.name}, ${f.id}, mime type: ${f.type}`),B(this,R)[R](),f.id}addFiles(a){B(this,P)[P]();let{nextFilesState:b,validFilesToAdd:c,errors:d}=B(this,S)[S](a),e=d.filter(a=>a.isRestriction);B(this,M)[M](e);let f=d.filter(a=>!a.isRestriction);if(f.length>0){let a="Multiple errors occurred while adding files:\n";if(f.forEach(b=>{a+=`
 * ${b.message}`}),this.info({message:this.i18n("addBulkFilesFailed",{smart_count:f.length}),details:a},"error",this.opts.infoTimeout),"function"==typeof AggregateError)throw AggregateError(f,a);{let b=Error(a);throw b.errors=f,b}}this.setState({files:b}),c.forEach(a=>{this.emit("file-added",a)}),this.emit("files-added",c),c.length>5?this.log(`Added batch of ${c.length} files`):Object.values(c).forEach(a=>{this.log(`Added file: ${a.name}
 id: ${a.id}
 type: ${a.type}`)}),c.length>0&&B(this,R)[R]()}removeFiles(a){let{files:b,currentUploads:c}=this.getState(),d={...b},e={...c},f=Object.create(null);function g(a){return void 0===f[a]}a.forEach(a=>{b[a]&&(f[a]=b[a],delete d[a])}),Object.keys(e).forEach(a=>{let b=c[a].fileIDs.filter(g);if(0===b.length)return void delete e[a];let{capabilities:d}=this.getState();if(b.length!==c[a].fileIDs.length&&!d.individualCancellation)throw Error("The installed uploader plugin does not allow removing files during an upload.");e[a]={...c[a],fileIDs:b}});let h={currentUploads:e,files:d};0===Object.keys(d).length&&(h.allowNewUpload=!0,h.error=null,h.recoveredState=null),this.setState(h),B(this,X)[X]();let i=Object.keys(f);i.forEach(a=>{this.emit("file-removed",f[a])}),i.length>5?this.log(`Removed ${i.length} files`):this.log(`Removed files: ${i.join(", ")}`)}removeFile(a){this.removeFiles([a])}pauseResume(a){if(!this.getState().capabilities.resumableUploads||this.getFile(a).progress.uploadComplete)return;let b=this.getFile(a),c=!b.isPaused;return this.setFileState(a,{isPaused:c}),this.emit("upload-pause",b,c),c}pauseAll(){let a={...this.getState().files};Object.keys(a).filter(b=>!a[b].progress.uploadComplete&&a[b].progress.uploadStarted).forEach(b=>{let c={...a[b],isPaused:!0};a[b]=c}),this.setState({files:a}),this.emit("pause-all")}resumeAll(){let a={...this.getState().files};Object.keys(a).filter(b=>!a[b].progress.uploadComplete&&a[b].progress.uploadStarted).forEach(b=>{let c={...a[b],isPaused:!1,error:null};a[b]=c}),this.setState({files:a}),this.emit("resume-all")}async retryAll(){let a=await B(this,U)[U]();return this.emit("complete",a),a}cancelAll(){this.emit("cancel-all");let{files:a}=this.getState(),b=Object.keys(a);b.length&&this.removeFiles(b),this.setState(E)}retryUpload(a){this.setFileState(a,{error:null,isPaused:!1}),this.emit("upload-retry",this.getFile(a));let b=B(this,aa)[aa]([a],{forceAllowNewUpload:!0});return B(this,ad)[ad](b)}logout(){this.iteratePlugins(a=>{var b;null==(b=a.provider)||null==b.logout||b.logout()})}[Symbol.for("uppy test: updateTotalProgress")](){return B(this,W)[W]()}updateOnlineStatus(){var a;null==(a=window.navigator.onLine)||a?(this.emit("is-online"),this.wasOffline&&(this.emit("back-online"),this.info(this.i18n("connectedToInternet"),"success",3e3),this.wasOffline=!1)):(this.emit("is-offline"),this.info(this.i18n("noInternetConnection"),"error",0),this.wasOffline=!0)}getID(){return this.opts.id}use(a){if("function"!=typeof a)throw TypeError(`Expected a plugin class, but got ${null===a?"null":typeof a}. Please verify that the plugin was imported and spelled correctly.`);for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];let e=new a(this,...c),f=e.id;if(!f)throw Error("Your plugin must have an id");if(!e.type)throw Error("Your plugin must have a type");let g=this.getPlugin(f);if(g)throw Error(`Already found a plugin named '${g.id}'. Tried to use: '${f}'.
Uppy plugins must have unique \`id\` options.`);return a.VERSION&&this.log(`Using ${f} v${a.VERSION}`),e.type in B(this,F)[F]?B(this,F)[F][e.type].push(e):B(this,F)[F][e.type]=[e],e.install(),this.emit("plugin-added",e),this}getPlugin(a){for(let b of Object.values(B(this,F)[F])){let c=b.find(b=>b.id===a);if(null!=c)return c}}[Symbol.for("uppy test: getPlugins")](a){return B(this,F)[F][a]}iteratePlugins(a){Object.values(B(this,F)[F]).flat(1).forEach(a)}removePlugin(a){this.log(`Removing plugin ${a.id}`),this.emit("plugin-remove",a),a.uninstall&&a.uninstall();let b=B(this,F)[F][a.type],c=b.findIndex(b=>b.id===a.id);-1!==c&&b.splice(c,1);let d={plugins:{...this.getState().plugins,[a.id]:void 0}};this.setState(d)}destroy(){this.log(`Closing Uppy instance ${this.opts.id}: removing all files and uninstalling plugins`),this.cancelAll(),B(this,H)[H](),this.iteratePlugins(a=>{this.removePlugin(a)}),"undefined"!=typeof window&&window.removeEventListener&&(window.removeEventListener("online",B(this,$)[$]),window.removeEventListener("offline",B(this,$)[$]))}hideInfo(){let{info:a}=this.getState();this.setState({info:a.slice(1)}),this.emit("info-hidden")}info(a,b,c){void 0===b&&(b="info"),void 0===c&&(c=3e3);let d="object"==typeof a;this.setState({info:[...this.getState().info,{type:b,message:d?a.message:a,details:d?a.details:null}]}),setTimeout(()=>this.hideInfo(),c),this.emit("info-visible")}log(a,b){let{logger:c}=this.opts;switch(b){case"error":c.error(a);break;case"warning":c.warn(a);break;default:c.debug(a)}}registerRequestClient(a,b){B(this,_)[_].set(a,b)}getRequestClientForFile(a){if(!a.remote)throw Error(`Tried to get RequestClient for a non-remote file ${a.id}`);let b=B(this,_)[_].get(a.remote.requestClientId);if(null==b)throw Error(`requestClientId "${a.remote.requestClientId}" not registered for file "${a.id}"`);return b}restore(a){return(this.log(`Core: attempting to restore upload "${a}"`),this.getState().currentUploads[a])?B(this,ad)[ad](a):(B(this,ac)[ac](a),Promise.reject(Error("Nonexistent upload")))}[Symbol.for("uppy test: createUpload")](){return B(this,aa)[aa](...arguments)}addResultData(a,b){if(!B(this,ab)[ab](a))return void this.log(`Not setting result for an upload that has been removed: ${a}`);let{currentUploads:c}=this.getState(),d={...c[a],result:{...c[a].result,...b}};this.setState({currentUploads:{...c,[a]:d}})}async upload(){var a;null!=(a=B(this,F)[F].uploader)&&a.length||this.log("No uploader type plugins are used","warning");let{files:b}=this.getState();if(B(this,T)[T]().length>0){let a=await B(this,U)[U]();if(!(this.getFiles().filter(a=>null==a.progress.uploadStarted).length>0))return this.emit("complete",a),a;({files:b}=this.getState())}let c=this.opts.onBeforeUpload(b);return!1===c?Promise.reject(Error("Not starting the upload because onBeforeUpload returned false")):(c&&"object"==typeof c&&(b=c,this.setState({files:b})),Promise.resolve().then(()=>B(this,G)[G].validateMinNumberOfFiles(b)).catch(a=>{throw B(this,M)[M]([a]),a}).then(()=>{if(!B(this,O)[O](b))throw new y(this.i18n("missingRequiredMetaField"))}).catch(a=>{throw a}).then(async()=>{let{currentUploads:a}=this.getState(),c=Object.values(a).flatMap(a=>a.fileIDs),d=[];Object.keys(b).forEach(a=>{let b=this.getFile(a);b.progress.uploadStarted||-1!==c.indexOf(a)||d.push(b.id)});let e=B(this,aa)[aa](d),f=await B(this,ad)[ad](e);return this.emit("complete",f),f}).catch(a=>{throw this.emit("error",a),this.log(a,"error"),a}))}}function af(a){for(let b of a)b.isRestriction?this.emit("restriction-failed",b.file,b):this.emit("error",b,b.file),this.log(b,"warning");let b=a.filter(a=>a.isUserFacing),c=b.slice(0,4),d=b.slice(4);c.forEach(a=>{let{message:b,details:c=""}=a;this.info({message:b,details:c},"error",this.opts.infoTimeout)}),d.length>0&&this.info({message:this.i18n("additionalRestrictionsFailed",{count:d.length})})}function ag(a){let{missingFields:b,error:c}=B(this,G)[G].getMissingRequiredMetaFields(a);return b.length>0?(this.setFileState(a.id,{missingRequiredMetaFields:b}),this.log(c.message),this.emit("restriction-failed",a,c),!1):(0===b.length&&a.missingRequiredMetaFields&&this.setFileState(a.id,{missingRequiredMetaFields:[]}),!0)}function ah(a){let b=!0;for(let c of Object.values(a))B(this,N)[N](c)||(b=!1);return b}function ai(a){let{allowNewUpload:b}=this.getState();if(!1===b){let b=new y(this.i18n("noMoreFilesAllowed"),{file:a});throw B(this,M)[M]([b]),b}}function aj(a){let b=a instanceof File?{name:a.name,type:a.type,size:a.size,data:a}:a,c=p(b),d=b.name?b.name:"image"===c.split("/")[0]?`${c.split("/")[0]}.${c.split("/")[1]}`:"noname",e=n(d).extension,f=function(a,b){var c;let d;if(a.isRemote&&a.remote&&new Set(["box","dropbox","drive","facebook","unsplash"]).has(a.remote.provider))return a.id;let e=p(a);return c={...a,type:e},d=b||"uppy","string"==typeof c.name&&(d+=`-${q(c.name.toLowerCase())}`),void 0!==c.type&&(d+=`-${c.type}`),c.meta&&"string"==typeof c.meta.relativePath&&(d+=`-${q(c.meta.relativePath.toLowerCase())}`),void 0!==c.data.size&&(d+=`-${c.data.size}`),void 0!==c.data.lastModified&&(d+=`-${c.data.lastModified}`),d}(b,this.getID()),g=b.meta||{};g.name=d,g.type=c;let h=Number.isFinite(b.data.size)?b.data.size:null;return{source:b.source||"",id:f,name:d,extension:e||"",meta:{...this.getState().meta,...g},type:c,data:b.data,progress:{percentage:0,bytesUploaded:!1,bytesTotal:h,uploadComplete:!1,uploadStarted:null},size:h,isGhost:!1,isRemote:b.isRemote||!1,remote:b.remote,preview:b.preview}}function ak(){this.opts.autoProceed&&!this.scheduledAutoProceed&&(this.scheduledAutoProceed=setTimeout(()=>{this.scheduledAutoProceed=null,this.upload().catch(a=>{a.isRestriction||this.log(a.stack||a.message||a)})},4))}function al(a){let{files:b}=this.getState(),c={...b},d=[],e=[];for(let h of a)try{var f,g;let a=B(this,Q)[Q](h),e=null==(f=b[a.id])?void 0:f.isGhost;e&&(a={...b[a.id],isGhost:!1,data:h.data},this.log(`Replaced the blob in the restored ghost file: ${a.name}, ${a.id}`));let i=this.opts.onBeforeFileAdded(a,c);if(!i&&this.checkIfFileAlreadyExists(a.id))throw new y(this.i18n("noDuplicates",{fileName:null!=(g=a.name)?g:this.i18n("unnamed")}),{file:h});if(!1!==i||e)"object"==typeof i&&null!==i&&(a=i);else throw new y("Cannot add the file because onBeforeFileAdded returned false.",{isUserFacing:!1,file:h});B(this,G)[G].validateSingleFile(a),c[a.id]=a,d.push(a)}catch(a){e.push(a)}try{B(this,G)[G].validateAggregateRestrictions(Object.values(b),d)}catch(a){return e.push(a),{nextFilesState:b,validFilesToAdd:[],errors:e}}return{nextFilesState:c,validFilesToAdd:d,errors:e}}function am(){let{files:a}=this.getState();return Object.keys(a).filter(b=>a[b].error)}async function an(){let a=B(this,T)[T](),b={...this.getState().files};if(a.forEach(a=>{b[a]={...b[a],isPaused:!1,error:null}}),this.setState({files:b,error:null}),this.emit("retry-all",this.getFilesByIds(a)),0===a.length)return{successful:[],failed:[]};let c=B(this,aa)[aa](a,{forceAllowNewUpload:!0});return B(this,ad)[ad](c)}function ao(){var a,b;let c=B(this,Y)[Y](),d=null;null!=c&&((d=Math.round(100*c))>100?d=100:d<0&&(d=0)),this.emit("progress",null!=(a=d)?a:0),this.setState({totalProgress:null!=(b=d)?b:0})}function ap(){let a=this.getFiles().filter(a=>a.progress.uploadStarted||a.progress.preprocess||a.progress.postprocess);if(0===a.length)return 0;if(a.every(a=>a.progress.uploadComplete))return 1;let b=a=>null!=a.progress.bytesTotal&&0!==a.progress.bytesTotal,c=a.filter(b),d=a.filter(a=>!b(a));if(c.every(a=>a.progress.uploadComplete)&&d.length>0&&!d.every(a=>a.progress.uploadComplete))return null;let e=c.reduce((a,b)=>{var c;return a+(null!=(c=b.progress.bytesTotal)?c:0)},0),f=c.reduce((a,b)=>a+(b.progress.bytesUploaded||0),0);return 0===e?0:f/e}function aq(){let a=(a,b,c)=>{let d=a.message||"Unknown error";a.details&&(d+=` ${a.details}`),this.setState({error:d}),null!=b&&b.id in this.getState().files&&this.setFileState(b.id,{error:d,response:c})};this.on("error",a),this.on("upload-error",(b,c,d)=>{if(a(c,b,d),"object"==typeof c&&c.message){var e;this.log(c.message,"error");let a=Error(this.i18n("failedToUpload",{file:null!=(e=null==b?void 0:b.name)?e:""}));a.isUserFacing=!0,a.details=c.message,c.details&&(a.details+=` ${c.details}`),B(this,M)[M]([a])}else B(this,M)[M]([c])});let b=null;this.on("upload-stalled",(a,c)=>{let{message:d}=a,e=c.map(a=>a.meta.name).join(", ");b||(this.info({message:d,details:e},"warning",this.opts.infoTimeout),b=setTimeout(()=>{b=null},this.opts.infoTimeout)),this.log(`${d} ${e}`.trim(),"warning")}),this.on("upload",()=>{this.setState({error:null})}),this.on("upload-start",a=>{let b=Object.fromEntries(a.filter(a=>{let b=null!=a&&this.getFile(a.id);return b||this.log(`Not setting progress for a file that has been removed: ${null==a?void 0:a.id}`),b}).map(a=>[a.id,{progress:{uploadStarted:Date.now(),uploadComplete:!1,bytesUploaded:0,bytesTotal:a.size}}]));this.patchFilesState(b)}),this.on("upload-progress",B(this,V)[V]),this.on("upload-success",(a,b)=>{if(null==a||!this.getFile(a.id))return void this.log(`Not setting progress for a file that has been removed: ${null==a?void 0:a.id}`);let c=this.getFile(a.id).progress;this.setFileState(a.id,{progress:{...c,postprocess:B(this,L)[L].size>0?{mode:"indeterminate"}:void 0,uploadComplete:!0,percentage:100,bytesUploaded:c.bytesTotal},response:b,uploadURL:b.uploadURL,isPaused:!1}),null==a.size&&this.setFileState(a.id,{size:b.bytesUploaded||c.bytesTotal}),B(this,X)[X]()}),this.on("preprocess-progress",(a,b)=>{if(null==a||!this.getFile(a.id))return void this.log(`Not setting progress for a file that has been removed: ${null==a?void 0:a.id}`);this.setFileState(a.id,{progress:{...this.getFile(a.id).progress,preprocess:b}})}),this.on("preprocess-complete",a=>{if(null==a||!this.getFile(a.id))return void this.log(`Not setting progress for a file that has been removed: ${null==a?void 0:a.id}`);let b={...this.getState().files};b[a.id]={...b[a.id],progress:{...b[a.id].progress}},delete b[a.id].progress.preprocess,this.setState({files:b})}),this.on("postprocess-progress",(a,b)=>{if(null==a||!this.getFile(a.id))return void this.log(`Not setting progress for a file that has been removed: ${null==a?void 0:a.id}`);this.setFileState(a.id,{progress:{...this.getState().files[a.id].progress,postprocess:b}})}),this.on("postprocess-complete",a=>{if(null==a||!this.getFile(a.id))return void this.log(`Not setting progress for a file that has been removed: ${null==a?void 0:a.id}`);let b={...this.getState().files};b[a.id]={...b[a.id],progress:{...b[a.id].progress}},delete b[a.id].progress.postprocess,this.setState({files:b})}),this.on("restored",()=>{B(this,X)[X]()}),this.on("dashboard:file-edit-complete",a=>{a&&B(this,N)[N](a)}),"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("online",B(this,$)[$]),window.addEventListener("offline",B(this,$)[$]),setTimeout(B(this,$)[$],3e3))}function ar(a,b){void 0===b&&(b={});let{forceAllowNewUpload:c=!1}=b,{allowNewUpload:d,currentUploads:e}=this.getState();if(!d&&!c)throw Error("Cannot create a new upload: already uploading.");let f=((a=21)=>{let b="",c=0|a;for(;c--;)b+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[64*Math.random()|0];return b})();return this.emit("upload",f,this.getFilesByIds(a)),this.setState({allowNewUpload:!1!==this.opts.allowMultipleUploadBatches&&!1!==this.opts.allowMultipleUploads,currentUploads:{...e,[f]:{fileIDs:a,step:0,result:{}}}}),f}function as(a){let{currentUploads:b}=this.getState();return b[a]}function at(a){let b={...this.getState().currentUploads};delete b[a],this.setState({currentUploads:b})}async function au(a){let b,c=()=>{let{currentUploads:b}=this.getState();return b[a]},d=c(),e=[...B(this,J)[J],...B(this,K)[K],...B(this,L)[L]];try{for(let b=d.step||0;b<e.length&&d;b++){let f=e[b];this.setState({currentUploads:{...this.getState().currentUploads,[a]:{...d,step:b}}});let{fileIDs:g}=d;await f(g,a),d=c()}}catch(b){throw B(this,ac)[ac](a),b}if(d){d.fileIDs.forEach(a=>{let b=this.getFile(a);b&&b.progress.postprocess&&this.emit("postprocess-complete",b)});let b=d.fileIDs.map(a=>this.getFile(a)),e=b.filter(a=>!a.error),f=b.filter(a=>a.error);this.addResultData(a,{successful:e,failed:f,uploadID:a}),d=c()}return d&&(b=d.result,B(this,ac)[ac](a)),null==b&&(this.log(`Not setting result for an upload that has been removed: ${a}`),b={successful:[],failed:[],uploadID:a}),b}ae.VERSION="4.4.7";let av=ae},90131:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},93028:(a,b,c)=>{"use strict";function d(a,b){if(!({}).hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}c.d(b,{A:()=>k});var e=0;function f(a){return"__private_"+e+++"_"+a}function g(a,b){let c=/\$/g,d=[a];if(null==b)return d;for(let a of Object.keys(b))if("_"!==a){let e=b[a];"string"==typeof e&&(e=c[Symbol.replace](e,"$$$$")),d=function(a,b,c){let d=[];return a.forEach(a=>"string"!=typeof a?d.push(a):b[Symbol.split](a).forEach((a,b,e)=>{""!==a&&d.push(a),b<e.length-1&&d.push(c)})),d}(d,RegExp(`%\\{${a}\\}`,"g"),e)}return d}let h=a=>{throw Error(`missing string: ${a}`)};var i=f("onMissingKey"),j=f("apply");class k{constructor(a,b){let{onMissingKey:c=h}=void 0===b?{}:b;Object.defineProperty(this,j,{value:l}),Object.defineProperty(this,i,{writable:!0,value:void 0}),this.locale={strings:{},pluralize:a=>+(1!==a)},Array.isArray(a)?a.forEach(d(this,j)[j],this):d(this,j)[j](a),d(this,i)[i]=c}translate(a,b){return this.translateArray(a,b).join("")}translateArray(a,b){let c=this.locale.strings[a];if(null==c&&(d(this,i)[i](a),c=a),"object"==typeof c){if(b&&void 0!==b.smart_count)return g(c[this.locale.pluralize(b.smart_count)],b);throw Error("Attempted to use a string with plural forms, but no value was given for %{smart_count}")}if("string"!=typeof c)throw Error("string was not a string");return g(c,b)}}function l(a){if(!(null!=a&&a.strings))return;let b=this.locale;Object.assign(this.locale,{strings:{...b.strings,...a.strings},pluralize:a.pluralize||b.pluralize})}},97576:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(86897),e=c(49026),f=c(62765),g=c(48976),h=c(70899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},98866:()=>{}};