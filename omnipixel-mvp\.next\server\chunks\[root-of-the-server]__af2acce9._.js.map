{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\r\nimport { cookies } from 'next/headers'\r\n\r\nexport async function createClient(url?:string, key?:string) {\r\n  const cookieStore = await cookies()\r\n  url = url ?? process.env.NEXT_PUBLIC_SUPABASE_URL!\r\n  key = key ?? process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\n  return createServerClient(\r\n    url,\r\n    key,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll()\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            )\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe,aAAa,GAAW,EAAE,GAAW;IACzD,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM;IACN,MAAM;IAEN,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/app/api/projects/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/utils/supabase/server';\nimport { Build } from '@/lib/supabase';\n\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // Create Supabase client for server-side auth\n    const supabase = await createClient();\n\n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const {id:projectId} = await params\n\n    // Parse request body\n    const body = await request.json()\n    const { name, config, auto_release } = body\n\n    // Validate that at least one field is provided\n    if (!name && !config && auto_release === undefined) {\n      return NextResponse.json(\n        { error: 'At least one field (name, config, or auto_release) must be provided' },\n        { status: 400 }\n      )\n    }\n\n    // Prepare update data (allow name, config, and auto_release updates for regular users)\n    const updateData: Record<string, unknown> = {}\n    if (name) updateData.name = name\n    if (config) updateData.config = config\n    if (auto_release !== undefined) updateData.auto_release = auto_release\n\n    // Verify that the project belongs to the user\n    const { data: project, error: projectError } = await supabase\n      .from('projects')\n      .select('id, user_id, name')\n      .eq('id', projectId)\n      .eq('user_id', user.id)\n      .single()\n\n    if (projectError || !project) {\n      return NextResponse.json(\n        { error: 'Project not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Update the project\n    const { data: updatedProject, error: updateError } = await supabase\n      .from('projects')\n      .update(updateData)\n      .eq('id', projectId)\n      .eq('user_id', user.id)\n      .select()\n      .single()\n\n    if (updateError) {\n      console.error('Error updating project:', updateError)\n      return NextResponse.json(\n        { error: 'Failed to update project' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({\n      project: updatedProject,\n      message: 'Project updated successfully',\n    })\n\n  } catch (error) {\n    console.error('Error in project update:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // Create Supabase client for server-side auth\n    const supabase = await createClient();\n\n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const {id:projectId} = await params\n\n    // Fetch the project with builds\n    const { data: project, error: projectError } = await supabase\n      .from('projects')\n      .select(`\n        *,\n        builds (\n          id,\n          filename,\n          original_filename,\n          s3_key,\n          version,\n          status,\n          is_current,\n          file_size,\n          streampixel_build_id,\n          streampixel_status,\n          error_message,\n          created_at,\n          updated_at\n        )\n      `)\n      .eq('id', projectId)\n      .eq('user_id', user.id)\n      .single()\n\n    if (projectError || !project) {\n      return NextResponse.json(\n        { error: 'Project not found or access denied' },\n        { status: 404 }\n      )\n    }\n\n    // Sort builds by version descending\n    const projectWithSortedBuilds = {\n      ...project,\n      builds: project.builds\n        ?.sort((a:Build, b:Build) => b.version - a.version) || []\n    }\n\n    return NextResponse.json({\n      project: projectWithSortedBuilds,\n    })\n\n  } catch (error) {\n    console.error('Error fetching project:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,8CAA8C;QAC9C,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;QAElC,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAC,IAAG,SAAS,EAAC,GAAG,MAAM;QAE7B,qBAAqB;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;QAEvC,+CAA+C;QAC/C,IAAI,CAAC,QAAQ,CAAC,UAAU,iBAAiB,WAAW;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsE,GAC/E;gBAAE,QAAQ;YAAI;QAElB;QAEA,uFAAuF;QACvF,MAAM,aAAsC,CAAC;QAC7C,IAAI,MAAM,WAAW,IAAI,GAAG;QAC5B,IAAI,QAAQ,WAAW,MAAM,GAAG;QAChC,IAAI,iBAAiB,WAAW,WAAW,YAAY,GAAG;QAE1D,8CAA8C;QAC9C,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,qBACP,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;QAET,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AACO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,8CAA8C;QAC9C,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;QAElC,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAC,IAAG,SAAS,EAAC,GAAG,MAAM;QAE7B,gCAAgC;QAChC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;MAiBT,CAAC,EACA,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,MAAM;QAET,IAAI,gBAAgB,CAAC,SAAS;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,oCAAoC;QACpC,MAAM,0BAA0B;YAC9B,GAAG,OAAO;YACV,QAAQ,QAAQ,MAAM,EAClB,KAAK,CAAC,GAAS,IAAY,EAAE,OAAO,GAAG,EAAE,OAAO,KAAK,EAAE;QAC7D;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}