"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1515],{968:(e,t,n)=>{n.d(t,{b:()=>s});var r=n(2115),a=n(3655),o=n(5155),l=r.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=l},1154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5169:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5452:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>ea,ZL:()=>ee,bL:()=>X,bm:()=>eo,hE:()=>er,hJ:()=>et,l9:()=>$});var r=n(2115),a=n(5185),o=n(6101),l=n(6081),s=n(1285),i=n(5845),d=n(9178),c=n(7900),u=n(4378),p=n(8905),f=n(3655),y=n(2293),h=n(3795),g=n(8168),v=n(9708),m=n(5155),k="Dialog",[x,D]=(0,l.A)(k),[A,b]=x(k),w=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,c=r.useRef(null),u=r.useRef(null),[p,f]=(0,i.i)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:k});return(0,m.jsx)(A,{scope:t,triggerRef:c,contentRef:u,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};w.displayName=k;var j="DialogTrigger",M=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=b(j,n),s=(0,o.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Z(l.open),...r,ref:s,onClick:(0,a.m)(e.onClick,l.onOpenToggle)})});M.displayName=j;var C="DialogPortal",[R,I]=x(C,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:o}=e,l=b(C,t);return(0,m.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(a,e=>(0,m.jsx)(p.C,{present:n||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};N.displayName=C;var O="DialogOverlay",_=r.forwardRef((e,t)=>{let n=I(O,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=b(O,e.__scopeDialog);return o.modal?(0,m.jsx)(p.C,{present:r||o.open,children:(0,m.jsx)(F,{...a,ref:t})}):null});_.displayName=O;var E=(0,v.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(O,n);return(0,m.jsx)(h.A,{as:E,allowPinchZoom:!0,shards:[a.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":Z(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",q=r.forwardRef((e,t)=>{let n=I(P,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=b(P,e.__scopeDialog);return(0,m.jsx)(p.C,{present:r||o.open,children:o.modal?(0,m.jsx)(G,{...a,ref:t}):(0,m.jsx)(T,{...a,ref:t})})});q.displayName=P;var G=r.forwardRef((e,t)=>{let n=b(P,e.__scopeDialog),l=r.useRef(null),s=(0,o.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,m.jsx)(V,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=r.forwardRef((e,t)=>{let n=b(P,e.__scopeDialog),a=r.useRef(!1),o=r.useRef(!1);return(0,m.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(a.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let s=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),V=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:s,...i}=e,u=b(P,n),p=r.useRef(null),f=(0,o.s)(t,p);return(0,y.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:l,onUnmountAutoFocus:s,children:(0,m.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...i,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(Y,{titleId:u.titleId}),(0,m.jsx)(Q,{contentRef:p,descriptionId:u.descriptionId})]})]})}),z="DialogTitle",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(z,n);return(0,m.jsx)(f.sG.h2,{id:a.titleId,...r,ref:t})});B.displayName=z;var L="DialogDescription",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(L,n);return(0,m.jsx)(f.sG.p,{id:a.descriptionId,...r,ref:t})});H.displayName=L;var W="DialogClose",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(W,n);return(0,m.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}S.displayName=W;var U="DialogTitleWarning",[J,K]=(0,l.q)(U,{contentName:P,titleName:z,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=K(U),a="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(a))},[a,t]),null},Q=e=>{let{contentRef:t,descriptionId:n}=e,a=K("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},X=w,$=M,ee=N,et=_,en=q,er=B,ea=H,eo=S},6287:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},7108:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}}]);