"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2285],{1007:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2664:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=t(9991),o=t(7102);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let r=(0,n.getLocationOrigin)(),t=new URL(e,r);return t.origin===r&&(0,o.hasBasePath)(t.pathname)}catch(e){return!1}}},2757:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return l},urlObjectKeys:function(){return u}});let n=t(6966)._(t(8859)),o=/https?|ftp|gopher|file/;function a(e){let{auth:r,hostname:t}=e,a=e.protocol||"",u=e.pathname||"",l=e.hash||"",i=e.query||"",c=!1;r=r?encodeURIComponent(r).replace(/%3A/i,":")+"@":"",e.host?c=r+e.host:t&&(c=r+(~t.indexOf(":")?"["+t+"]":t),e.port&&(c+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let s=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==c?(c="//"+(c||""),u&&"/"!==u[0]&&(u="/"+u)):c||(c=""),l&&"#"!==l[0]&&(l="#"+l),s&&"?"!==s[0]&&(s="?"+s),""+a+c+(u=u.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+l}let u=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return a(e)}},3180:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}},4835:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5525:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,r,t)=>{var n=t(8999);t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}})},6654:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=t(2115);function o(e,r){let t=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=t.current;e&&(t.current=null,e());let r=o.current;r&&(o.current=null,r())}else e&&(t.current=a(e,n)),r&&(o.current=a(r,n))},[e,r])}function a(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},6874:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return v},useLinkStatus:function(){return y}});let n=t(6966),o=t(5155),a=n._(t(2115)),u=t(2757),l=t(5227),i=t(9818),c=t(6654),s=t(9991),d=t(5929);t(3230);let f=t(4930),p=t(2664),h=t(6634);function m(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}function v(e){let r,t,n,[u,v]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:w,as:x,children:b,prefetch:C=null,passHref:M,replace:j,shallow:_,scroll:R,onClick:P,onMouseEnter:k,onTouchStart:E,legacyBehavior:D=!1,onNavigate:O,ref:T,unstable_dynamicOnHover:N,...I}=e;r=b,D&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let S=a.default.useContext(l.AppRouterContext),L=!1!==C,A=null===C||"auto"===C?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:F,as:K}=a.default.useMemo(()=>{let e=m(w);return{href:e,as:x?m(x):e}},[w,x]);D&&(t=a.default.Children.only(r));let U=D?t&&"object"==typeof t&&t.ref:T,G=a.default.useCallback(e=>(null!==S&&(y.current=(0,f.mountLinkInstance)(e,F,S,A,L,v)),()=>{y.current&&((0,f.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,f.unmountPrefetchableInstance)(e)}),[L,F,S,A,v]),B={ref:(0,c.useMergedRef)(G,U),onClick(e){D||"function"!=typeof P||P(e),D&&t.props&&"function"==typeof t.props.onClick&&t.props.onClick(e),S&&(e.defaultPrevented||function(e,r,t,n,o,u,l){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let r=e.currentTarget.getAttribute("target");return r&&"_self"!==r||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(r)){o&&(e.preventDefault(),location.replace(r));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,h.dispatchNavigateAction)(t||r,o?"replace":"push",null==u||u,n.current)})}}(e,F,K,y,j,R,O))},onMouseEnter(e){D||"function"!=typeof k||k(e),D&&t.props&&"function"==typeof t.props.onMouseEnter&&t.props.onMouseEnter(e),S&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){D||"function"!=typeof E||E(e),D&&t.props&&"function"==typeof t.props.onTouchStart&&t.props.onTouchStart(e),S&&L&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,s.isAbsoluteUrl)(K)?B.href=K:D&&!M&&("a"!==t.type||"href"in t.props)||(B.href=(0,d.addBasePath)(K)),n=D?a.default.cloneElement(t,B):(0,o.jsx)("a",{...I,...B,children:r}),(0,o.jsx)(g.Provider,{value:u,children:n})}t(3180);let g=(0,a.createContext)(f.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(g);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8698:(e,r,t)=>{t.d(r,{UC:()=>eH,q7:()=>eW,ZL:()=>eX,bL:()=>ez,wv:()=>eZ,l9:()=>eq});var n=t(2115),o=t(5185),a=t(6101),u=t(6081),l=t(5845),i=t(3655),c=t(7328),s=t(4315),d=t(9178),f=t(2293),p=t(7900),h=t(1285),m=t(5152),v=t(4378),g=t(8905),y=t(9196),w=t(9708),x=t(9033),b=t(8168),C=t(3795),M=t(5155),j=["Enter"," "],_=["ArrowUp","PageDown","End"],R=["ArrowDown","PageUp","Home",..._],P={ltr:[...j,"ArrowRight"],rtl:[...j,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},E="Menu",[D,O,T]=(0,c.N)(E),[N,I]=(0,u.A)(E,[T,m.Bk,y.RG]),S=(0,m.Bk)(),L=(0,y.RG)(),[A,F]=N(E),[K,U]=N(E),G=e=>{let{__scopeMenu:r,open:t=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=S(r),[c,d]=n.useState(null),f=n.useRef(!1),p=(0,x.c)(u),h=(0,s.jH)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,M.jsx)(m.bL,{...i,children:(0,M.jsx)(A,{scope:r,open:t,onOpenChange:p,content:c,onContentChange:d,children:(0,M.jsx)(K,{scope:r,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};G.displayName=E;var B=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=S(t);return(0,M.jsx)(m.Mz,{...o,...n,ref:r})});B.displayName="MenuAnchor";var V="MenuPortal",[z,q]=N(V,{forceMount:void 0}),X=e=>{let{__scopeMenu:r,forceMount:t,children:n,container:o}=e,a=F(V,r);return(0,M.jsx)(z,{scope:r,forceMount:t,children:(0,M.jsx)(g.C,{present:t||a.open,children:(0,M.jsx)(v.Z,{asChild:!0,container:o,children:n})})})};X.displayName=V;var H="MenuContent",[W,Z]=N(H),Q=n.forwardRef((e,r)=>{let t=q(H,e.__scopeMenu),{forceMount:n=t.forceMount,...o}=e,a=F(H,e.__scopeMenu),u=U(H,e.__scopeMenu);return(0,M.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:n||a.open,children:(0,M.jsx)(D.Slot,{scope:e.__scopeMenu,children:u.modal?(0,M.jsx)(Y,{...o,ref:r}):(0,M.jsx)(J,{...o,ref:r})})})})}),Y=n.forwardRef((e,r)=>{let t=F(H,e.__scopeMenu),u=n.useRef(null),l=(0,a.s)(r,u);return n.useEffect(()=>{let e=u.current;if(e)return(0,b.Eq)(e)},[]),(0,M.jsx)(ee,{...e,ref:l,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),J=n.forwardRef((e,r)=>{let t=F(H,e.__scopeMenu);return(0,M.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),$=(0,w.TL)("MenuContent.ScrollLock"),ee=n.forwardRef((e,r)=>{let{__scopeMenu:t,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:b,disableOutsideScroll:j,...P}=e,k=F(H,t),E=U(H,t),D=S(t),T=L(t),N=O(t),[I,A]=n.useState(null),K=n.useRef(null),G=(0,a.s)(r,K,k.onContentChange),B=n.useRef(0),V=n.useRef(""),z=n.useRef(0),q=n.useRef(null),X=n.useRef("right"),Z=n.useRef(0),Q=j?C.A:n.Fragment;n.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,f.Oh)();let Y=n.useCallback(e=>{var r,t;return X.current===(null==(r=q.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let u=r[e],l=r[a],i=u.x,c=u.y,s=l.x,d=l.y;c>n!=d>n&&t<(s-i)*(n-c)/(d-c)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(t=q.current)?void 0:t.area)},[]);return(0,M.jsx)(W,{scope:t,searchRef:V,onItemEnter:n.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),onItemLeave:n.useCallback(e=>{var r;Y(e)||(null==(r=K.current)||r.focus(),A(null))},[Y]),onTriggerLeave:n.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),pointerGraceTimerRef:z,onPointerGraceIntentChange:n.useCallback(e=>{q.current=e},[]),children:(0,M.jsx)(Q,{...j?{as:$,allowPinchZoom:!0}:void 0,children:(0,M.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=K.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,M.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:b,children:(0,M.jsx)(y.bL,{asChild:!0,...T,dir:E.dir,orientation:"vertical",loop:u,currentTabStopId:I,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(h,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eR(k.open),"data-radix-menu-content":"",dir:E.dir,...D,...P,ref:G,style:{outline:"none",...P.style},onKeyDown:(0,o.m)(P.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&(e=>{var r,t;let n=V.current+e,o=N().filter(e=>!e.disabled),a=document.activeElement,u=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,l=function(e,r,t){var n;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=t?e.indexOf(t):-1,u=(n=Math.max(a,0),e.map((r,t)=>e[(n+t)%e.length]));1===o.length&&(u=u.filter(e=>e!==t));let l=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==t?l:void 0}(o.map(e=>e.textValue),n,u),i=null==(t=o.find(e=>e.textValue===l))?void 0:t.ref.current;!function e(r){V.current=r,window.clearTimeout(B.current),""!==r&&(B.current=window.setTimeout(()=>e(""),1e3))}(n),i&&setTimeout(()=>i.focus())})(e.key));let o=K.current;if(e.target!==o||!R.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);_.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{let r=e.target,t=Z.current!==e.clientX;e.currentTarget.contains(r)&&t&&(X.current=e.clientX>Z.current?"right":"left",Z.current=e.clientX)}))})})})})})})});Q.displayName=H;var er=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{role:"group",...n,ref:r})});er.displayName="MenuGroup";var et=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{...n,ref:r})});et.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:u,...l}=e,c=n.useRef(null),s=U(en,e.__scopeMenu),d=Z(en,e.__scopeMenu),f=(0,a.s)(r,c),p=n.useRef(!1);return(0,M.jsx)(eu,{...l,ref:f,disabled:t,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!t&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==u?void 0:u(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:r=>{var t;null==(t=e.onPointerDown)||t.call(e,r),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;p.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==d.searchRef.current;t||r&&" "===e.key||j.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var eu=n.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:u=!1,textValue:l,...c}=e,s=Z(en,t),d=L(t),f=n.useRef(null),p=(0,a.s)(r,f),[h,m]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=f.current;if(e){var r;g((null!=(r=e.textContent)?r:"").trim())}},[c.children]),(0,M.jsx)(D.ItemSlot,{scope:t,disabled:u,textValue:null!=l?l:v,children:(0,M.jsx)(y.q7,{asChild:!0,...d,focusable:!u,children:(0,M.jsx)(i.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eE(e=>{u?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),el=n.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:n,...a}=e;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:t,children:(0,M.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eP(t)?"mixed":t,...a,ref:r,"data-state":ek(t),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!eP(t)||!t),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[ec,es]=N(ei,{value:void 0,onValueChange:()=>{}}),ed=n.forwardRef((e,r)=>{let{value:t,onValueChange:n,...o}=e,a=(0,x.c)(n);return(0,M.jsx)(ec,{scope:e.__scopeMenu,value:t,onValueChange:a,children:(0,M.jsx)(er,{...o,ref:r})})});ed.displayName=ei;var ef="MenuRadioItem",ep=n.forwardRef((e,r)=>{let{value:t,...n}=e,a=es(ef,e.__scopeMenu),u=t===a.value;return(0,M.jsx)(em,{scope:e.__scopeMenu,checked:u,children:(0,M.jsx)(ea,{role:"menuitemradio","aria-checked":u,...n,ref:r,"data-state":ek(u),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,t)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var eh="MenuItemIndicator",[em,ev]=N(eh,{checked:!1}),eg=n.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:n,...o}=e,a=ev(eh,t);return(0,M.jsx)(g.C,{present:n||eP(a.checked)||!0===a.checked,children:(0,M.jsx)(i.sG.span,{...o,ref:r,"data-state":ek(a.checked)})})});eg.displayName=eh;var ey=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,M.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:r})});ey.displayName="MenuSeparator";var ew=n.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,o=S(t);return(0,M.jsx)(m.i3,{...o,...n,ref:r})});ew.displayName="MenuArrow";var[ex,eb]=N("MenuSub"),eC="MenuSubTrigger",eM=n.forwardRef((e,r)=>{let t=F(eC,e.__scopeMenu),u=U(eC,e.__scopeMenu),l=eb(eC,e.__scopeMenu),i=Z(eC,e.__scopeMenu),c=n.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,M.jsx)(B,{asChild:!0,...f,children:(0,M.jsx)(eu,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":l.contentId,"data-state":eR(t.open),...e,ref:(0,a.t)(r,l.onTriggerChange),onClick:r=>{var n;null==(n=e.onClick)||n.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eE(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||t.open||c.current||(i.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{t.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eE(e=>{var r,n;p();let o=null==(r=t.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(n=t.content)?void 0:n.dataset.side,a="right"===r,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let n=""!==i.searchRef.current;if(!e.disabled&&(!n||" "!==r.key)&&P[u.dir].includes(r.key)){var o;t.onOpenChange(!0),null==(o=t.content)||o.focus(),r.preventDefault()}})})})});eM.displayName=eC;var ej="MenuSubContent",e_=n.forwardRef((e,r)=>{let t=q(H,e.__scopeMenu),{forceMount:u=t.forceMount,...l}=e,i=F(H,e.__scopeMenu),c=U(H,e.__scopeMenu),s=eb(ej,e.__scopeMenu),d=n.useRef(null),f=(0,a.s)(r,d);return(0,M.jsx)(D.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:u||i.open,children:(0,M.jsx)(D.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;c.isUsingKeyboardRef.current&&(null==(r=d.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=k[c.dir].includes(e.key);if(r&&t){var n;i.onOpenChange(!1),null==(n=s.trigger)||n.focus(),e.preventDefault()}})})})})})});function eR(e){return e?"open":"closed"}function eP(e){return"indeterminate"===e}function ek(e){return eP(e)?"indeterminate":e?"checked":"unchecked"}function eE(e){return r=>"mouse"===r.pointerType?e(r):void 0}e_.displayName=ej;var eD="DropdownMenu",[eO,eT]=(0,u.A)(eD,[I]),eN=I(),[eI,eS]=eO(eD),eL=e=>{let{__scopeDropdownMenu:r,children:t,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:c=!0}=e,s=eN(r),d=n.useRef(null),[f,p]=(0,l.i)({prop:a,defaultProp:null!=u&&u,onChange:i,caller:eD});return(0,M.jsx)(eI,{scope:r,triggerId:(0,h.B)(),triggerRef:d,contentId:(0,h.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,M.jsx)(G,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:t})})};eL.displayName=eD;var eA="DropdownMenuTrigger",eF=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:n=!1,...u}=e,l=eS(eA,t),c=eN(t);return(0,M.jsx)(B,{asChild:!0,...c,children:(0,M.jsx)(i.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...u,ref:(0,a.t)(r,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eA;var eK=e=>{let{__scopeDropdownMenu:r,...t}=e,n=eN(r);return(0,M.jsx)(X,{...n,...t})};eK.displayName="DropdownMenuPortal";var eU="DropdownMenuContent",eG=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...a}=e,u=eS(eU,t),l=eN(t),i=n.useRef(!1);return(0,M.jsx)(Q,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=u.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!u.modal||n)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=eU,n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(er,{...o,...n,ref:r})}).displayName="DropdownMenuGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(et,{...o,...n,ref:r})}).displayName="DropdownMenuLabel";var eB=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ea,{...o,...n,ref:r})});eB.displayName="DropdownMenuItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(el,{...o,...n,ref:r})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ed,{...o,...n,ref:r})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ep,{...o,...n,ref:r})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(eg,{...o,...n,ref:r})}).displayName="DropdownMenuItemIndicator";var eV=n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ey,{...o,...n,ref:r})});eV.displayName="DropdownMenuSeparator",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(ew,{...o,...n,ref:r})}).displayName="DropdownMenuArrow",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(eM,{...o,...n,ref:r})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,o=eN(t);return(0,M.jsx)(e_,{...o,...n,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var ez=eL,eq=eF,eX=eK,eH=eG,eW=eB,eZ=eV},8859:(e,r)=>{function t(e){let r={};for(let[t,n]of e.entries()){let e=r[t];void 0===e?r[t]=n:Array.isArray(e)?e.push(n):r[t]=[e,n]}return r}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let r=new URLSearchParams;for(let[t,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)r.append(t,n(e));else r.set(t,n(o));return r}function a(e){for(var r=arguments.length,t=Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,n]of r.entries())e.append(t,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{assign:function(){return a},searchParamsToUrlQuery:function(){return t},urlQueryToSearchParams:function(){return o}})},9991:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return t},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return u},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return w}});let t=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let r,t=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return t||(t=!0,r=e(...o)),r}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function u(){let{protocol:e,hostname:r,port:t}=window.location;return e+"//"+r+(t?":"+t:"")}function l(){let{href:e}=window.location,r=u();return e.substring(r.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}async function d(e,r){let t=r.res||r.ctx&&r.ctx.res;if(!e.getInitialProps)return r.ctx&&r.Component?{pageProps:await d(r.Component,r.ctx)}:{};let n=await e.getInitialProps(r);if(t&&c(t))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,r){super(),this.message="Failed to load static file for page: "+e+" "+r}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function w(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);