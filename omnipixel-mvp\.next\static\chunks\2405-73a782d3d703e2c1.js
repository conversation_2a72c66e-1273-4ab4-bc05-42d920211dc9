(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2405],{342:(e,t,i)=>{var r=i(9200);t.operation=function(e){return new r(t.timeouts(e),{forever:e&&(e.forever||e.retries===1/0),unref:e&&e.unref,maxRetryTime:e&&e.maxRetryTime})},t.timeouts=function(e){if(e instanceof Array)return[].concat(e);var t={retries:10,factor:2,minTimeout:1e3,maxTimeout:1/0,randomize:!1};for(var i in e)t[i]=e[i];if(t.minTimeout>t.maxTimeout)throw Error("minTimeout is greater than maxTimeout");for(var r=[],s=0;s<t.retries;s++)r.push(this.createTimeout(s,t));return e&&e.forever&&!r.length&&r.push(this.createTimeout(s,t)),r.sort(function(e,t){return e-t}),r},t.createTimeout=function(e,t){var i=Math.round((t.randomize?Math.random()+1:1)*Math.max(t.minTimeout,1)*Math.pow(t.factor,e));return Math.min(i,t.maxTimeout)},t.wrap=function(e,i,r){if(i instanceof Array&&(r=i,i=null),!r)for(var s in r=[],e)"function"==typeof e[s]&&r.push(s);for(var o=0;o<r.length;o++){var a=r[o],n=e[a];e[a]=(function(r){var s=t.operation(i),o=Array.prototype.slice.call(arguments,1),a=o.pop();o.push(function(e){s.retry(e)||(e&&(arguments[0]=s.mainError()),a.apply(this,arguments))}),s.attempt(function(){r.apply(e,o)})}).bind(e,n),e[a].options=i}}},570:(e,t,i)=>{var r=i(4376),s=Object.prototype,o=s.hasOwnProperty,a=s.toString,n=r?r.toStringTag:void 0;e.exports=function(e){var t=o.call(e,n),i=e[n];try{e[n]=void 0;var r=!0}catch(e){}var s=a.call(e);return r&&(t?e[n]=i:delete e[n]),s}},646:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},771:(e,t,i)=>{var r=i(8233),s=i(8611);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==r(e)}},1539:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1788:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1959:(e,t,i)=>{var r=i(5964),s=i(7460);e.exports=function(e,t,i){var o=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return s(i)&&(o="leading"in i?!!i.leading:o,a="trailing"in i?!!i.trailing:a),r(e,t,{leading:o,maxWait:t,trailing:a})}},2178:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},2354:e=>{"use strict";function t(e,t){this.text=e=e||"",this.hasWild=~e.indexOf("*"),this.separator=t,this.parts=e.split(t)}t.prototype.match=function(e){var t,i,r=!0,s=this.parts,o=s.length;if("string"==typeof e||e instanceof String)if(this.hasWild||this.text==e){for(t=0,i=(e||"").split(this.separator);r&&t<o;t++)if("*"===s[t])continue;else r=t<i.length&&s[t]===i[t];r=r&&i}else r=!1;else if("function"==typeof e.splice)for(r=[],t=e.length;t--;)this.match(e[t])&&(r[r.length]=e[t]);else if("object"==typeof e)for(var a in r={},e)this.match(a)&&(r[a]=e[a]);return r},e.exports=function(e,i,r){var s=new t(e,r||/[\/\.]/);return void 0!==i?s.match(i):s}},2500:(e,t,i)=>{var r=i(7985),s="object"==typeof self&&self&&self.Object===Object&&self;e.exports=r||s||Function("return this")()},2525:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3893:(e,t,i)=>{"use strict";i.d(t,{A:()=>eP});var r=i(6586),s=i(5994),o=i(1959);function a(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var n=0;function l(e){return"__private_"+n+++"_"+e}var u=l("callbacks"),h=l("publish");class d{constructor(){Object.defineProperty(this,h,{value:p}),this.state={},Object.defineProperty(this,u,{writable:!0,value:new Set})}getState(){return this.state}setState(e){let t={...this.state},i={...this.state,...e};this.state=i,a(this,h)[h](t,i,e)}subscribe(e){return a(this,u)[u].add(e),()=>{a(this,u)[u].delete(e)}}}function p(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];a(this,u)[u].forEach(e=>{e(...t)})}function c(e){let t=e.lastIndexOf(".");return -1===t||t===e.length-1?{name:e,extension:void 0}:{name:e.slice(0,t),extension:e.slice(t+1)}}d.VERSION="4.2.0";let f={__proto__:null,md:"text/markdown",markdown:"text/markdown",mp4:"video/mp4",mp3:"audio/mp3",svg:"image/svg+xml",jpg:"image/jpeg",png:"image/png",webp:"image/webp",gif:"image/gif",heic:"image/heic",heif:"image/heif",yaml:"text/yaml",yml:"text/yaml",csv:"text/csv",tsv:"text/tab-separated-values",tab:"text/tab-separated-values",avi:"video/x-msvideo",mks:"video/x-matroska",mkv:"video/x-matroska",mov:"video/quicktime",dicom:"application/dicom",doc:"application/msword",msg:"application/vnd.ms-outlook",docm:"application/vnd.ms-word.document.macroenabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroenabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",xla:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlc:"application/vnd.ms-excel",xlf:"application/x-xliff+xml",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroenabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xlw:"application/vnd.ms-excel",txt:"text/plain",text:"text/plain",conf:"text/plain",log:"text/plain",pdf:"application/pdf",zip:"application/zip","7z":"application/x-7z-compressed",rar:"application/x-rar-compressed",tar:"application/x-tar",gz:"application/gzip",dmg:"application/x-apple-diskimage"};function m(e){var t;if(e.type)return e.type;let i=e.name?null==(t=c(e.name).extension)?void 0:t.toLowerCase():null;return i&&i in f?f[i]:"application/octet-stream"}function g(e){let t="";return e.replace(/[^A-Z0-9]/gi,e=>(t+=`-${e.charCodeAt(0).toString(32)}`,"/"))+t}function y(e){return e<10?`0${e}`:e.toString()}function v(){let e=new Date,t=y(e.getHours()),i=y(e.getMinutes()),r=y(e.getSeconds());return`${t}:${i}:${r}`}let b={debug:()=>{},warn:()=>{},error:function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return console.error(`[Uppy] [${v()}]`,...t)}},w={debug:function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return console.debug(`[Uppy] [${v()}]`,...t)},warn:function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return console.warn(`[Uppy] [${v()}]`,...t)},error:function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return console.error(`[Uppy] [${v()}]`,...t)}};var P=i(4592),S=i(8792);let O={maxFileSize:null,minFileSize:null,maxTotalFileSize:null,maxNumberOfFiles:null,minNumberOfFiles:null,allowedFileTypes:null,requiredMetaFields:[]};class x extends Error{constructor(e,t){var i;super(e),this.isRestriction=!0,this.isUserFacing=null==(i=null==t?void 0:t.isUserFacing)||i,null!=t&&t.file&&(this.file=t.file)}}class E{constructor(e,t){this.getI18n=t,this.getOpts=()=>{var t;let i=e();if((null==(t=i.restrictions)?void 0:t.allowedFileTypes)!=null&&!Array.isArray(i.restrictions.allowedFileTypes))throw TypeError("`restrictions.allowedFileTypes` must be an array");return i}}validateAggregateRestrictions(e,t){let{maxTotalFileSize:i,maxNumberOfFiles:r}=this.getOpts().restrictions;if(r&&e.filter(e=>!e.isGhost).length+t.length>r)throw new x(`${this.getI18n()("youCanOnlyUploadX",{smart_count:r})}`);if(i){let r=[...e,...t].reduce((e,t)=>{var i;return e+(null!=(i=t.size)?i:0)},0);if(r>i)throw new x(this.getI18n()("aggregateExceedsSize",{sizeAllowed:P(i),size:P(r)}))}}validateSingleFile(e){let{maxFileSize:t,minFileSize:i,allowedFileTypes:r}=this.getOpts().restrictions;if(r&&!r.some(t=>t.includes("/")?!!e.type&&S(e.type.replace(/;.*?$/,""),t):"."===t[0]&&!!e.extension&&e.extension.toLowerCase()===t.slice(1).toLowerCase())){let t=r.join(", ");throw new x(this.getI18n()("youCanOnlyUploadFileTypes",{types:t}),{file:e})}if(t&&null!=e.size&&e.size>t){var s;throw new x(this.getI18n()("exceedsSize",{size:P(t),file:null!=(s=e.name)?s:this.getI18n()("unnamed")}),{file:e})}if(i&&null!=e.size&&e.size<i)throw new x(this.getI18n()("inferiorSize",{size:P(i)}),{file:e})}validate(e,t){t.forEach(e=>{this.validateSingleFile(e)}),this.validateAggregateRestrictions(e,t)}validateMinNumberOfFiles(e){let{minNumberOfFiles:t}=this.getOpts().restrictions;if(t&&Object.keys(e).length<t)throw new x(this.getI18n()("youHaveToAtLeastSelectX",{smart_count:t}))}getMissingRequiredMetaFields(e){var t;let i=new x(this.getI18n()("missingRequiredMetaFieldOnFile",{fileName:null!=(t=e.name)?t:this.getI18n()("unnamed")})),{requiredMetaFields:r}=this.getOpts().restrictions,s=[];for(let t of r)Object.hasOwn(e.meta,t)&&""!==e.meta[t]||s.push(t);return{missingFields:s,error:i}}}let j={strings:{addBulkFilesFailed:{0:"Failed to add %{smart_count} file due to an internal error",1:"Failed to add %{smart_count} files due to internal errors"},youCanOnlyUploadX:{0:"You can only upload %{smart_count} file",1:"You can only upload %{smart_count} files"},youHaveToAtLeastSelectX:{0:"You have to select at least %{smart_count} file",1:"You have to select at least %{smart_count} files"},aggregateExceedsSize:"You selected %{size} of files, but maximum allowed size is %{sizeAllowed}",exceedsSize:"%{file} exceeds maximum allowed size of %{size}",missingRequiredMetaField:"Missing required meta fields",missingRequiredMetaFieldOnFile:"Missing required meta fields in %{fileName}",inferiorSize:"This file is smaller than the allowed size of %{size}",youCanOnlyUploadFileTypes:"You can only upload: %{types}",noMoreFilesAllowed:"Cannot add more files",noDuplicates:"Cannot add the duplicate file '%{fileName}', it already exists",companionError:"Connection with Companion failed",authAborted:"Authentication aborted",companionUnauthorizeHint:"To unauthorize to your %{provider} account, please go to %{url}",failedToUpload:"Failed to upload %{file}",noInternetConnection:"No Internet connection",connectedToInternet:"Connected to the Internet",noFilesFound:"You have no files or folders here",noSearchResults:"Unfortunately, there are no results for this search",selectX:{0:"Select %{smart_count}",1:"Select %{smart_count}"},allFilesFromFolderNamed:"All files from folder %{name}",openFolderNamed:"Open folder %{name}",cancel:"Cancel",logOut:"Log out",logIn:"Log in",pickFiles:"Pick files",pickPhotos:"Pick photos",filter:"Filter",resetFilter:"Reset filter",loading:"Loading...",loadedXFiles:"Loaded %{numFiles} files",authenticateWithTitle:"Please authenticate with %{pluginName} to select files",authenticateWith:"Connect to %{pluginName}",signInWithGoogle:"Sign in with Google",searchImages:"Search for images",enterTextToSearch:"Enter text to search for images",search:"Search",resetSearch:"Reset search",emptyFolderAdded:"No files were added from empty folder",addedNumFiles:"Added %{numFiles} file(s)",folderAlreadyAdded:'The folder "%{folder}" was already added',folderAdded:{0:"Added %{smart_count} file from %{folder}",1:"Added %{smart_count} files from %{folder}"},additionalRestrictionsFailed:"%{count} additional restrictions were not fulfilled",unnamed:"Unnamed",pleaseWait:"Please wait"}};function k(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var F=0;function A(e){return"__private_"+F+++"_"+e}let U={totalProgress:0,allowNewUpload:!0,error:null,recoveredState:null};var T=A("plugins"),C=A("restricter"),R=A("storeUnsubscribe"),_=A("emitter"),M=A("preProcessors"),$=A("uploaders"),z=A("postProcessors"),I=A("informAndEmit"),N=A("checkRequiredMetaFieldsOnFile"),L=A("checkRequiredMetaFields"),q=A("assertNewUploadAllowed"),H=A("transformFile"),D=A("startIfAutoProceed"),B=A("checkAndUpdateFileState"),W=A("getFilesToRetry"),G=A("doRetryAll"),X=A("handleUploadProgress"),V=A("updateTotalProgress"),Y=A("updateTotalProgressThrottled"),K=A("calculateTotalProgress"),J=A("addListeners"),Q=A("updateOnlineStatus"),Z=A("requestClientById"),ee=A("createUpload"),et=A("getUpload"),ei=A("removeUpload"),er=A("runUpload");class es{constructor(e){Object.defineProperty(this,er,{value:ew}),Object.defineProperty(this,ei,{value:eb}),Object.defineProperty(this,et,{value:ev}),Object.defineProperty(this,ee,{value:ey}),Object.defineProperty(this,J,{value:eg}),Object.defineProperty(this,K,{value:em}),Object.defineProperty(this,V,{value:ef}),Object.defineProperty(this,G,{value:ec}),Object.defineProperty(this,W,{value:ep}),Object.defineProperty(this,B,{value:ed}),Object.defineProperty(this,D,{value:eh}),Object.defineProperty(this,H,{value:eu}),Object.defineProperty(this,q,{value:el}),Object.defineProperty(this,L,{value:en}),Object.defineProperty(this,N,{value:ea}),Object.defineProperty(this,I,{value:eo}),Object.defineProperty(this,T,{writable:!0,value:Object.create(null)}),Object.defineProperty(this,C,{writable:!0,value:void 0}),Object.defineProperty(this,R,{writable:!0,value:void 0}),Object.defineProperty(this,_,{writable:!0,value:s()}),Object.defineProperty(this,M,{writable:!0,value:new Set}),Object.defineProperty(this,$,{writable:!0,value:new Set}),Object.defineProperty(this,z,{writable:!0,value:new Set}),this.scheduledAutoProceed=null,this.wasOffline=!1,Object.defineProperty(this,X,{writable:!0,value:(e,t)=>{let i=e?this.getFile(e.id):void 0;if(null==e||!i)return void this.log(`Not setting progress for a file that has been removed: ${null==e?void 0:e.id}`);if(100===i.progress.percentage)return void this.log(`Not setting progress for a file that has been already uploaded: ${e.id}`);let r={bytesTotal:t.bytesTotal,percentage:null!=t.bytesTotal&&Number.isFinite(t.bytesTotal)&&t.bytesTotal>0?Math.round(t.bytesUploaded/t.bytesTotal*100):void 0};null!=i.progress.uploadStarted?this.setFileState(e.id,{progress:{...i.progress,...r,bytesUploaded:t.bytesUploaded}}):this.setFileState(e.id,{progress:{...i.progress,...r}}),k(this,Y)[Y]()}}),Object.defineProperty(this,Y,{writable:!0,value:o(()=>k(this,V)[V](),500,{leading:!0,trailing:!0})}),Object.defineProperty(this,Q,{writable:!0,value:this.updateOnlineStatus.bind(this)}),Object.defineProperty(this,Z,{writable:!0,value:new Map}),this.defaultLocale=j;let t={id:"uppy",autoProceed:!1,allowMultipleUploadBatches:!0,debug:!1,restrictions:O,meta:{},onBeforeFileAdded:(e,t)=>!Object.hasOwn(t,e.id),onBeforeUpload:e=>e,store:new d,logger:b,infoTimeout:5e3},i={...t,...e};this.opts={...i,restrictions:{...t.restrictions,...e&&e.restrictions}},e&&e.logger&&e.debug?this.log("You are using a custom `logger`, but also set `debug: true`, which uses built-in logger to output logs to console. Ignoring `debug: true` and using your custom `logger`.","warning"):e&&e.debug&&(this.opts.logger=w),this.log(`Using Core v${es.VERSION}`),this.i18nInit(),this.store=this.opts.store,this.setState({...U,plugins:{},files:{},currentUploads:{},capabilities:{uploadProgress:function(e){if(null==e&&"undefined"!=typeof navigator&&(e=navigator.userAgent),!e)return!0;let t=/Edge\/(\d+\.\d+)/.exec(e);if(!t)return!0;let i=t[1].split(".",2),r=parseInt(i[0],10),s=parseInt(i[1],10);return!!(r<15)||15===r&&!!(s<15063)||!!(r>18)||18===r&&!!(s>=18218)}(),individualCancellation:!0,resumableUploads:!1},meta:{...this.opts.meta},info:[]}),k(this,C)[C]=new E(()=>this.opts,()=>this.i18n),k(this,R)[R]=this.store.subscribe((e,t,i)=>{this.emit("state-update",e,t,i),this.updateAll(t)}),this.opts.debug&&"undefined"!=typeof window&&(window[this.opts.id]=this),k(this,J)[J]()}emit(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];k(this,_)[_].emit(e,...i)}on(e,t){return k(this,_)[_].on(e,t),this}once(e,t){return k(this,_)[_].once(e,t),this}off(e,t){return k(this,_)[_].off(e,t),this}updateAll(e){this.iteratePlugins(t=>{t.update(e)})}setState(e){this.store.setState(e)}getState(){return this.store.getState()}patchFilesState(e){let t=this.getState().files;this.setState({files:{...t,...Object.fromEntries(Object.entries(e).map(e=>{let[i,r]=e;return[i,{...t[i],...r}]}))}})}setFileState(e,t){if(!this.getState().files[e])throw Error(`Can’t set state for ${e} (the file could have been removed)`);this.patchFilesState({[e]:t})}i18nInit(){let e=new r.A([this.defaultLocale,this.opts.locale],{onMissingKey:e=>this.log(`Missing i18n string: ${e}`,"error")});this.i18n=e.translate.bind(e),this.i18nArray=e.translateArray.bind(e),this.locale=e.locale}setOptions(e){this.opts={...this.opts,...e,restrictions:{...this.opts.restrictions,...null==e?void 0:e.restrictions}},e.meta&&this.setMeta(e.meta),this.i18nInit(),e.locale&&this.iteratePlugins(t=>{t.setOptions(e)}),this.setState(void 0)}resetProgress(){let e={percentage:0,bytesUploaded:!1,uploadComplete:!1,uploadStarted:null},t={...this.getState().files},i=Object.create(null);Object.keys(t).forEach(r=>{i[r]={...t[r],progress:{...t[r].progress,...e},tus:void 0,transloadit:void 0}}),this.setState({files:i,...U})}clear(){let{capabilities:e,currentUploads:t}=this.getState();if(Object.keys(t).length>0&&!e.individualCancellation)throw Error("The installed uploader plugin does not allow removing files during an upload.");this.setState({...U,files:{}})}addPreProcessor(e){k(this,M)[M].add(e)}removePreProcessor(e){return k(this,M)[M].delete(e)}addPostProcessor(e){k(this,z)[z].add(e)}removePostProcessor(e){return k(this,z)[z].delete(e)}addUploader(e){k(this,$)[$].add(e)}removeUploader(e){return k(this,$)[$].delete(e)}setMeta(e){let t={...this.getState().meta,...e},i={...this.getState().files};Object.keys(i).forEach(t=>{i[t]={...i[t],meta:{...i[t].meta,...e}}}),this.log("Adding metadata:"),this.log(e),this.setState({meta:t,files:i})}setFileMeta(e,t){let i={...this.getState().files};if(!i[e])return void this.log(`Was trying to set metadata for a file that has been removed: ${e}`);let r={...i[e].meta,...t};i[e]={...i[e],meta:r},this.setState({files:i})}getFile(e){return this.getState().files[e]}getFiles(){let{files:e}=this.getState();return Object.values(e)}getFilesByIds(e){return e.map(e=>this.getFile(e))}getObjectOfFilesPerState(){let{files:e,totalProgress:t,error:i}=this.getState(),r=Object.values(e),s=[],o=[],a=[],n=[],l=[],u=[],h=[],d=[],p=[];for(let e of r){let{progress:t}=e;!t.uploadComplete&&t.uploadStarted&&(s.push(e),e.isPaused||d.push(e)),t.uploadStarted||o.push(e),(t.uploadStarted||t.preprocess||t.postprocess)&&a.push(e),t.uploadStarted&&n.push(e),e.isPaused&&l.push(e),t.uploadComplete&&u.push(e),e.error&&h.push(e),(t.preprocess||t.postprocess)&&p.push(e)}return{newFiles:o,startedFiles:a,uploadStartedFiles:n,pausedFiles:l,completeFiles:u,erroredFiles:h,inProgressFiles:s,inProgressNotPausedFiles:d,processingFiles:p,isUploadStarted:n.length>0,isAllComplete:100===t&&u.length===r.length&&0===p.length,isAllErrored:!!i&&h.length===r.length,isAllPaused:0!==s.length&&l.length===s.length,isUploadInProgress:s.length>0,isSomeGhost:r.some(e=>e.isGhost)}}validateRestrictions(e,t){void 0===t&&(t=this.getFiles());try{k(this,C)[C].validate(t,[e])}catch(e){return e}return null}validateSingleFile(e){try{k(this,C)[C].validateSingleFile(e)}catch(e){return e.message}return null}validateAggregateRestrictions(e){let t=this.getFiles();try{k(this,C)[C].validateAggregateRestrictions(t,e)}catch(e){return e.message}return null}checkIfFileAlreadyExists(e){let{files:t}=this.getState();return!!t[e]&&!t[e].isGhost}addFile(e){k(this,q)[q](e);let{nextFilesState:t,validFilesToAdd:i,errors:r}=k(this,B)[B]([e]),s=r.filter(e=>e.isRestriction);if(k(this,I)[I](s),r.length>0)throw r[0];this.setState({files:t});let[o]=i;return this.emit("file-added",o),this.emit("files-added",i),this.log(`Added file: ${o.name}, ${o.id}, mime type: ${o.type}`),k(this,D)[D](),o.id}addFiles(e){k(this,q)[q]();let{nextFilesState:t,validFilesToAdd:i,errors:r}=k(this,B)[B](e),s=r.filter(e=>e.isRestriction);k(this,I)[I](s);let o=r.filter(e=>!e.isRestriction);if(o.length>0){let e="Multiple errors occurred while adding files:\n";if(o.forEach(t=>{e+=`
 * ${t.message}`}),this.info({message:this.i18n("addBulkFilesFailed",{smart_count:o.length}),details:e},"error",this.opts.infoTimeout),"function"==typeof AggregateError)throw AggregateError(o,e);{let t=Error(e);throw t.errors=o,t}}this.setState({files:t}),i.forEach(e=>{this.emit("file-added",e)}),this.emit("files-added",i),i.length>5?this.log(`Added batch of ${i.length} files`):Object.values(i).forEach(e=>{this.log(`Added file: ${e.name}
 id: ${e.id}
 type: ${e.type}`)}),i.length>0&&k(this,D)[D]()}removeFiles(e){let{files:t,currentUploads:i}=this.getState(),r={...t},s={...i},o=Object.create(null);function a(e){return void 0===o[e]}e.forEach(e=>{t[e]&&(o[e]=t[e],delete r[e])}),Object.keys(s).forEach(e=>{let t=i[e].fileIDs.filter(a);if(0===t.length)return void delete s[e];let{capabilities:r}=this.getState();if(t.length!==i[e].fileIDs.length&&!r.individualCancellation)throw Error("The installed uploader plugin does not allow removing files during an upload.");s[e]={...i[e],fileIDs:t}});let n={currentUploads:s,files:r};0===Object.keys(r).length&&(n.allowNewUpload=!0,n.error=null,n.recoveredState=null),this.setState(n),k(this,Y)[Y]();let l=Object.keys(o);l.forEach(e=>{this.emit("file-removed",o[e])}),l.length>5?this.log(`Removed ${l.length} files`):this.log(`Removed files: ${l.join(", ")}`)}removeFile(e){this.removeFiles([e])}pauseResume(e){if(!this.getState().capabilities.resumableUploads||this.getFile(e).progress.uploadComplete)return;let t=this.getFile(e),i=!t.isPaused;return this.setFileState(e,{isPaused:i}),this.emit("upload-pause",t,i),i}pauseAll(){let e={...this.getState().files};Object.keys(e).filter(t=>!e[t].progress.uploadComplete&&e[t].progress.uploadStarted).forEach(t=>{let i={...e[t],isPaused:!0};e[t]=i}),this.setState({files:e}),this.emit("pause-all")}resumeAll(){let e={...this.getState().files};Object.keys(e).filter(t=>!e[t].progress.uploadComplete&&e[t].progress.uploadStarted).forEach(t=>{let i={...e[t],isPaused:!1,error:null};e[t]=i}),this.setState({files:e}),this.emit("resume-all")}async retryAll(){let e=await k(this,G)[G]();return this.emit("complete",e),e}cancelAll(){this.emit("cancel-all");let{files:e}=this.getState(),t=Object.keys(e);t.length&&this.removeFiles(t),this.setState(U)}retryUpload(e){this.setFileState(e,{error:null,isPaused:!1}),this.emit("upload-retry",this.getFile(e));let t=k(this,ee)[ee]([e],{forceAllowNewUpload:!0});return k(this,er)[er](t)}logout(){this.iteratePlugins(e=>{var t;null==(t=e.provider)||null==t.logout||t.logout()})}[Symbol.for("uppy test: updateTotalProgress")](){return k(this,V)[V]()}updateOnlineStatus(){var e;null==(e=window.navigator.onLine)||e?(this.emit("is-online"),this.wasOffline&&(this.emit("back-online"),this.info(this.i18n("connectedToInternet"),"success",3e3),this.wasOffline=!1)):(this.emit("is-offline"),this.info(this.i18n("noInternetConnection"),"error",0),this.wasOffline=!0)}getID(){return this.opts.id}use(e){if("function"!=typeof e)throw TypeError(`Expected a plugin class, but got ${null===e?"null":typeof e}. Please verify that the plugin was imported and spelled correctly.`);for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];let s=new e(this,...i),o=s.id;if(!o)throw Error("Your plugin must have an id");if(!s.type)throw Error("Your plugin must have a type");let a=this.getPlugin(o);if(a)throw Error(`Already found a plugin named '${a.id}'. Tried to use: '${o}'.
Uppy plugins must have unique \`id\` options.`);return e.VERSION&&this.log(`Using ${o} v${e.VERSION}`),s.type in k(this,T)[T]?k(this,T)[T][s.type].push(s):k(this,T)[T][s.type]=[s],s.install(),this.emit("plugin-added",s),this}getPlugin(e){for(let t of Object.values(k(this,T)[T])){let i=t.find(t=>t.id===e);if(null!=i)return i}}[Symbol.for("uppy test: getPlugins")](e){return k(this,T)[T][e]}iteratePlugins(e){Object.values(k(this,T)[T]).flat(1).forEach(e)}removePlugin(e){this.log(`Removing plugin ${e.id}`),this.emit("plugin-remove",e),e.uninstall&&e.uninstall();let t=k(this,T)[T][e.type],i=t.findIndex(t=>t.id===e.id);-1!==i&&t.splice(i,1);let r={plugins:{...this.getState().plugins,[e.id]:void 0}};this.setState(r)}destroy(){this.log(`Closing Uppy instance ${this.opts.id}: removing all files and uninstalling plugins`),this.cancelAll(),k(this,R)[R](),this.iteratePlugins(e=>{this.removePlugin(e)}),"undefined"!=typeof window&&window.removeEventListener&&(window.removeEventListener("online",k(this,Q)[Q]),window.removeEventListener("offline",k(this,Q)[Q]))}hideInfo(){let{info:e}=this.getState();this.setState({info:e.slice(1)}),this.emit("info-hidden")}info(e,t,i){void 0===t&&(t="info"),void 0===i&&(i=3e3);let r="object"==typeof e;this.setState({info:[...this.getState().info,{type:t,message:r?e.message:e,details:r?e.details:null}]}),setTimeout(()=>this.hideInfo(),i),this.emit("info-visible")}log(e,t){let{logger:i}=this.opts;switch(t){case"error":i.error(e);break;case"warning":i.warn(e);break;default:i.debug(e)}}registerRequestClient(e,t){k(this,Z)[Z].set(e,t)}getRequestClientForFile(e){if(!e.remote)throw Error(`Tried to get RequestClient for a non-remote file ${e.id}`);let t=k(this,Z)[Z].get(e.remote.requestClientId);if(null==t)throw Error(`requestClientId "${e.remote.requestClientId}" not registered for file "${e.id}"`);return t}restore(e){return(this.log(`Core: attempting to restore upload "${e}"`),this.getState().currentUploads[e])?k(this,er)[er](e):(k(this,ei)[ei](e),Promise.reject(Error("Nonexistent upload")))}[Symbol.for("uppy test: createUpload")](){return k(this,ee)[ee](...arguments)}addResultData(e,t){if(!k(this,et)[et](e))return void this.log(`Not setting result for an upload that has been removed: ${e}`);let{currentUploads:i}=this.getState(),r={...i[e],result:{...i[e].result,...t}};this.setState({currentUploads:{...i,[e]:r}})}async upload(){var e;null!=(e=k(this,T)[T].uploader)&&e.length||this.log("No uploader type plugins are used","warning");let{files:t}=this.getState();if(k(this,W)[W]().length>0){let e=await k(this,G)[G]();if(!(this.getFiles().filter(e=>null==e.progress.uploadStarted).length>0))return this.emit("complete",e),e;({files:t}=this.getState())}let i=this.opts.onBeforeUpload(t);return!1===i?Promise.reject(Error("Not starting the upload because onBeforeUpload returned false")):(i&&"object"==typeof i&&(t=i,this.setState({files:t})),Promise.resolve().then(()=>k(this,C)[C].validateMinNumberOfFiles(t)).catch(e=>{throw k(this,I)[I]([e]),e}).then(()=>{if(!k(this,L)[L](t))throw new x(this.i18n("missingRequiredMetaField"))}).catch(e=>{throw e}).then(async()=>{let{currentUploads:e}=this.getState(),i=Object.values(e).flatMap(e=>e.fileIDs),r=[];Object.keys(t).forEach(e=>{let t=this.getFile(e);t.progress.uploadStarted||-1!==i.indexOf(e)||r.push(t.id)});let s=k(this,ee)[ee](r),o=await k(this,er)[er](s);return this.emit("complete",o),o}).catch(e=>{throw this.emit("error",e),this.log(e,"error"),e}))}}function eo(e){for(let t of e)t.isRestriction?this.emit("restriction-failed",t.file,t):this.emit("error",t,t.file),this.log(t,"warning");let t=e.filter(e=>e.isUserFacing),i=t.slice(0,4),r=t.slice(4);i.forEach(e=>{let{message:t,details:i=""}=e;this.info({message:t,details:i},"error",this.opts.infoTimeout)}),r.length>0&&this.info({message:this.i18n("additionalRestrictionsFailed",{count:r.length})})}function ea(e){let{missingFields:t,error:i}=k(this,C)[C].getMissingRequiredMetaFields(e);return t.length>0?(this.setFileState(e.id,{missingRequiredMetaFields:t}),this.log(i.message),this.emit("restriction-failed",e,i),!1):(0===t.length&&e.missingRequiredMetaFields&&this.setFileState(e.id,{missingRequiredMetaFields:[]}),!0)}function en(e){let t=!0;for(let i of Object.values(e))k(this,N)[N](i)||(t=!1);return t}function el(e){let{allowNewUpload:t}=this.getState();if(!1===t){let t=new x(this.i18n("noMoreFilesAllowed"),{file:e});throw k(this,I)[I]([t]),t}}function eu(e){let t=e instanceof File?{name:e.name,type:e.type,size:e.size,data:e}:e,i=m(t),r=t.name?t.name:"image"===i.split("/")[0]?`${i.split("/")[0]}.${i.split("/")[1]}`:"noname",s=c(r).extension,o=function(e,t){var i;let r;if(e.isRemote&&e.remote&&new Set(["box","dropbox","drive","facebook","unsplash"]).has(e.remote.provider))return e.id;let s=m(e);return i={...e,type:s},r=t||"uppy","string"==typeof i.name&&(r+=`-${g(i.name.toLowerCase())}`),void 0!==i.type&&(r+=`-${i.type}`),i.meta&&"string"==typeof i.meta.relativePath&&(r+=`-${g(i.meta.relativePath.toLowerCase())}`),void 0!==i.data.size&&(r+=`-${i.data.size}`),void 0!==i.data.lastModified&&(r+=`-${i.data.lastModified}`),r}(t,this.getID()),a=t.meta||{};a.name=r,a.type=i;let n=Number.isFinite(t.data.size)?t.data.size:null;return{source:t.source||"",id:o,name:r,extension:s||"",meta:{...this.getState().meta,...a},type:i,data:t.data,progress:{percentage:0,bytesUploaded:!1,bytesTotal:n,uploadComplete:!1,uploadStarted:null},size:n,isGhost:!1,isRemote:t.isRemote||!1,remote:t.remote,preview:t.preview}}function eh(){this.opts.autoProceed&&!this.scheduledAutoProceed&&(this.scheduledAutoProceed=setTimeout(()=>{this.scheduledAutoProceed=null,this.upload().catch(e=>{e.isRestriction||this.log(e.stack||e.message||e)})},4))}function ed(e){let{files:t}=this.getState(),i={...t},r=[],s=[];for(let n of e)try{var o,a;let e=k(this,H)[H](n),s=null==(o=t[e.id])?void 0:o.isGhost;s&&(e={...t[e.id],isGhost:!1,data:n.data},this.log(`Replaced the blob in the restored ghost file: ${e.name}, ${e.id}`));let l=this.opts.onBeforeFileAdded(e,i);if(!l&&this.checkIfFileAlreadyExists(e.id))throw new x(this.i18n("noDuplicates",{fileName:null!=(a=e.name)?a:this.i18n("unnamed")}),{file:n});if(!1!==l||s)"object"==typeof l&&null!==l&&(e=l);else throw new x("Cannot add the file because onBeforeFileAdded returned false.",{isUserFacing:!1,file:n});k(this,C)[C].validateSingleFile(e),i[e.id]=e,r.push(e)}catch(e){s.push(e)}try{k(this,C)[C].validateAggregateRestrictions(Object.values(t),r)}catch(e){return s.push(e),{nextFilesState:t,validFilesToAdd:[],errors:s}}return{nextFilesState:i,validFilesToAdd:r,errors:s}}function ep(){let{files:e}=this.getState();return Object.keys(e).filter(t=>e[t].error)}async function ec(){let e=k(this,W)[W](),t={...this.getState().files};if(e.forEach(e=>{t[e]={...t[e],isPaused:!1,error:null}}),this.setState({files:t,error:null}),this.emit("retry-all",this.getFilesByIds(e)),0===e.length)return{successful:[],failed:[]};let i=k(this,ee)[ee](e,{forceAllowNewUpload:!0});return k(this,er)[er](i)}function ef(){var e,t;let i=k(this,K)[K](),r=null;null!=i&&((r=Math.round(100*i))>100?r=100:r<0&&(r=0)),this.emit("progress",null!=(e=r)?e:0),this.setState({totalProgress:null!=(t=r)?t:0})}function em(){let e=this.getFiles().filter(e=>e.progress.uploadStarted||e.progress.preprocess||e.progress.postprocess);if(0===e.length)return 0;if(e.every(e=>e.progress.uploadComplete))return 1;let t=e=>null!=e.progress.bytesTotal&&0!==e.progress.bytesTotal,i=e.filter(t),r=e.filter(e=>!t(e));if(i.every(e=>e.progress.uploadComplete)&&r.length>0&&!r.every(e=>e.progress.uploadComplete))return null;let s=i.reduce((e,t)=>{var i;return e+(null!=(i=t.progress.bytesTotal)?i:0)},0),o=i.reduce((e,t)=>e+(t.progress.bytesUploaded||0),0);return 0===s?0:o/s}function eg(){let e=(e,t,i)=>{let r=e.message||"Unknown error";e.details&&(r+=` ${e.details}`),this.setState({error:r}),null!=t&&t.id in this.getState().files&&this.setFileState(t.id,{error:r,response:i})};this.on("error",e),this.on("upload-error",(t,i,r)=>{if(e(i,t,r),"object"==typeof i&&i.message){var s;this.log(i.message,"error");let e=Error(this.i18n("failedToUpload",{file:null!=(s=null==t?void 0:t.name)?s:""}));e.isUserFacing=!0,e.details=i.message,i.details&&(e.details+=` ${i.details}`),k(this,I)[I]([e])}else k(this,I)[I]([i])});let t=null;this.on("upload-stalled",(e,i)=>{let{message:r}=e,s=i.map(e=>e.meta.name).join(", ");t||(this.info({message:r,details:s},"warning",this.opts.infoTimeout),t=setTimeout(()=>{t=null},this.opts.infoTimeout)),this.log(`${r} ${s}`.trim(),"warning")}),this.on("upload",()=>{this.setState({error:null})}),this.on("upload-start",e=>{let t=Object.fromEntries(e.filter(e=>{let t=null!=e&&this.getFile(e.id);return t||this.log(`Not setting progress for a file that has been removed: ${null==e?void 0:e.id}`),t}).map(e=>[e.id,{progress:{uploadStarted:Date.now(),uploadComplete:!1,bytesUploaded:0,bytesTotal:e.size}}]));this.patchFilesState(t)}),this.on("upload-progress",k(this,X)[X]),this.on("upload-success",(e,t)=>{if(null==e||!this.getFile(e.id))return void this.log(`Not setting progress for a file that has been removed: ${null==e?void 0:e.id}`);let i=this.getFile(e.id).progress;this.setFileState(e.id,{progress:{...i,postprocess:k(this,z)[z].size>0?{mode:"indeterminate"}:void 0,uploadComplete:!0,percentage:100,bytesUploaded:i.bytesTotal},response:t,uploadURL:t.uploadURL,isPaused:!1}),null==e.size&&this.setFileState(e.id,{size:t.bytesUploaded||i.bytesTotal}),k(this,Y)[Y]()}),this.on("preprocess-progress",(e,t)=>{if(null==e||!this.getFile(e.id))return void this.log(`Not setting progress for a file that has been removed: ${null==e?void 0:e.id}`);this.setFileState(e.id,{progress:{...this.getFile(e.id).progress,preprocess:t}})}),this.on("preprocess-complete",e=>{if(null==e||!this.getFile(e.id))return void this.log(`Not setting progress for a file that has been removed: ${null==e?void 0:e.id}`);let t={...this.getState().files};t[e.id]={...t[e.id],progress:{...t[e.id].progress}},delete t[e.id].progress.preprocess,this.setState({files:t})}),this.on("postprocess-progress",(e,t)=>{if(null==e||!this.getFile(e.id))return void this.log(`Not setting progress for a file that has been removed: ${null==e?void 0:e.id}`);this.setFileState(e.id,{progress:{...this.getState().files[e.id].progress,postprocess:t}})}),this.on("postprocess-complete",e=>{if(null==e||!this.getFile(e.id))return void this.log(`Not setting progress for a file that has been removed: ${null==e?void 0:e.id}`);let t={...this.getState().files};t[e.id]={...t[e.id],progress:{...t[e.id].progress}},delete t[e.id].progress.postprocess,this.setState({files:t})}),this.on("restored",()=>{k(this,Y)[Y]()}),this.on("dashboard:file-edit-complete",e=>{e&&k(this,N)[N](e)}),"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("online",k(this,Q)[Q]),window.addEventListener("offline",k(this,Q)[Q]),setTimeout(k(this,Q)[Q],3e3))}function ey(e,t){void 0===t&&(t={});let{forceAllowNewUpload:i=!1}=t,{allowNewUpload:r,currentUploads:s}=this.getState();if(!r&&!i)throw Error("Cannot create a new upload: already uploading.");let o=((e=21)=>{let t="",i=0|e;for(;i--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[64*Math.random()|0];return t})();return this.emit("upload",o,this.getFilesByIds(e)),this.setState({allowNewUpload:!1!==this.opts.allowMultipleUploadBatches&&!1!==this.opts.allowMultipleUploads,currentUploads:{...s,[o]:{fileIDs:e,step:0,result:{}}}}),o}function ev(e){let{currentUploads:t}=this.getState();return t[e]}function eb(e){let t={...this.getState().currentUploads};delete t[e],this.setState({currentUploads:t})}async function ew(e){let t,i=()=>{let{currentUploads:t}=this.getState();return t[e]},r=i(),s=[...k(this,M)[M],...k(this,$)[$],...k(this,z)[z]];try{for(let t=r.step||0;t<s.length&&r;t++){let o=s[t];this.setState({currentUploads:{...this.getState().currentUploads,[e]:{...r,step:t}}});let{fileIDs:a}=r;await o(a,e),r=i()}}catch(t){throw k(this,ei)[ei](e),t}if(r){r.fileIDs.forEach(e=>{let t=this.getFile(e);t&&t.progress.postprocess&&this.emit("postprocess-complete",t)});let t=r.fileIDs.map(e=>this.getFile(e)),s=t.filter(e=>!e.error),o=t.filter(e=>e.error);this.addResultData(e,{successful:s,failed:o,uploadID:e}),r=i()}return r&&(t=r.result,k(this,ei)[ei](e)),null==t&&(this.log(`Not setting result for an upload that has been removed: ${e}`),t={successful:[],failed:[],uploadID:e}),t}es.VERSION="4.4.7";let eP=es},4186:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4217:(e,t,i)=>{var r=i(6713),s=/^\s+/;e.exports=function(e){return e?e.slice(0,r(e)+1).replace(s,""):e}},4376:(e,t,i)=>{e.exports=i(2500).Symbol},4439:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},4592:e=>{"use strict";e.exports=function(e){if("number"!=typeof e||Number.isNaN(e))throw TypeError(`Expected a number, got ${typeof e}`);let t=Math.abs(e);if(e<0&&(t=-t),0===t)return"0 B";let i=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],r=Math.min(Math.floor(Math.log(t)/Math.log(1024)),i.length-1),s=Number(t/1024**r),o=i[r];return`${s>=10||s%1==0?Math.round(s):s.toFixed(1)} ${o}`}},4896:(e,t,i)=>{e.exports=i(342)},5169:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5863:(e,t,i)=>{"use strict";i.d(t,{C1:()=>P,bL:()=>w});var r=i(2115),s=i(6081),o=i(3655),a=i(5155),n="Progress",[l,u]=(0,s.A)(n),[h,d]=l(n),p=r.forwardRef((e,t)=>{var i,r,s,n;let{__scopeProgress:l,value:u=null,max:d,getValueLabel:p=m,...c}=e;(d||0===d)&&!v(d)&&console.error((i="".concat(d),r="Progress","Invalid prop `max` of value `".concat(i,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let f=v(d)?d:100;null===u||b(u,f)||console.error((s="".concat(u),n="Progress","Invalid prop `value` of value `".concat(s,"` supplied to `").concat(n,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let w=b(u,f)?u:null,P=y(w)?p(w,f):void 0;return(0,a.jsx)(h,{scope:l,value:w,max:f,children:(0,a.jsx)(o.sG.div,{"aria-valuemax":f,"aria-valuemin":0,"aria-valuenow":y(w)?w:void 0,"aria-valuetext":P,role:"progressbar","data-state":g(w,f),"data-value":null!=w?w:void 0,"data-max":f,...c,ref:t})})});p.displayName=n;var c="ProgressIndicator",f=r.forwardRef((e,t)=>{var i;let{__scopeProgress:r,...s}=e,n=d(c,r);return(0,a.jsx)(o.sG.div,{"data-state":g(n.value,n.max),"data-value":null!=(i=n.value)?i:void 0,"data-max":n.max,...s,ref:t})});function m(e,t){return"".concat(Math.round(e/t*100),"%")}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function v(e){return y(e)&&!isNaN(e)&&e>0}function b(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=c;var w=p,P=f},5880:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]])},5964:(e,t,i)=>{var r=i(7460),s=i(6685),o=i(6815),a=Math.max,n=Math.min;e.exports=function(e,t,i){var l,u,h,d,p,c,f=0,m=!1,g=!1,y=!0;if("function"!=typeof e)throw TypeError("Expected a function");function v(t){var i=l,r=u;return l=u=void 0,f=t,d=e.apply(r,i)}function b(e){var i=e-c,r=e-f;return void 0===c||i>=t||i<0||g&&r>=h}function w(){var e,i,r,o=s();if(b(o))return P(o);p=setTimeout(w,(e=o-c,i=o-f,r=t-e,g?n(r,h-i):r))}function P(e){return(p=void 0,y&&l)?v(e):(l=u=void 0,d)}function S(){var e,i=s(),r=b(i);if(l=arguments,u=this,c=i,r){if(void 0===p)return f=e=c,p=setTimeout(w,t),m?v(e):d;if(g)return clearTimeout(p),p=setTimeout(w,t),v(c)}return void 0===p&&(p=setTimeout(w,t)),d}return t=o(t)||0,r(i)&&(m=!!i.leading,h=(g="maxWait"in i)?a(o(i.maxWait)||0,t):h,y="trailing"in i?!!i.trailing:y),S.cancel=function(){void 0!==p&&clearTimeout(p),f=0,l=c=u=p=void 0},S.flush=function(){return void 0===p?d:P(s())},S}},5994:e=>{e.exports=function(){var e={},t=e._fns={};return e.emit=function(e,i,r,s,o,a,n){var l=function(e){for(var i=t[e]?t[e]:[],r=e.indexOf(":"),s=-1===r?[e]:[e.substring(0,r),e.substring(r+1)],o=Object.keys(t),a=0,n=o.length;a<n;a++){var l=o[a];if("*"===l&&(i=i.concat(t[l])),2===s.length&&s[0]===l){i=i.concat(t[l]);break}}return i}(e);l.length&&function(e,t,i){for(var r=0,s=t.length;r<s&&t[r];r++)t[r].event=e,t[r].apply(t[r],i)}(e,l,[i,r,s,o,a,n])},e.on=function(e,i){t[e]||(t[e]=[]),t[e].push(i)},e.once=function(t,i){this.on(t,function r(){i.apply(this,arguments),e.off(t,r)})},e.off=function(e,t){var i=[];if(e&&t)for(var r=this._fns[e],s=0,o=r?r.length:0;s<o;s++)r[s]!==t&&i.push(r[s]);i.length?this._fns[e]=i:delete this._fns[e]},e}},6287:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},6472:(e,t,i)=>{"use strict";i.d(t,{A:()=>tO});var r=i(6586);class s{constructor(e,t){this.uppy=e,this.opts=null!=t?t:{}}getPluginState(){let{plugins:e}=this.uppy.getState();return(null==e?void 0:e[this.id])||{}}setPluginState(e){let{plugins:t}=this.uppy.getState();this.uppy.setState({plugins:{...t,[this.id]:{...t[this.id],...e}}})}setOptions(e){this.opts={...this.opts,...e},this.setPluginState(void 0),this.i18nInit()}i18nInit(){let e=new r.A([this.defaultLocale,this.uppy.locale,this.opts.locale]);this.i18n=e.translate.bind(e),this.i18nArray=e.translateArray.bind(e),this.setPluginState(void 0)}addTarget(e){throw Error("Extend the addTarget method to add your plugin to another plugin's target")}install(){}uninstall(){}update(e){}afterUpdate(){}}class o extends Error{constructor(){super(...arguments),this.name="UserFacingApiError"}}var a=i(4896);let n=Object.prototype.toString,l=new Set(["network error","Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Load failed","Network request failed","fetch failed","terminated"]);class u extends Error{constructor(e){super(),e instanceof Error?(this.originalError=e,{message:e}=e):(this.originalError=Error(e),this.originalError.stack=this.stack),this.name="AbortError",this.message=e}}let h=(e,t,i)=>{let r=i.retries-(t-1);return e.attemptNumber=t,e.retriesLeft=r,e};async function d(e,t){return new Promise((i,r)=>{t={...t},t.onFailedAttempt??=()=>{},t.shouldRetry??=()=>!0,t.retries??=10;let s=a.operation(t),o=()=>{s.stop(),r(t.signal?.reason)};t.signal&&!t.signal.aborted&&t.signal.addEventListener("abort",o,{once:!0});let d=()=>{t.signal?.removeEventListener("abort",o),s.stop()};s.attempt(async o=>{try{let t=await e(o);d(),i(t)}catch(e){try{if(!(e instanceof Error))throw TypeError(`Non-error was thrown: "${e}". You should only throw errors.`);if(e instanceof u)throw e.originalError;if(e instanceof TypeError&&!(e&&"[object Error]"===n.call(e)&&"TypeError"===e.name&&"string"==typeof e.message&&("Load failed"===e.message?void 0===e.stack:l.has(e.message))))throw e;if(h(e,o,t),await t.shouldRetry(e)||(s.stop(),r(e)),await t.onFailedAttempt(e),!s.retry(e))throw s.mainError()}catch(e){h(e,o,t),d(),r(e)}}})})}class p extends Error{constructor(e,t){void 0===t&&(t=null),super("This looks like a network error, the endpoint might be blocked by an internet provider or a firewall."),this.cause=e,this.isNetworkError=!0,this.request=t}}function c(e,t){return Object.prototype.hasOwnProperty.call(e,t)}class f extends Error{constructor(e,t){super(e),this.cause=null==t?void 0:t.cause,this.cause&&c(this.cause,"isNetworkError")?this.isNetworkError=this.cause.isNetworkError:this.isNetworkError=!1}}class m extends Error{constructor(){super("Authorization required"),this.name="AuthError",this.isAuthError=!0}}function g(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var y=0;function v(e){return"__private_"+y+++"_"+e}class b extends Error{constructor(e){let{statusCode:t,message:i}=e;super(i),this.name="HttpError",this.statusCode=t}}async function w(e){let t;if(401===e.status)throw new m;if(e.ok)return e.json();let i=`Failed request with status: ${e.status}. ${e.statusText}`;try{(t=await e.json()).message&&(i=`${i} message: ${t.message}`),t.requestId&&(i=`${i} request-Id: ${t.requestId}`)}catch(e){throw Error(i,{cause:e})}if(e.status>=400&&e.status<=499&&t.message)throw new o(t.message);throw new b({statusCode:e.status,message:i})}var P=v("companionHeaders"),S=v("getUrl"),O=v("requestSocketToken"),x=v("awaitRemoteFileUpload");class E{constructor(e,t){Object.defineProperty(this,x,{value:k}),Object.defineProperty(this,S,{value:j}),Object.defineProperty(this,P,{writable:!0,value:void 0}),Object.defineProperty(this,O,{writable:!0,value:async e=>{var t;let{file:i,postBody:r,signal:s}=e;if((null==(t=i.remote)?void 0:t.url)==null)throw Error("Cannot connect to an undefined URL");return(await this.post(i.remote.url,{...i.remote.body,...r},{signal:s})).token}}),this.uppy=e,this.opts=t,this.onReceiveResponse=this.onReceiveResponse.bind(this),g(this,P)[P]=t.companionHeaders}setCompanionHeaders(e){g(this,P)[P]=e}[Symbol.for("uppy test: getCompanionHeaders")](){return g(this,P)[P]}get hostname(){let{companion:e}=this.uppy.getState(),t=this.opts.companionUrl;return(e&&e[t]?e[t]:t).replace(/\/$/,"")}async headers(e){return void 0===e&&(e=!1),{Accept:"application/json",...e?void 0:{"Content-Type":"application/json"},...g(this,P)[P]}}onReceiveResponse(e){let{headers:t}=e,i=this.uppy.getState().companion||{},r=this.opts.companionUrl;t.has("i-am")&&t.get("i-am")!==i[r]&&this.uppy.setState({companion:{...i,[r]:t.get("i-am")}})}async request(e){let{path:t,method:i="GET",data:r,skipPostResponse:s,signal:o}=e;try{let e=await this.headers(!r),a=await function(){return fetch(...arguments).catch(e=>{if("AbortError"===e.name)throw e;throw new p(e)})}(g(this,S)[S](t),{method:i,signal:o,headers:e,credentials:this.opts.companionCookiesRule||"same-origin",body:r?JSON.stringify(r):null});return s||this.onReceiveResponse(a),await w(a)}catch(e){if(e.isAuthError||"UserFacingApiError"===e.name||"AbortError"===e.name)throw e;throw new f(`Could not ${i} ${g(this,S)[S](t)}`,{cause:e})}}async get(e,t){return this.request({...t,path:e})}async post(e,t,i){return this.request({...i,path:e,method:"POST",data:t})}async delete(e,t,i){return this.request({...i,path:e,method:"DELETE",data:t})}async uploadRemoteFile(e,t,i){var r=this;try{let{signal:s,getQueue:o}=i||{};return await d(async()=>{var i;let a=null==(i=this.uppy.getFile(e.id))?void 0:i.serverToken;if(null!=a)return this.uppy.log(`Connecting to exiting websocket ${a}`),g(this,x)[x]({file:e,queue:o(),signal:s});let n=o().wrapPromiseFunction(async function(){try{return await g(r,O)[O](...arguments)}catch(t){if(t.isAuthError)throw new u(t);if(null==t.cause)throw t;let e=t.cause;if("HttpError"===e.name&&!([408,409,429,418,423].includes(e.statusCode)||e.statusCode>=500&&e.statusCode<=599&&![501,505].includes(e.statusCode)))throw new u(e);throw e}},{priority:-1}),l=await n({file:e,postBody:t,signal:s}).abortOn(s);if(this.uppy.getFile(e.id))return this.uppy.setFileState(e.id,{serverToken:l}),g(this,x)[x]({file:this.uppy.getFile(e.id),queue:o(),signal:s})},{retries:10,signal:s,onFailedAttempt:e=>this.uppy.log(`Retrying upload due to: ${e.message}`,"warning")})}catch(t){if("AbortError"===t.name)return;throw this.uppy.emit("upload-error",e,t),t}}}function j(e){return/^(https?:|)\/\//.test(e)?e:`${this.hostname}/${e}`}async function k(e){let t,{file:i,queue:r,signal:s}=e,{capabilities:o}=this.uppy.getState();try{return await new Promise((e,a)=>{let n,l,u,h=i.serverToken,p=function(e){var t;let i=null==(t=/^(?:https?:\/\/|\/\/)?(?:[^@\n]+@)?([^\n]+)/i.exec(e))?void 0:t[1],r=/^http:\/\//i.test(e)?"ws":"wss";return`${r}://${i}`}(i.remote.companionUrl),{isPaused:c}=i,f=(e,t)=>{if(null==n||n.readyState!==n.OPEN){var r;this.uppy.log(`Cannot send "${e}" to socket ${i.id} because the socket state was ${String(null==(r=n)?void 0:r.readyState)}`,"warning");return}n.send(JSON.stringify({action:e,payload:null!=t?t:{}}))};function m(){o.resumableUploads&&(c?f("pause"):f("resume"))}let g=async()=>{l&&l.abort(),l=new AbortController;let t=e=>{var t;this.uppy.setFileState(i.id,{serverToken:null}),null==(t=l)||null==t.abort||t.abort(),a(e)};function s(){clearTimeout(u),c||(u=setTimeout(()=>t(Error("Timeout waiting for message from Companion socket")),3e5))}try{await r.wrapPromiseFunction(async()=>{let r=async()=>new Promise((r,o)=>{n=new WebSocket(`${p}/api/${h}`),s(),n.addEventListener("close",()=>{n=void 0,o(Error("Socket closed unexpectedly"))}),n.addEventListener("error",e=>{var t;this.uppy.log(`Companion socket error ${JSON.stringify(e)}, closing socket`,"warning"),null==(t=n)||t.close()}),n.addEventListener("open",()=>{m()}),n.addEventListener("message",r=>{s();try{let{action:t,payload:s}=JSON.parse(r.data);switch(t){case"progress":!function(e,t,i){let{progress:r,bytesUploaded:s,bytesTotal:o}=t;if(r){var a;e.uppy.log(`Upload progress: ${r}`),e.uppy.emit("upload-progress",i,{uploadStarted:null!=(a=i.progress.uploadStarted)?a:0,bytesUploaded:s,bytesTotal:o})}}(this,s,this.uppy.getFile(i.id));break;case"success":{var o,a,n,u;let t=null==(o=s.response)?void 0:o.responseText;this.uppy.emit("upload-success",this.uppy.getFile(i.id),{uploadURL:s.url,status:null!=(a=null==(n=s.response)?void 0:n.status)?a:200,body:t?JSON.parse(t):void 0}),null==(u=l)||null==u.abort||u.abort(),e();break}case"error":{let{message:e}=s.error;throw Object.assign(Error(e),{cause:s.error})}default:this.uppy.log(`Companion socket unknown action ${t}`,"warning")}}catch(e){t(e)}}),l.signal.addEventListener("abort",()=>{this.uppy.log(`Closing socket ${i.id}`),clearTimeout(u),n&&n.close(),n=void 0})});await d(r,{retries:10,signal:l.signal,onFailedAttempt:()=>{l.signal.aborted||this.uppy.log(`Retrying websocket ${i.id}`)}})})().abortOn(l.signal)}catch(e){if(l.signal.aborted)return;t(e)}},y=e=>{o.resumableUploads&&(c=e,n&&m())},v=t=>{var r;o.individualCancellation&&t.id===i.id&&(f("cancel"),null==(r=l)||null==r.abort||r.abort(),this.uppy.log(`upload ${i.id} was removed`),e())},b=()=>{var t;f("cancel"),null==(t=l)||null==t.abort||t.abort(),this.uppy.log(`upload ${i.id} was canceled`),e()},w=(e,t)=>{(null==e?void 0:e.id)===i.id&&y(t)},P=()=>y(!0),S=()=>y(!1);this.uppy.on("file-removed",v),this.uppy.on("cancel-all",b),this.uppy.on("upload-pause",w),this.uppy.on("pause-all",P),this.uppy.on("resume-all",S),t=()=>{this.uppy.off("file-removed",v),this.uppy.off("cancel-all",b),this.uppy.off("upload-pause",w),this.uppy.off("pause-all",P),this.uppy.off("resume-all",S)},s.addEventListener("abort",()=>{var e;null==(e=l)||e.abort()}),g()})}finally{null==t||t()}}E.VERSION="4.4.2";function F(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var A=0;function U(e){return"__private_"+A+++"_"+e}var T=U("uppy"),C=U("events");class R{constructor(e){Object.defineProperty(this,T,{writable:!0,value:void 0}),Object.defineProperty(this,C,{writable:!0,value:[]}),F(this,T)[T]=e}on(e,t){return F(this,C)[C].push([e,t]),F(this,T)[T].on(e,t)}remove(){for(let[e,t]of F(this,C)[C].splice(0))F(this,T)[T].off(e,t)}onFilePause(e,t){this.on("upload-pause",(i,r)=>{e===(null==i?void 0:i.id)&&t(r)})}onFileRemove(e,t){this.on("file-removed",i=>{e===i.id&&t(i.id)})}onPause(e,t){this.on("upload-pause",(i,r)=>{e===(null==i?void 0:i.id)&&t(r)})}onRetry(e,t){this.on("upload-retry",i=>{e===(null==i?void 0:i.id)&&t()})}onRetryAll(e,t){this.on("retry-all",()=>{F(this,T)[T].getFile(e)&&t()})}onPauseAll(e,t){this.on("pause-all",()=>{F(this,T)[T].getFile(e)&&t()})}onCancelAll(e,t){var i=this;this.on("cancel-all",function(){F(i,T)[T].getFile(e)&&t(...arguments)})}onResumeAll(e,t){this.on("resume-all",()=>{F(this,T)[T].getFile(e)&&t()})}}function _(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var M=0;function $(e){return"__private_"+M+++"_"+e}function z(e){if(null!=e){var t;let i=()=>this.abort(e.reason);e.addEventListener("abort",i,{once:!0});let r=()=>{e.removeEventListener("abort",i)};null==(t=this.then)||t.call(this,r,r)}return this}var I=$("activeRequests"),N=$("queuedHandlers"),L=$("paused"),q=$("pauseTimer"),H=$("downLimit"),D=$("upperLimit"),B=$("rateLimitingTimer"),W=$("call"),G=$("queueNext"),X=$("next"),V=$("queue"),Y=$("dequeue"),K=$("resume"),J=$("increaseLimit");class Q{constructor(e){Object.defineProperty(this,Y,{value:er}),Object.defineProperty(this,V,{value:ei}),Object.defineProperty(this,X,{value:et}),Object.defineProperty(this,G,{value:ee}),Object.defineProperty(this,W,{value:Z}),Object.defineProperty(this,I,{writable:!0,value:0}),Object.defineProperty(this,N,{writable:!0,value:[]}),Object.defineProperty(this,L,{writable:!0,value:!1}),Object.defineProperty(this,q,{writable:!0,value:void 0}),Object.defineProperty(this,H,{writable:!0,value:1}),Object.defineProperty(this,D,{writable:!0,value:void 0}),Object.defineProperty(this,B,{writable:!0,value:void 0}),Object.defineProperty(this,K,{writable:!0,value:()=>this.resume()}),Object.defineProperty(this,J,{writable:!0,value:()=>{if(_(this,L)[L]){_(this,B)[B]=setTimeout(_(this,J)[J],0);return}_(this,H)[H]=this.limit,this.limit=Math.ceil((_(this,D)[D]+_(this,H)[H])/2);for(let e=_(this,H)[H];e<=this.limit;e++)_(this,G)[G]();_(this,D)[D]-_(this,H)[H]>3?_(this,B)[B]=setTimeout(_(this,J)[J],2e3):_(this,H)[H]=Math.floor(_(this,H)[H]/2)}}),"number"!=typeof e||0===e?this.limit=1/0:this.limit=e}run(e,t){return!_(this,L)[L]&&_(this,I)[I]<this.limit?_(this,W)[W](e):_(this,V)[V](e,t)}wrapSyncFunction(e,t){var i=this;return function(){for(var r=arguments.length,s=Array(r),o=0;o<r;o++)s[o]=arguments[o];let a=i.run(()=>(e(...s),queueMicrotask(()=>a.done()),()=>{}),t);return{abortOn:z,abort(){a.abort()}}}}wrapPromiseFunction(e,t){var i=this;return function(){let r;for(var s=arguments.length,o=Array(s),a=0;a<s;a++)o[a]=arguments[a];let n=new Promise((s,a)=>{r=i.run(()=>{let t,i;try{i=Promise.resolve(e(...o))}catch(e){i=Promise.reject(e)}return i.then(e=>{t?a(t):(r.done(),s(e))},e=>{t?a(t):(r.done(),a(e))}),e=>{t=Error("Cancelled",{cause:e})}},t)});return n.abort=e=>{r.abort(e)},n.abortOn=z,n}}resume(){_(this,L)[L]=!1,clearTimeout(_(this,q)[q]);for(let e=0;e<this.limit;e++)_(this,G)[G]()}pause(e){void 0===e&&(e=null),_(this,L)[L]=!0,clearTimeout(_(this,q)[q]),null!=e&&(_(this,q)[q]=setTimeout(_(this,K)[K],e))}rateLimit(e){clearTimeout(_(this,B)[B]),this.pause(e),this.limit>1&&Number.isFinite(this.limit)&&(_(this,D)[D]=this.limit-1,this.limit=_(this,H)[H],_(this,B)[B]=setTimeout(_(this,J)[J],e))}get isPaused(){return _(this,L)[L]}}function Z(e){let t;_(this,I)[I]+=1;let i=!1;try{t=e()}catch(e){throw _(this,I)[I]-=1,e}return{abort:e=>{i||(i=!0,_(this,I)[I]-=1,null==t||t(e),_(this,G)[G]())},done:()=>{i||(i=!0,_(this,I)[I]-=1,_(this,G)[G]())}}}function ee(){queueMicrotask(()=>_(this,X)[X]())}function et(){if(_(this,L)[L]||_(this,I)[I]>=this.limit||0===_(this,N)[N].length)return;let e=_(this,N)[N].shift();if(null==e)throw Error("Invariant violation: next is null");let t=_(this,W)[W](e.fn);e.abort=t.abort,e.done=t.done}function ei(e,t){let i={fn:e,priority:(null==t?void 0:t.priority)||0,abort:()=>{_(this,Y)[Y](i)},done:()=>{throw Error("Cannot mark a queued request as done: this indicates a bug")}},r=_(this,N)[N].findIndex(e=>i.priority>e.priority);return -1===r?_(this,N)[N].push(i):_(this,N)[N].splice(r,0,i),i}function er(e){let t=_(this,N)[N].indexOf(e);-1!==t&&_(this,N)[N].splice(t,1)}Symbol("__queue");let{AbortController:es}=globalThis,{AbortSignal:eo}=globalThis,ea=function(e,t){void 0===e&&(e="Aborted");let i=new DOMException(e,"AbortError");return null!=t&&c(t,"cause")&&Object.defineProperty(i,"cause",{__proto__:null,configurable:!0,writable:!0,value:t.cause}),i};function en(e,t){return!0===e?Object.keys(t):Array.isArray(e)?e:[]}function el(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var eu=0;function eh(e){return"__private_"+eu+++"_"+e}let ed={getChunkSize:e=>Math.ceil(e.size/1e4),onProgress(){},onPartComplete(){},onSuccess(){},onError(e){throw e}},ep=Symbol("pausing upload, not an actual error");var ec=eh("abortController"),ef=eh("chunks"),em=eh("chunkState"),eg=eh("data"),ey=eh("file"),ev=eh("uploadHasStarted"),eb=eh("onError"),ew=eh("onSuccess"),eP=eh("shouldUseMultipart"),eS=eh("isRestoring"),eO=eh("onReject"),ex=eh("maxMultipartParts"),eE=eh("minPartSize"),ej=eh("initChunks"),ek=eh("createUpload"),eF=eh("resumeUpload"),eA=eh("onPartProgress"),eU=eh("onPartComplete"),eT=eh("abortUpload");class eC{constructor(e,t){var i;Object.defineProperty(this,eT,{value:e$}),Object.defineProperty(this,eF,{value:eM}),Object.defineProperty(this,ek,{value:e_}),Object.defineProperty(this,ej,{value:eR}),Object.defineProperty(this,ec,{writable:!0,value:new es}),Object.defineProperty(this,ef,{writable:!0,value:[]}),Object.defineProperty(this,em,{writable:!0,value:[]}),Object.defineProperty(this,eg,{writable:!0,value:void 0}),Object.defineProperty(this,ey,{writable:!0,value:void 0}),Object.defineProperty(this,ev,{writable:!0,value:!1}),Object.defineProperty(this,eb,{writable:!0,value:void 0}),Object.defineProperty(this,ew,{writable:!0,value:void 0}),Object.defineProperty(this,eP,{writable:!0,value:void 0}),Object.defineProperty(this,eS,{writable:!0,value:void 0}),Object.defineProperty(this,eO,{writable:!0,value:e=>(null==e?void 0:e.cause)===ep?null:el(this,eb)[eb](e)}),Object.defineProperty(this,ex,{writable:!0,value:1e4}),Object.defineProperty(this,eE,{writable:!0,value:5242880}),Object.defineProperty(this,eA,{writable:!0,value:e=>t=>{if(!t.lengthComputable)return;el(this,em)[em][e].uploaded=function(e){if("string"==typeof e)return parseInt(e,10);if("number"==typeof e)return e;throw TypeError("Expected a number")}(t.loaded);let i=el(this,em)[em].reduce((e,t)=>e+t.uploaded,0);this.options.onProgress(i,el(this,eg)[eg].size)}}),Object.defineProperty(this,eU,{writable:!0,value:e=>t=>{el(this,ef)[ef][e]=null,el(this,em)[em][e].etag=t,el(this,em)[em][e].done=!0,this.options.onPartComplete({PartNumber:e+1,ETag:t})}}),this.options={...ed,...t},null!=(i=this.options).getChunkSize||(i.getChunkSize=ed.getChunkSize),el(this,eg)[eg]=e,el(this,ey)[ey]=t.file,el(this,ew)[ew]=this.options.onSuccess,el(this,eb)[eb]=this.options.onError,el(this,eP)[eP]=this.options.shouldUseMultipart,el(this,eS)[eS]=t.uploadId&&t.key,el(this,ej)[ej]()}start(){el(this,ev)[ev]?(el(this,ec)[ec].signal.aborted||el(this,ec)[ec].abort(ep),el(this,ec)[ec]=new es,el(this,eF)[eF]()):el(this,eS)[eS]?(this.options.companionComm.restoreUploadFile(el(this,ey)[ey],{uploadId:this.options.uploadId,key:this.options.key}),el(this,eF)[eF]()):el(this,ek)[ek]()}pause(){el(this,ec)[ec].abort(ep),el(this,ec)[ec]=new es}abort(e){null!=e&&e.really?el(this,eT)[eT]():this.pause()}[Symbol.for("uppy test: getChunkState")](){return el(this,em)[em]}}function eR(){let e=el(this,eg)[eg].size,t="function"==typeof el(this,eP)[eP]?el(this,eP)[eP](el(this,ey)[ey]):!!el(this,eP)[eP];if(t&&e>el(this,eE)[eE]){let i=Math.max(this.options.getChunkSize(el(this,eg)[eg]),el(this,eE)[eE]),r=Math.floor(e/i);r>el(this,ex)[ex]&&(r=el(this,ex)[ex],i=e/el(this,ex)[ex]),el(this,ef)[ef]=Array(r);for(let r=0,s=0;r<e;r+=i,s++){let o=Math.min(e,r+i),a=()=>{let e=r;return el(this,eg)[eg].slice(e,o)};if(el(this,ef)[ef][s]={getData:a,onProgress:el(this,eA)[eA](s),onComplete:el(this,eU)[eU](s),shouldUseMultipart:t},el(this,eS)[eS]){let t=r+i>e?e-r:i;el(this,ef)[ef][s].setAsUploaded=()=>{el(this,ef)[ef][s]=null,el(this,em)[em][s].uploaded=t}}}}else el(this,ef)[ef]=[{getData:()=>el(this,eg)[eg],onProgress:el(this,eA)[eA](0),onComplete:el(this,eU)[eU](0),shouldUseMultipart:t}];el(this,em)[em]=el(this,ef)[ef].map(()=>({uploaded:0}))}function e_(){this.options.companionComm.uploadFile(el(this,ey)[ey],el(this,ef)[ef],el(this,ec)[ec].signal).then(el(this,ew)[ew],el(this,eO)[eO]),el(this,ev)[ev]=!0}function eM(){this.options.companionComm.resumeUploadFile(el(this,ey)[ey],el(this,ef)[ef],el(this,ec)[ec].signal).then(el(this,ew)[ew],el(this,eO)[eO])}function e$(){el(this,ec)[ec].abort(),this.options.companionComm.abortFileUpload(el(this,ey)[ey]).catch(e=>this.options.log(e))}function ez(e){if(null!=e&&e.aborted)throw ea("The operation was aborted",{cause:e.reason})}let eI=new TextEncoder,eN={name:"HMAC",hash:"SHA-256"};async function eL(e){let{subtle:t}=globalThis.crypto;return t.digest(eN.hash,eI.encode(e))}async function eq(e){let{subtle:t}=globalThis.crypto;return t.importKey("raw","string"==typeof e?eI.encode(e):e,eN,!1,["sign"])}function eH(e){let t=new Uint8Array(e),i="";for(let e=0;e<t.length;e++)i+=t[e].toString(16).padStart(2,"0");return i}async function eD(e,t){let{subtle:i}=globalThis.crypto;return i.sign(eN,await eq(e),eI.encode(t))}async function eB(e){let{accountKey:t,accountSecret:i,sessionToken:r,bucketName:s,Key:o,Region:a,expires:n,uploadId:l,partNumber:u}=e,h=`s3.${a}.amazonaws.com`,d=`/${s}/${encodeURI(o).replace(/[;?:@&=+$,#!'()*]/g,e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`)}`,p="UNSIGNED-PAYLOAD",c=new Date().toISOString().replace(/[-:]|\.\d+/g,""),f=c.slice(0,8),m=`${f}/${a}/s3/aws4_request`,g=new URL(`https://${h}${d}`);g.searchParams.set("X-Amz-Algorithm","AWS4-HMAC-SHA256"),g.searchParams.set("X-Amz-Content-Sha256",p),g.searchParams.set("X-Amz-Credential",`${t}/${m}`),g.searchParams.set("X-Amz-Date",c),g.searchParams.set("X-Amz-Expires",n),g.searchParams.set("X-Amz-Security-Token",r),g.searchParams.set("X-Amz-SignedHeaders","host"),u&&g.searchParams.set("partNumber",u),l&&g.searchParams.set("uploadId",l),g.searchParams.set("x-id",u&&l?"UploadPart":"PutObject");let y=function(e){let{method:t="PUT",CanonicalUri:i="/",CanonicalQueryString:r="",SignedHeaders:s,HashedPayload:o}=e,a=Object.keys(s).map(e=>e.toLowerCase()).sort();return[t,i,r,...a.map(e=>`${e}:${s[e]}`),"",a.join(";"),o].join("\n")}({CanonicalUri:d,CanonicalQueryString:g.search.slice(1),SignedHeaders:{host:h},HashedPayload:p}),v=["AWS4-HMAC-SHA256",c,m,eH(await eL(y))].join("\n"),b=await eD(`AWS4${i}`,f),w=await eD(b,a),P=await eD(w,"s3"),S=await eD(P,"aws4_request"),O=eH(await eD(S,v));return g.searchParams.set("X-Amz-Signature",O),g}function eW(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var eG=0;function eX(e){return"__private_"+eG+++"_"+e}var eV=eX("abortMultipartUpload"),eY=eX("cache"),eK=eX("createMultipartUpload"),eJ=eX("fetchSignature"),eQ=eX("getUploadParameters"),eZ=eX("listParts"),e0=eX("previousRetryDelay"),e1=eX("requests"),e2=eX("retryDelays"),e4=eX("sendCompletionRequest"),e3=eX("setS3MultipartState"),e6=eX("uploadPartBytes"),e8=eX("getFile"),e5=eX("shouldRetry"),e9=eX("nonMultipartUpload");class e7{constructor(e,t,i,r){Object.defineProperty(this,e9,{value:tt}),Object.defineProperty(this,e5,{value:te}),Object.defineProperty(this,eV,{writable:!0,value:void 0}),Object.defineProperty(this,eY,{writable:!0,value:new WeakMap}),Object.defineProperty(this,eK,{writable:!0,value:void 0}),Object.defineProperty(this,eJ,{writable:!0,value:void 0}),Object.defineProperty(this,eQ,{writable:!0,value:void 0}),Object.defineProperty(this,eZ,{writable:!0,value:void 0}),Object.defineProperty(this,e0,{writable:!0,value:void 0}),Object.defineProperty(this,e1,{writable:!0,value:void 0}),Object.defineProperty(this,e2,{writable:!0,value:void 0}),Object.defineProperty(this,e4,{writable:!0,value:void 0}),Object.defineProperty(this,e3,{writable:!0,value:void 0}),Object.defineProperty(this,e6,{writable:!0,value:void 0}),Object.defineProperty(this,e8,{writable:!0,value:void 0}),eW(this,e1)[e1]=e,eW(this,e3)[e3]=i,eW(this,e8)[e8]=r,this.setOptions(t)}setOptions(e){let t=eW(this,e1)[e1];if("abortMultipartUpload"in e&&(eW(this,eV)[eV]=t.wrapPromiseFunction(e.abortMultipartUpload,{priority:1})),"createMultipartUpload"in e&&(eW(this,eK)[eK]=t.wrapPromiseFunction(e.createMultipartUpload,{priority:-1})),"signPart"in e&&(eW(this,eJ)[eJ]=t.wrapPromiseFunction(e.signPart)),"listParts"in e&&(eW(this,eZ)[eZ]=t.wrapPromiseFunction(e.listParts)),"completeMultipartUpload"in e&&(eW(this,e4)[e4]=t.wrapPromiseFunction(e.completeMultipartUpload,{priority:1})),"retryDelays"in e){var i;eW(this,e2)[e2]=null!=(i=e.retryDelays)?i:[]}"uploadPartBytes"in e&&(eW(this,e6)[e6]=t.wrapPromiseFunction(e.uploadPartBytes,{priority:1/0})),"getUploadParameters"in e&&(eW(this,eQ)[eQ]=t.wrapPromiseFunction(e.getUploadParameters))}async getUploadId(e,t){let i;for(;null!=(i=eW(this,eY)[eY].get(e.data));)try{return await i}catch{}let r=eW(this,eK)[eK](eW(this,e8)[e8](e),t),s=()=>{r.abort(t.reason),eW(this,eY)[eY].delete(e.data)};return t.addEventListener("abort",s,{once:!0}),eW(this,eY)[eY].set(e.data,r),r.then(async i=>{t.removeEventListener("abort",s),eW(this,e3)[e3](e,i),eW(this,eY)[eY].set(e.data,i)},()=>{t.removeEventListener("abort",s),eW(this,eY)[eY].delete(e.data)}),r}async abortFileUpload(e){let t,i=eW(this,eY)[eY].get(e.data);if(null!=i){eW(this,eY)[eY].delete(e.data),eW(this,e3)[e3](e,Object.create(null));try{t=await i}catch{return}await eW(this,eV)[eV](eW(this,e8)[e8](e),t)}}async uploadFile(e,t,i){if(ez(i),1===t.length&&!t[0].shouldUseMultipart)return eW(this,e9)[e9](e,t[0],i);let{uploadId:r,key:s}=await this.getUploadId(e,i);ez(i);try{let o=await Promise.all(t.map((t,r)=>this.uploadChunk(e,r+1,t,i)));return ez(i),await eW(this,e4)[e4](eW(this,e8)[e8](e),{key:s,uploadId:r,parts:o,signal:i},i).abortOn(i)}catch(t){throw(null==t?void 0:t.cause)!==ep&&(null==t?void 0:t.name)!=="AbortError"&&this.abortFileUpload(e),t}}restoreUploadFile(e,t){eW(this,eY)[eY].set(e.data,t)}async resumeUploadFile(e,t,i){if(ez(i),1===t.length&&null!=t[0]&&!t[0].shouldUseMultipart)return eW(this,e9)[e9](e,t[0],i);let{uploadId:r,key:s}=await this.getUploadId(e,i);ez(i);let o=await eW(this,eZ)[eZ](eW(this,e8)[e8](e),{uploadId:r,key:s,signal:i},i).abortOn(i);ez(i);let a=await Promise.all(t.map((t,r)=>{let s=r+1,a=o.find(e=>{let{PartNumber:t}=e;return t===s});return null==a?this.uploadChunk(e,s,t,i):(null==t||null==t.setAsUploaded||t.setAsUploaded(),{PartNumber:s,ETag:a.ETag})}));return ez(i),eW(this,e4)[e4](eW(this,e8)[e8](e),{key:s,uploadId:r,parts:a,signal:i},i).abortOn(i)}async uploadChunk(e,t,i,r){ez(r);let{uploadId:s,key:o}=await this.getUploadId(e,r),a=eW(this,e2)[e2].values(),n=eW(this,e2)[e2].values(),l=()=>{let e=a.next();return null==e||e.done?null:e.value};for(;;){let a;ez(r);let u=i.getData(),{onProgress:h,onComplete:d}=i;try{a=await eW(this,eJ)[eJ](eW(this,e8)[e8](e),{uploadId:s,key:o,partNumber:t,body:u,signal:r}).abortOn(r)}catch(t){let e=l();if(null==e||r.aborted)throw t;await new Promise(t=>setTimeout(t,e));continue}ez(r);try{return{PartNumber:t,...await eW(this,e6)[e6]({signature:a,body:u,size:u.size,onProgress:h,onComplete:d,signal:r}).abortOn(r)}}catch(e){if(!await eW(this,e5)[e5](e,n))throw e}}}}async function te(e,t){var i;let r=eW(this,e1)[e1],s=null==e||null==(i=e.source)?void 0:i.status;if(null==s)return!1;if(403===s&&"Request has expired"===e.message){if(!r.isPaused){if(1===r.limit||null==eW(this,e0)[e0]){let e=t.next();if(null==e||e.done)return!1;eW(this,e0)[e0]=e.value}r.rateLimit(0),await new Promise(e=>setTimeout(e,eW(this,e0)[e0]))}}else if(429===s){if(!r.isPaused){let e=t.next();if(null==e||e.done)return!1;r.rateLimit(e.value)}}else if(s>400&&s<500&&409!==s)return!1;else if("undefined"!=typeof navigator&&!1===navigator.onLine)r.isPaused||(r.pause(),window.addEventListener("online",()=>{r.resume()},{once:!0}));else{let e=t.next();if(null==e||e.done)return!1;await new Promise(t=>setTimeout(t,e.value))}return!0}async function tt(e,t,i){var r;let s,{method:o="POST",url:a,fields:n,headers:l}=await eW(this,eQ)[eQ](eW(this,e8)[e8](e),{signal:i}).abortOn(i),u=t.getData();if("POST"===o.toUpperCase()){let e=new FormData;Object.entries(n).forEach(t=>{let[i,r]=t;return e.set(i,r)}),e.set("file",u),s=e}else s=u;let{onProgress:h,onComplete:d}=t,p=await eW(this,e6)[e6]({signature:{url:a,headers:l,method:o},body:s,size:u.size,onProgress:h,onComplete:d,signal:i}).abortOn(i),c=null==n?void 0:n.key;return eW(this,e3)[e3](e,{key:c}),{...p,location:null!=(r=p.location)?r:function(e){let t=new URL(e);return t.search="",t.hash="",t.href}(a),bucket:null==n?void 0:n.bucket,key:c}}function ti(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}var tr=0;function ts(e){return"__private_"+tr+++"_"+e}function to(e){if(null!=e&&e.error){let t=Error(e.message);throw Object.assign(t,e.error),t}return e}function ta(e){let t=e.Expiration;if(t){let e=Math.floor((new Date(t)-Date.now())/1e3);if(e>9)return e}}function tn(e){let{meta:t,allowedMetaFields:i,querify:r=!1}=e,s=null!=i?i:Object.keys(t);return t?Object.fromEntries(s.filter(e=>null!=t[e]).map(e=>[r?`metadata[${e}]`:e,String(t[e])])):{}}let tl={allowedMetaFields:!0,limit:6,getTemporarySecurityCredentials:!1,shouldUseMultipart:e=>(e.size||0)>0x6400000,retryDelays:[0,1e3,3e3,5e3]};var tu=ts("companionCommunicationQueue"),th=ts("client"),td=ts("setClient"),tp=ts("assertHost"),tc=ts("cachedTemporaryCredentials"),tf=ts("getTemporarySecurityCredentials"),tm=ts("setS3MultipartState"),tg=ts("getFile"),ty=ts("uploadLocalFile"),tv=ts("getCompanionClientArgs"),tb=ts("upload"),tw=ts("setCompanionHeaders"),tP=ts("setResumableUploadsCapability"),tS=ts("resetResumableCapability");class tO extends s{constructor(e,t){var i;super(e,{...tl,uploadPartBytes:tO.uploadPartBytes,createMultipartUpload:null,listParts:null,abortMultipartUpload:null,completeMultipartUpload:null,signPart:null,getUploadParameters:null,...t}),Object.defineProperty(this,tv,{value:tF}),Object.defineProperty(this,ty,{value:tk}),Object.defineProperty(this,tf,{value:tj}),Object.defineProperty(this,tp,{value:tE}),Object.defineProperty(this,td,{value:tx}),Object.defineProperty(this,tu,{writable:!0,value:void 0}),Object.defineProperty(this,th,{writable:!0,value:void 0}),Object.defineProperty(this,tc,{writable:!0,value:void 0}),Object.defineProperty(this,tm,{writable:!0,value:(e,t)=>{let{key:i,uploadId:r}=t,s=this.uppy.getFile(e.id);null!=s&&this.uppy.setFileState(e.id,{s3Multipart:{...s.s3Multipart,key:i,uploadId:r}})}}),Object.defineProperty(this,tg,{writable:!0,value:e=>this.uppy.getFile(e.id)||e}),Object.defineProperty(this,tb,{writable:!0,value:async e=>{if(0===e.length)return;let t=this.uppy.getFilesByIds(e).filter(e=>!("error"in e&&e.error)),i=t.filter(e=>{var t;return!(null!=(t=e.progress)&&t.uploadStarted)||!e.isRestored});this.uppy.emit("upload-start",i);let r=t.map(e=>{if(e.isRemote){ti(this,tP)[tP](!1);let t=new AbortController,i=i=>{i.id===e.id&&t.abort()};this.uppy.on("file-removed",i);let r=this.uppy.getRequestClientForFile(e).uploadRemoteFile(e,ti(this,tv)[tv](e),{signal:t.signal,getQueue:()=>this.requests});return this.requests.wrapSyncFunction(()=>{this.uppy.off("file-removed",i)},{priority:-1})(),r}return ti(this,ty)[ty](e)}),s=await Promise.allSettled(r);return ti(this,tP)[tP](!0),s}}),Object.defineProperty(this,tw,{writable:!0,value:()=>{var e;null==(e=ti(this,th)[th])||e.setCompanionHeaders(this.opts.headers)}}),Object.defineProperty(this,tP,{writable:!0,value:e=>{let{capabilities:t}=this.uppy.getState();this.uppy.setState({capabilities:{...t,resumableUploads:e}})}}),Object.defineProperty(this,tS,{writable:!0,value:()=>{ti(this,tP)[tP](!0)}}),this.type="uploader",this.id=this.opts.id||"AwsS3Multipart",ti(this,td)[td](t);let r={createMultipartUpload:this.createMultipartUpload,listParts:this.listParts,abortMultipartUpload:this.abortMultipartUpload,completeMultipartUpload:this.completeMultipartUpload,signPart:null!=t&&t.getTemporarySecurityCredentials?this.createSignedURL:this.signPart,getUploadParameters:null!=t&&t.getTemporarySecurityCredentials?this.createSignedURL:this.getUploadParameters};for(let e of Object.keys(r))null==this.opts[e]&&(this.opts[e]=r[e].bind(this));this.requests=null!=(i=this.opts.rateLimitedQueue)?i:new Q(this.opts.limit),ti(this,tu)[tu]=new e7(this.requests,this.opts,ti(this,tm)[tm],ti(this,tg)[tg]),this.uploaders=Object.create(null),this.uploaderEvents=Object.create(null)}[Symbol.for("uppy test: getClient")](){return ti(this,th)[th]}setOptions(e){ti(this,tu)[tu].setOptions(e),super.setOptions(e),ti(this,td)[td](e)}resetUploaderReferences(e,t){this.uploaders[e]&&(this.uploaders[e].abort({really:(null==t?void 0:t.abort)||!1}),this.uploaders[e]=null),this.uploaderEvents[e]&&(this.uploaderEvents[e].remove(),this.uploaderEvents[e]=null)}createMultipartUpload(e,t){ti(this,tp)[tp]("createMultipartUpload"),ez(t);let i=en(this.opts.allowedMetaFields,e.meta),r=tn({meta:e.meta,allowedMetaFields:i});return ti(this,th)[th].post("s3/multipart",{filename:e.name,type:e.type,metadata:r},{signal:t}).then(to)}listParts(e,t,i){let{key:r,uploadId:s,signal:o}=t;null!=o||(o=i),ti(this,tp)[tp]("listParts"),ez(o);let a=encodeURIComponent(r);return ti(this,th)[th].get(`s3/multipart/${encodeURIComponent(s)}?key=${a}`,{signal:o}).then(to)}completeMultipartUpload(e,t,i){let{key:r,uploadId:s,parts:o,signal:a}=t;null!=a||(a=i),ti(this,tp)[tp]("completeMultipartUpload"),ez(a);let n=encodeURIComponent(r),l=encodeURIComponent(s);return ti(this,th)[th].post(`s3/multipart/${l}/complete?key=${n}`,{parts:o.map(e=>{let{ETag:t,PartNumber:i}=e;return{ETag:t,PartNumber:i}})},{signal:a}).then(to)}async createSignedURL(e,t){let i=await ti(this,tf)[tf](t),r=ta(i.credentials)||604800,{uploadId:s,key:o,partNumber:a}=t;return{method:"PUT",expires:r,fields:{},url:`${await eB({accountKey:i.credentials.AccessKeyId,accountSecret:i.credentials.SecretAccessKey,sessionToken:i.credentials.SessionToken,expires:r,bucketName:i.bucket,Region:i.region,Key:null!=o?o:`${crypto.randomUUID()}-${e.name}`,uploadId:s,partNumber:a})}`,headers:{"Content-Type":e.type}}}signPart(e,t){let{uploadId:i,key:r,partNumber:s,signal:o}=t;if(ti(this,tp)[tp]("signPart"),ez(o),null==i||null==r||null==s)throw Error("Cannot sign without a key, an uploadId, and a partNumber");let a=encodeURIComponent(r);return ti(this,th)[th].get(`s3/multipart/${encodeURIComponent(i)}/${s}?key=${a}`,{signal:o}).then(to)}abortMultipartUpload(e,t){let{key:i,uploadId:r,signal:s}=t;ti(this,tp)[tp]("abortMultipartUpload");let o=encodeURIComponent(i),a=encodeURIComponent(r);return ti(this,th)[th].delete(`s3/multipart/${a}?key=${o}`,void 0,{signal:s}).then(to)}getUploadParameters(e,t){ti(this,tp)[tp]("getUploadParameters");let{meta:i}=e,{type:r,name:s}=i,o=new URLSearchParams({filename:s,type:r,...tn({meta:i,allowedMetaFields:en(this.opts.allowedMetaFields,e.meta),querify:!0})});return ti(this,th)[th].get(`s3/params?${o}`,t)}static async uploadPartBytes(e){let{signature:{url:t,expires:i,headers:r,method:s="PUT"},body:o,size:a=o.size,onProgress:n,onComplete:l,signal:u}=e;if(ez(u),null==t)throw Error("Cannot upload to an undefined URL");return new Promise((e,h)=>{let d=new XMLHttpRequest;function p(){d.abort()}function c(){null==u||u.removeEventListener("abort",p)}d.open(s,t,!0),r&&Object.keys(r).forEach(e=>{d.setRequestHeader(e,r[e])}),d.responseType="text","number"==typeof i&&(d.timeout=1e3*i),null==u||u.addEventListener("abort",p),d.upload.addEventListener("progress",e=>{n(e)}),d.addEventListener("abort",()=>{c(),h(ea())}),d.addEventListener("timeout",()=>{c();let e=Error("Request has expired");e.source={status:403},h(e)}),d.addEventListener("load",()=>{if(c(),403===d.status&&d.responseText.includes("<Message>Request has expired</Message>")){let e=Error("Request has expired");e.source=d,h(e);return}if(d.status<200||d.status>=300){let e=Error("Non 2xx");e.source=d,h(e);return}null==n||n({loaded:a,lengthComputable:!0});let t=d.getAllResponseHeaders().trim().split(/[\r\n]+/),i={__proto__:null};for(let e of t){let t=e.split(": "),r=t.shift(),s=t.join(": ");i[r]=s}let{etag:r,location:o}=i;if("POST"===s.toUpperCase()&&null==o&&console.error("@uppy/aws-s3: Could not read the Location header. This likely means CORS is not configured correctly on the S3 Bucket. See https://uppy.io/docs/aws-s3/#setting-up-your-s3-bucket"),null==r)return void console.error("@uppy/aws-s3: Could not read the ETag header. This likely means CORS is not configured correctly on the S3 Bucket. See https://uppy.io/docs/aws-s3/#setting-up-your-s3-bucket");null==l||l(r),e({...i,ETag:r})}),d.addEventListener("error",e=>{c();let t=Error("Unknown error");t.source=e.target,h(t)}),d.send(o)})}install(){ti(this,tP)[tP](!0),this.uppy.addPreProcessor(ti(this,tw)[tw]),this.uppy.addUploader(ti(this,tb)[tb]),this.uppy.on("cancel-all",ti(this,tS)[tS])}uninstall(){this.uppy.removePreProcessor(ti(this,tw)[tw]),this.uppy.removeUploader(ti(this,tb)[tb]),this.uppy.off("cancel-all",ti(this,tS)[tS])}}function tx(e){null!=e&&("endpoint"in e||"companionUrl"in e||"headers"in e||"companionHeaders"in e||"cookiesRule"in e||"companionCookiesRule"in e)&&("companionUrl"in e&&!("endpoint"in e)&&this.uppy.log("`companionUrl` option has been removed in @uppy/aws-s3, use `endpoint` instead.","warning"),"companionHeaders"in e&&!("headers"in e)&&this.uppy.log("`companionHeaders` option has been removed in @uppy/aws-s3, use `headers` instead.","warning"),"companionCookiesRule"in e&&!("cookiesRule"in e)&&this.uppy.log("`companionCookiesRule` option has been removed in @uppy/aws-s3, use `cookiesRule` instead.","warning"),"endpoint"in e?ti(this,th)[th]=new E(this.uppy,{pluginId:this.id,provider:"AWS",companionUrl:this.opts.endpoint,companionHeaders:this.opts.headers,companionCookiesRule:this.opts.cookiesRule}):("headers"in e&&ti(this,tw)[tw](),"cookiesRule"in e&&(ti(this,th)[th].opts.companionCookiesRule=e.cookiesRule)))}function tE(e){if(!ti(this,th)[th])throw Error(`Expected a \`endpoint\` option containing a URL, or if you are not using Companion, a custom \`${e}\` implementation.`)}async function tj(e){if(ez(null==e?void 0:e.signal),null==ti(this,tc)[tc]){let{getTemporarySecurityCredentials:t}=this.opts;!0===t?(ti(this,tp)[tp]("getTemporarySecurityCredentials"),ti(this,tc)[tc]=ti(this,th)[th].get("s3/sts",e).then(to)):ti(this,tc)[tc]=t(e),ti(this,tc)[tc]=await ti(this,tc)[tc],setTimeout(()=>{ti(this,tc)[tc]=null},500*(ta(ti(this,tc)[tc].credentials)||0))}return ti(this,tc)[tc]}function tk(e){var t=this;return new Promise((i,r)=>{let s=new eC(e.data,{companionComm:ti(this,tu)[tu],log:function(){return t.uppy.log(...arguments)},getChunkSize:this.opts.getChunkSize?this.opts.getChunkSize.bind(this):void 0,onProgress:(t,i)=>{var r;let s=this.uppy.getFile(e.id);this.uppy.emit("upload-progress",s,{uploadStarted:null!=(r=s.progress.uploadStarted)?r:0,bytesUploaded:t,bytesTotal:i})},onError:t=>{this.uppy.log(t),this.uppy.emit("upload-error",e,t),this.resetUploaderReferences(e.id),r(t)},onSuccess:t=>{let r={body:{...t},status:200,uploadURL:t.location};this.resetUploaderReferences(e.id),this.uppy.emit("upload-success",ti(this,tg)[tg](e),r),t.location&&this.uppy.log(`Download ${e.name} from ${t.location}`),i()},onPartComplete:t=>{this.uppy.emit("s3-multipart:part-uploaded",ti(this,tg)[tg](e),t)},file:e,shouldUseMultipart:this.opts.shouldUseMultipart,...e.s3Multipart});this.uploaders[e.id]=s;let o=new R(this.uppy);this.uploaderEvents[e.id]=o,o.onFileRemove(e.id,t=>{s.abort(),this.resetUploaderReferences(e.id,{abort:!0}),i(`upload ${t} was removed`)}),o.onCancelAll(e.id,()=>{s.abort(),this.resetUploaderReferences(e.id,{abort:!0}),i(`upload ${e.id} was canceled`)}),o.onFilePause(e.id,e=>{e?s.pause():s.start()}),o.onPauseAll(e.id,()=>{s.pause()}),o.onResumeAll(e.id,()=>{s.start()}),s.start()})}function tF(e){var t;return{...null==(t=e.remote)?void 0:t.body,protocol:"s3-multipart",size:e.data.size,metadata:e.meta}}tO.VERSION="4.2.3"},6586:(e,t,i)=>{"use strict";function r(e,t){if(!({}).hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}i.d(t,{A:()=>h});var s=0;function o(e){return"__private_"+s+++"_"+e}function a(e,t){let i=/\$/g,r=[e];if(null==t)return r;for(let e of Object.keys(t))if("_"!==e){let s=t[e];"string"==typeof s&&(s=i[Symbol.replace](s,"$$$$")),r=function(e,t,i){let r=[];return e.forEach(e=>"string"!=typeof e?r.push(e):t[Symbol.split](e).forEach((e,t,s)=>{""!==e&&r.push(e),t<s.length-1&&r.push(i)})),r}(r,RegExp(`%\\{${e}\\}`,"g"),s)}return r}let n=e=>{throw Error(`missing string: ${e}`)};var l=o("onMissingKey"),u=o("apply");class h{constructor(e,t){let{onMissingKey:i=n}=void 0===t?{}:t;Object.defineProperty(this,u,{value:d}),Object.defineProperty(this,l,{writable:!0,value:void 0}),this.locale={strings:{},pluralize:e=>+(1!==e)},Array.isArray(e)?e.forEach(r(this,u)[u],this):r(this,u)[u](e),r(this,l)[l]=i}translate(e,t){return this.translateArray(e,t).join("")}translateArray(e,t){let i=this.locale.strings[e];if(null==i&&(r(this,l)[l](e),i=e),"object"==typeof i){if(t&&void 0!==t.smart_count)return a(i[this.locale.pluralize(t.smart_count)],t);throw Error("Attempted to use a string with plural forms, but no value was given for %{smart_count}")}if("string"!=typeof i)throw Error("string was not a string");return a(i,t)}}function d(e){if(!(null!=e&&e.strings))return;let t=this.locale;Object.assign(this.locale,{strings:{...t.strings,...e.strings},pluralize:e.pluralize||t.pluralize})}},6685:(e,t,i)=>{var r=i(2500);e.exports=function(){return r.Date.now()}},6713:e=>{var t=/\s/;e.exports=function(e){for(var i=e.length;i--&&t.test(e.charAt(i)););return i}},6815:(e,t,i)=>{var r=i(4217),s=i(7460),o=i(771),a=0/0,n=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,u=/^0o[0-7]+$/i,h=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(o(e))return a;if(s(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=s(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=r(e);var i=l.test(e);return i||u.test(e)?h(e.slice(2),i?2:8):n.test(e)?a:+e}},7434:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7460:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},7489:(e,t,i)=>{"use strict";i.d(t,{b:()=>u});var r=i(2115),s=i(3655),o=i(5155),a="horizontal",n=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var i;let{decorative:r,orientation:l=a,...u}=e,h=(i=l,n.includes(i))?l:a;return(0,o.jsx)(s.sG.div,{"data-orientation":h,...r?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...u,ref:t})});l.displayName="Separator";var u=l},7985:(e,t,i)=>{e.exports="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g},8233:(e,t,i)=>{var r=i(4376),s=i(570),o=i(4439),a=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?s(e):o(e)}},8344:()=>{},8611:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},8792:(e,t,i)=>{var r=i(2354),s=/[\/\+\.]/;e.exports=function(e,t){function i(t){var i=r(t,e,s);return i&&i.length>=2}return t?i(t.split(";")[0]):i}},9022:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},9074:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9200:e=>{function t(e,t){"boolean"==typeof t&&(t={forever:t}),this._originalTimeouts=JSON.parse(JSON.stringify(e)),this._timeouts=e,this._options=t||{},this._maxRetryTime=t&&t.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}e.exports=t,t.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)},t.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null},t.prototype.retry=function(e){if(this._timeout&&clearTimeout(this._timeout),!e)return!1;var t=new Date().getTime();if(e&&t-this._operationStart>=this._maxRetryTime)return this._errors.push(e),this._errors.unshift(Error("RetryOperation timeout occurred")),!1;this._errors.push(e);var i=this._timeouts.shift();if(void 0===i)if(!this._cachedTimeouts)return!1;else this._errors.splice(0,this._errors.length-1),i=this._cachedTimeouts.slice(-1);var r=this;return this._timer=setTimeout(function(){r._attempts++,r._operationTimeoutCb&&(r._timeout=setTimeout(function(){r._operationTimeoutCb(r._attempts)},r._operationTimeout),r._options.unref&&r._timeout.unref()),r._fn(r._attempts)},i),this._options.unref&&this._timer.unref(),!0},t.prototype.attempt=function(e,t){this._fn=e,t&&(t.timeout&&(this._operationTimeout=t.timeout),t.cb&&(this._operationTimeoutCb=t.cb));var i=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){i._operationTimeoutCb()},i._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)},t.prototype.try=function(e){console.log("Using RetryOperation.try() is deprecated"),this.attempt(e)},t.prototype.start=function(e){console.log("Using RetryOperation.start() is deprecated"),this.attempt(e)},t.prototype.start=t.prototype.try,t.prototype.errors=function(){return this._errors},t.prototype.attempts=function(){return this._attempts},t.prototype.mainError=function(){if(0===this._errors.length)return null;for(var e={},t=null,i=0,r=0;r<this._errors.length;r++){var s=this._errors[r],o=s.message,a=(e[o]||0)+1;e[o]=a,a>=i&&(t=s,i=a)}return t}},9676:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},9869:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9890:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])}}]);