"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5580],{133:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},704:(e,t,r)=>{r.d(t,{B8:()=>I,UC:()=>P,bL:()=>N,l9:()=>E});var n=r(2115),l=r(5185),a=r(6081),o=r(9196),i=r(8905),s=r(3655),d=r(4315),u=r(5845),c=r(1285),p=r(5155),f="Tabs",[v,h]=(0,a.A)(f,[o.RG]),m=(0,o.RG)(),[w,g]=v(f),y=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:l,defaultValue:a,orientation:o="horizontal",dir:i,activationMode:v="automatic",...h}=e,m=(0,d.jH)(i),[g,y]=(0,u.i)({prop:n,onChange:l,defaultProp:null!=a?a:"",caller:f});return(0,p.jsx)(w,{scope:r,baseId:(0,c.B)(),value:g,onValueChange:y,orientation:o,dir:m,activationMode:v,children:(0,p.jsx)(s.sG.div,{dir:m,"data-orientation":o,...h,ref:t})})});y.displayName=f;var x="TabsList",b=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...l}=e,a=g(x,r),i=m(r);return(0,p.jsx)(o.bL,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...l,ref:t})})});b.displayName=x;var S="TabsTrigger",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,d=g(S,r),u=m(r),c=R(d.baseId,n),f=T(d.baseId,n),v=n===d.value;return(0,p.jsx)(o.q7,{asChild:!0,...u,focusable:!a,active:v,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":f,"data-state":v?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...i,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;v||a||!e||d.onValueChange(n)})})})});C.displayName=S;var j="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:l,forceMount:a,children:o,...d}=e,u=g(j,r),c=R(u.baseId,l),f=T(u.baseId,l),v=l===u.value,h=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:a||v,children:r=>{let{present:n}=r;return(0,p.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:f,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&o})}})});function R(e,t){return"".concat(e,"-trigger-").concat(t)}function T(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=j;var N=y,I=b,E=C,P=k},968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(2115),l=r(3655),a=r(5155),o=n.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},4229:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4884:(e,t,r)=>{r.d(t,{bL:()=>S,zi:()=>C});var n=r(2115),l=r(5185),a=r(6101),o=r(6081),i=r(5845),s=r(5503),d=r(1275),u=r(3655),c=r(5155),p="Switch",[f,v]=(0,o.A)(p),[h,m]=f(p),w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:o,checked:s,defaultChecked:d,required:f,disabled:v,value:m="on",onCheckedChange:w,form:g,...y}=e,[S,C]=n.useState(null),j=(0,a.s)(t,e=>C(e)),k=n.useRef(!1),R=!S||g||!!S.closest("form"),[T,N]=(0,i.i)({prop:s,defaultProp:null!=d&&d,onChange:w,caller:p});return(0,c.jsxs)(h,{scope:r,checked:T,disabled:v,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":f,"data-state":b(T),"data-disabled":v?"":void 0,disabled:v,value:m,...y,ref:j,onClick:(0,l.m)(e.onClick,e=>{N(e=>!e),R&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),R&&(0,c.jsx)(x,{control:S,bubbles:!k.current,name:o,value:m,checked:T,required:f,disabled:v,form:g,style:{transform:"translateX(-100%)"}})]})});w.displayName=p;var g="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,l=m(g,r);return(0,c.jsx)(u.sG.span,{"data-state":b(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:t})});y.displayName=g;var x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:l,checked:o,bubbles:i=!0,...u}=e,p=n.useRef(null),f=(0,a.s)(p,t),v=(0,s.Z)(o),h=(0,d.X)(l);return n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==o&&t){let r=new Event("click",{bubbles:i});t.call(e,o),e.dispatchEvent(r)}},[v,o,i]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...u,tabIndex:-1,ref:f,style:{...u.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var S=w,C=y},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(2115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8715:(e,t,r)=>{r.d(t,{UC:()=>eA,YJ:()=>eB,In:()=>eD,q7:()=>eV,VF:()=>eF,p4:()=>e_,JU:()=>eG,ZL:()=>eL,bL:()=>eE,wn:()=>eO,PP:()=>eK,wv:()=>ez,l9:()=>eP,WT:()=>eM,LM:()=>eH});var n=r(2115),l=r(7650);function a(e,[t,r]){return Math.min(r,Math.max(t,e))}var o=r(5185),i=r(7328),s=r(6101),d=r(6081),u=r(4315),c=r(9178),p=r(2293),f=r(7900),v=r(1285),h=r(5152),m=r(4378),w=r(3655),g=r(9708),y=r(9033),x=r(5845),b=r(2712),S=r(5503),C=r(5155),j=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.sG.span,{...e,ref:t,style:{...j,...e.style}})).displayName="VisuallyHidden";var k=r(8168),R=r(3795),T=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],I="Select",[E,P,M]=(0,i.N)(I),[D,L]=(0,d.A)(I,[M,h.Bk]),A=(0,h.Bk)(),[H,B]=D(I),[G,V]=D(I),_=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:a,onOpenChange:o,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,y=A(t),[b,S]=n.useState(null),[j,k]=n.useState(null),[R,T]=n.useState(!1),N=(0,u.jH)(c),[P,M]=(0,x.i)({prop:l,defaultProp:null!=a&&a,onChange:o,caller:I}),[D,L]=(0,x.i)({prop:i,defaultProp:s,onChange:d,caller:I}),B=n.useRef(null),V=!b||g||!!b.closest("form"),[_,F]=n.useState(new Set),K=Array.from(_).map(e=>e.props.value).join(";");return(0,C.jsx)(h.bL,{...y,children:(0,C.jsxs)(H,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:j,onValueNodeChange:k,valueNodeHasChildren:R,onValueNodeHasChildrenChange:T,contentId:(0,v.B)(),value:D,onValueChange:L,open:P,onOpenChange:M,dir:N,triggerPointerDownPosRef:B,disabled:m,children:[(0,C.jsx)(E.Provider,{scope:t,children:(0,C.jsx)(G,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,C.jsxs)(eR,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>L(e.target.value),disabled:m,form:g,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(_)]},K):null]})})};_.displayName=I;var F="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...a}=e,i=A(r),d=B(F,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=P(r),f=n.useRef("touch"),[v,m,g]=eN(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eI(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(h.Mz,{asChild:!0,...i,children:(0,C.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eT(d.value)?"":void 0,...a,ref:c,onClick:(0,o.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,o.m)(a.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,o.m)(a.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(y(),e.preventDefault())})})})});K.displayName=F;var O="SelectValue",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:a,placeholder:o="",...i}=e,d=B(O,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==a,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{u(c)},[u,c]),(0,C.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eT(d.value)?(0,C.jsx)(C.Fragment,{children:o}):a})});z.displayName=O;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var W=e=>(0,C.jsx)(m.Z,{asChild:!0,...e});W.displayName="SelectPortal";var q="SelectContent",Z=n.forwardRef((e,t)=>{let r=B(q,e.__scopeSelect),[a,o]=n.useState();return((0,b.N)(()=>{o(new DocumentFragment)},[]),r.open)?(0,C.jsx)(Q,{...e,ref:t}):a?l.createPortal((0,C.jsx)(X,{scope:e.__scopeSelect,children:(0,C.jsx)(E.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),a):null});Z.displayName=q;var[X,Y]=D(q),J=(0,g.TL)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...j}=e,T=B(q,r),[N,I]=n.useState(null),[E,M]=n.useState(null),D=(0,s.s)(t,e=>I(e)),[L,A]=n.useState(null),[H,G]=n.useState(null),V=P(r),[_,F]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(N)return(0,k.Eq)(N)},[N]),(0,p.Oh)();let O=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&E&&(E.scrollTop=0),r===n&&E&&(E.scrollTop=E.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[V,E]),z=n.useCallback(()=>O([L,N]),[O,L,N]);n.useEffect(()=>{_&&z()},[_,z]);let{onOpenChange:U,triggerPointerDownPosRef:W}=T;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,a;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=W.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(a=null==(n=W.current)?void 0:n.y)?a:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,U,W]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Z,Y]=eN(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eI(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==T.value&&T.value===t||n)&&(A(e),n&&(K.current=!0))},[T.value]),et=n.useCallback(()=>null==N?void 0:N.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==T.value&&T.value===t||n)&&G(e)},[T.value]),en="popper"===l?ee:$,el=en===ee?{side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(X,{scope:r,content:N,viewport:E,onViewportChange:M,itemRefCallback:Q,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:z,selectedItemText:H,position:l,isPositioned:_,searchRef:Z,children:(0,C.jsx)(R.A,{as:J,allowPinchZoom:!0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,o.m)(a,e=>{var t;null==(t=T.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>F(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,o.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>O(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...o}=e,i=B(q,r),d=Y(q,r),[u,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=P(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:S,focusSelectedItem:j}=d,k=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&x&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,o=r.left-l,i=e.left-o,s=e.width+i,d=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,o=window.innerWidth-r.right-l,i=window.innerWidth-e.right-o,s=e.width+i,d=Math.max(s,t.width),c=a(o,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let o=h(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*x.offsetHeight,g),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,T=x.offsetHeight/2,N=f+v+(x.offsetTop+T);if(N<=R){let e=o.length>0&&x===o[o.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-R,T+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=o.length>0&&x===o[0].ref.current;u.style.top="0px";let t=Math.max(R,f+y.offsetTop+(e?j:0)+T);u.style.height=t+(g-N)+"px",y.scrollTop=N-R+y.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=b+"px",u.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,p,y,x,S,i.dir,l]);(0,b.N)(()=>k(),[k]);let[R,T]=n.useState();(0,b.N)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(k(),null==j||j(),g.current=!1)},[k,j]);return(0,C.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,C.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,C.jsx)(w.sG.div,{...o,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...a}=e,o=A(r);return(0,C.jsx)(h.UC,{...o,...a,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...a}=e,i=Y(en,r),d=er(en,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(E.Slot,{scope:r,children:(0,C.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,o.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let a=l+e,o=Math.min(n,a),i=a-o;r.style.height=o+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var ea="SelectGroup",[eo,ei]=D(ea),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,C.jsx)(eo,{scope:r,id:l,children:(0,C.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});es.displayName=ea;var ed="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(ed,r);return(0,C.jsx)(w.sG.div,{id:l.id,...n,ref:t})});eu.displayName=ed;var ec="SelectItem",[ep,ef]=D(ec),ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:a=!1,textValue:i,...d}=e,u=B(ec,r),c=Y(ec,r),p=u.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,a)}),x=(0,v.B)(),b=n.useRef("touch"),S=()=>{a||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ep,{scope:r,value:l,disabled:a,textId:x,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,C.jsx)(E.ItemSlot,{scope:r,value:l,disabled:a,textValue:f,children:(0,C.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...d,ref:y,onFocus:(0,o.m)(d.onFocus,()=>g(!0)),onBlur:(0,o.m)(d.onBlur,()=>g(!1)),onClick:(0,o.m)(d.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,o.m)(d.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,o.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,o.m)(d.onPointerMove,e=>{if(b.current=e.pointerType,a){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,o.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,o.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ev.displayName=ec;var eh="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:o,...i}=e,d=B(eh,r),u=Y(eh,r),c=ef(eh,r),p=V(eh,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,C.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(y(g),()=>x(g)),[y,x,g]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});em.displayName=eh;var ew="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(ew,r).isSelected?(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=ew;var ey="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=Y(ey,e.__scopeSelect),l=er(ey,e.__scopeSelect),[a,o]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){o(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=ey;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=Y(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[a,o]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;o(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,C.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...a}=e,i=Y("SelectScrollButton",r),s=n.useRef(null),d=P(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,o.m)(a.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,o.m)(a.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,o.m)(a.onPointerLeave,()=>{u()})})}),ej=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})});ej.displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),a=B(ek,r),o=Y(ek,r);return a.open&&"popper"===o.position?(0,C.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=ek;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...a}=e,o=n.useRef(null),i=(0,s.s)(t,o),d=(0,S.Z)(l);return n.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[d,l]),(0,C.jsx)(w.sG.select,{...a,style:{...j,...a.style},ref:i,defaultValue:l})});function eT(e){return""===e||void 0===e}function eN(e){let t=(0,y.c)(e),r=n.useRef(""),l=n.useRef(0),a=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),o=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,a,o]}function eI(e,t,r){var n,l;let a=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=r?e.indexOf(r):-1,i=(n=e,l=Math.max(o,0),n.map((e,t)=>n[(l+t)%n.length]));1===a.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return s!==r?s:void 0}eR.displayName="SelectBubbleInput";var eE=_,eP=K,eM=z,eD=U,eL=W,eA=Z,eH=el,eB=es,eG=eu,eV=ev,e_=em,eF=eg,eK=ex,eO=eS,ez=ej}}]);