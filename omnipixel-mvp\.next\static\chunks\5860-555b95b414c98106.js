"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5860],{4574:(e,s,a)=>{a.d(s,{T:()=>N});var t=a(5155),l=a(2115),r=a(7168),i=a(8482),n=a(8145),c=a(9840),o=a(646),d=a(9869),u=a(4186),m=a(5690),x=a(9022),p=a(5339),h=a(9676),g=a(9074),f=a(5880),j=a(1788),v=a(1154),y=a(2525),b=a(1539);function N(e){let{projectId:s,builds:a,onBuildRevert:N,onBuildDelete:w,onBuildActivate:A,onRefresh:k,isAdmin:S=!1,className:U=""}=e,[_,z]=(0,l.useState)(null),[F,E]=(0,l.useState)(null),[P,C]=(0,l.useState)(null),[T,M]=(0,l.useState)(null),B=e=>{switch(e){case"active":return(0,t.jsx)(o.A,{className:"h-4 w-4 text-green-500"});case"uploading":return(0,t.jsx)(d.A,{className:"h-4 w-4 text-blue-500"});case"processing":return(0,t.jsx)(u.A,{className:"h-4 w-4 text-yellow-500"});case"inactive":return(0,t.jsx)(m.A,{className:"h-4 w-4 text-gray-500"});case"archived":return(0,t.jsx)(x.A,{className:"h-4 w-4 text-gray-500"});case"failed":return(0,t.jsx)(p.A,{className:"h-4 w-4 text-red-500"});default:return(0,t.jsx)(u.A,{className:"h-4 w-4 text-gray-500"})}},D=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"uploading":return"bg-blue-100 text-blue-800";case"processing":return"bg-yellow-100 text-yellow-800";case"inactive":return"bg-gray-100 text-gray-600";case"archived":default:return"bg-gray-100 text-gray-800";case"failed":return"bg-red-100 text-red-800"}},O=e=>{if(!e)return"Unknown size";let s=Math.floor(Math.log(e)/Math.log(1024));return Math.round(e/Math.pow(1024,s)*100)/100+" "+["Bytes","KB","MB","GB"][s]},R=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),Z=async e=>{if(w&&confirm("Are you sure you want to delete version ".concat(e.version," (").concat(e.original_filename||e.filename,")? This will permanently remove the ZIP file from storage and cannot be undone.")))try{E(e.id),await w(e.id),null==k||k()}catch(e){console.error("Error deleting build:",e),alert("Failed to delete build. Please try again.")}finally{E(null)}},$=async e=>{if(A&&!e.is_current&&confirm("Are you sure you want to activate version ".concat(e.version," (").concat(e.original_filename||e.filename,")? This will make it the current active build and upload it to StreamPixel.")))try{C(e.id),await A(e.id),null==k||k()}catch(e){console.error("Error activating build:",e),alert("Failed to activate build. Please try again.")}finally{C(null)}},L=a.find(e=>e.is_current),W=a.filter(e=>!e.is_current).slice(0,10);return(0,t.jsxs)(i.Zp,{className:U,children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),(0,t.jsx)(i.ZB,{children:"Builds"}),(0,t.jsxs)(n.E,{variant:"secondary",children:[a.length," total"]})]}),k&&(0,t.jsx)(r.$,{size:"sm",variant:"outline",onClick:k,children:"Refresh"})]}),(0,t.jsxs)(i.BT,{children:["View and manage build versions. Each project can have up to 2 active builds.",!S&&" You can revert to any previous version."]})]}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[L&&(0,t.jsx)("div",{children:(0,t.jsx)("div",{className:"border border-green-200 rounded-lg p-4 bg-green-50",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("h5",{className:"font-medium text-gray-900",children:L.original_filename||L.filename}),(0,t.jsxs)(n.E,{className:D(L.status),children:["Version ",L.version]}),(0,t.jsx)(n.E,{variant:"outline",className:"bg-green-100 text-green-800",children:"Current"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-1"}),R(L.created_at)]}),L.file_size&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-1"}),O(L.file_size)]}),(0,t.jsxs)("div",{className:"flex items-center",children:[B(L.status),(0,t.jsx)("span",{className:"ml-1 capitalize",children:L.status})]})]}),L.streampixel_status&&(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)(n.E,{variant:"outline",className:"text-xs ".concat("uploaded"===L.streampixel_status?"bg-green-50 text-green-700 border-green-200":"validation_failed"===L.streampixel_status?"bg-yellow-50 text-yellow-700 border-yellow-200":"failed"===L.streampixel_status?"bg-red-50 text-red-700 border-red-200":"")})})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(c.lG,{children:[(0,t.jsx)(c.zM,{asChild:!0,children:(0,t.jsx)(r.$,{size:"sm",variant:"outline",onClick:()=>M(L),children:"View Details"})}),(0,t.jsxs)(c.Cf,{children:[(0,t.jsxs)(c.c7,{children:[(0,t.jsxs)(c.L3,{children:["Build Details - Version ",L.version]}),(0,t.jsx)(c.rr,{children:"Detailed information about this build"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Filename"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:L.original_filename||L.filename})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Status"}),(0,t.jsxs)("div",{className:"flex items-center mt-1",children:[B(L.status),(0,t.jsx)("span",{className:"ml-2 text-sm capitalize",children:L.status})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Upload Date"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:R(L.created_at)})]}),L.file_size&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"File Size"}),(0,t.jsx)("p",{className:"text-sm text-gray-900",children:O(L.file_size)})]}),L.streampixel_build_id&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"StreamPixel Build ID"}),(0,t.jsx)("p",{className:"text-sm text-gray-900 font-mono",children:L.streampixel_build_id})]}),L.error_message&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-red-700",children:"Error Message"}),(0,t.jsx)("p",{className:"text-sm text-red-900 bg-red-50 p-2 rounded",children:L.error_message})]})]})]})]}),(0,t.jsx)(r.$,{size:"sm",variant:"outline",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})}),w&&(0,t.jsx)(r.$,{size:"sm",variant:"destructive",onClick:()=>Z(L),disabled:F===L.id,title:"Delete build",children:F===L.id?(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(y.A,{className:"h-4 w-4"})})]})]})})}),(0,t.jsx)("div",{children:(0,t.jsx)("div",{className:"space-y-3",children:W.map((e,s)=>(0,t.jsx)("div",{className:"border rounded-lg p-4 hover:bg-gray-50",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("h5",{className:"font-medium text-gray-900",children:e.original_filename||e.filename}),(0,t.jsxs)(n.E,{variant:"outline",children:["Version ",e.version]}),(0,t.jsx)(n.E,{className:D(e.status),children:e.status}),e.streampixel_status&&(0,t.jsx)(n.E,{variant:"outline",className:"text-xs ".concat("uploaded"===e.streampixel_status?"bg-green-50 text-green-700 border-green-200":"validation_failed"===e.streampixel_status?"bg-yellow-50 text-yellow-700 border-yellow-200":"failed"===e.streampixel_status?"bg-red-50 text-red-700 border-red-200":"")})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-1"}),R(e.created_at)]}),e.file_size&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-1"}),O(e.file_size)]})]}),e.error_message&&(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:e.error_message})})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[("archived"===e.status||"inactive"===e.status)&&A&&(0,t.jsxs)(r.$,{size:"sm",variant:"default",onClick:()=>$(e),disabled:P===e.id,children:[P===e.id?(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(b.A,{className:"h-4 w-4"}),P===e.id?"Activating...":"Activate"]}),(0,t.jsx)(r.$,{size:"sm",variant:"outline",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})}),w&&(0,t.jsx)(r.$,{size:"sm",variant:"destructive",onClick:()=>Z(e),disabled:F===e.id,title:"Delete build",children:F===e.id?(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(y.A,{className:"h-4 w-4"})})]})]})},e.id||"build-".concat(s,"-").concat(e.original_filename||e.filename,"-").concat(e.version)))})}),0===a.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(h.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No builds yet"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Upload your first build to get started."})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(p.A,{className:"h-5 w-5 text-blue-500"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-blue-900",children:"Build Management"}),(0,t.jsxs)("div",{className:"mt-1 text-sm text-blue-700",children:[(0,t.jsxs)("p",{children:["• Each project can have ",(0,t.jsx)("strong",{children:"maximum 2 builds"})," (active, inactive, or archived)"]}),(0,t.jsxs)("p",{children:["• New builds are ",(0,t.jsx)("strong",{children:"inactive by default"})," unless auto-release is enabled"]}),(0,t.jsxs)("p",{children:["• Only ",(0,t.jsx)("strong",{children:"ZIP files"})," are accepted for game builds"]}),(0,t.jsxs)("p",{children:["• You must ",(0,t.jsx)("strong",{children:"delete a build"})," to upload a new one when limit is reached"]}),(0,t.jsx)("p",{children:"• Activate an inactive/archived build to make it current and upload to StreamPixel"}),(0,t.jsxs)("p",{children:["• Activated builds show as ",(0,t.jsx)("strong",{children:"processing"})," until StreamPixel confirms they are live"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"Any build can be deleted"})," - you have full control over your builds"]}),(0,t.jsx)("p",{children:"• Deleting a build permanently removes the ZIP file from storage"})]})]})]})})]})]})}},7611:(e,s,a)=>{a.d(s,{s:()=>z});var t=a(5155),l=a(2115),r=a(3893),i=a(6472),n=a(8482),c=a(7168),o=a(5863),d=a(3999);function u(e){let{className:s,value:a,...l}=e;return(0,t.jsx)(o.bL,{"data-slot":"progress",className:(0,d.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...l,children:(0,t.jsx)(o.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}var m=a(8145);let x=(0,a(2085).F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function p(e){let{className:s,variant:a,...l}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,d.cn)(x({variant:a}),s),...l})}function h(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,d.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",s),...a})}var g=a(7489);function f(e){let{className:s,orientation:a="horizontal",decorative:l=!0,...r}=e;return(0,t.jsx)(g.b,{"data-slot":"separator",decorative:l,orientation:a,className:(0,d.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...r})}var j=a(9869),v=a(9890),y=a(7434),b=a(4416),N=a(1539),w=a(4186),A=a(5690),k=a(2178),S=a(646),U=a(5339);async function _(){if("undefined"==typeof navigator||!("serviceWorker"in navigator))return void console.log("Service workers not supported");try{let e=await navigator.serviceWorker.getRegistrations();for(let s of(console.log("Found ".concat(e.length," service worker registrations")),e))console.log("Unregistering service worker:",s.scope),await s.unregister();console.log("All service workers unregistered")}catch(e){console.error("Failed to unregister service workers:",e)}}function z(e){let{projectId:s,onUploadComplete:a,onUploadError:o,className:x}=e,g=(0,l.useRef)(null);(0,l.useRef)(null);let z=(0,l.useRef)(null),[F,E]=(0,l.useState)(!1),[P,C]=(0,l.useState)(0),[T,M]=(0,l.useState)(0),[B,D]=(0,l.useState)(0),[O,R]=(0,l.useState)(!1),[Z,$]=(0,l.useState)(null),[L,W]=(0,l.useState)(null),[J,G]=(0,l.useState)(null),[I,V]=(0,l.useState)(!1);(0,l.useEffect)(()=>{_().catch(console.warn);let e=new r.A({id:"omnipixel-uploader",autoProceed:!1,allowMultipleUploads:!1,restrictions:{maxFileSize:0x600000000,maxNumberOfFiles:1,allowedFileTypes:[".zip","application/zip","application/x-zip-compressed"]},meta:{projectId:s}});return e.use(i.A,{shouldUseMultipart:e=>(e.size||0)>0x6400000,limit:4,retryDelays:[0,1e3,3e3,5e3],getUploadParameters:async e=>{try{let a=await fetch("/api/upload/uppy/single-params",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filename:e.name,type:e.type,projectId:s})});if(!a.ok)throw Error("Failed to get upload parameters");return await a.json()}catch(e){throw console.error("Error getting upload parameters:",e),e}},createMultipartUpload:async e=>{try{let a=await fetch("/api/upload/uppy/multipart-params",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filename:e.name,type:e.type,projectId:s,fileSize:e.size})});if(!a.ok)throw Error("Failed to create multipart upload");return await a.json()}catch(e){throw console.error("Error creating multipart upload:",e),e}},signPart:async(e,s)=>{let{uploadId:a,key:t,partNumber:l}=s;try{let e=await fetch("/api/upload/uppy/sign-part",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({key:t,uploadId:a,partNumber:l})});if(!e.ok)throw Error("Failed to sign part");return await e.json()}catch(e){throw console.error("Error signing part:",e),e}},completeMultipartUpload:async(e,s)=>{let{uploadId:a,key:t,parts:l}=s;try{let e=await fetch("/api/upload/uppy/complete-multipart",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({key:t,uploadId:a,parts:l})});if(!e.ok)throw Error("Failed to complete multipart upload");return await e.json()}catch(e){throw console.error("Error completing multipart upload:",e),e}},listParts:async(e,s)=>{let{uploadId:a,key:t}=s;try{let e=await fetch("/api/upload/uppy/list-parts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({key:t,uploadId:a})});if(!e.ok)throw Error("Failed to list parts");return(await e.json()).parts||[]}catch(e){return console.error("Error listing parts:",e),[]}},abortMultipartUpload:async(e,s)=>{let{uploadId:a,key:t}=s;try{await fetch("/api/upload/uppy/abort-multipart",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({key:t,uploadId:a})})}catch(e){console.error("Error aborting multipart upload:",e)}}}),e.on("file-added",e=>{console.log("File added to Uppy:",e.name,e),G(e),$(null),W(null)}),e.on("file-removed",e=>{console.log("File removed from Uppy:",null==e?void 0:e.name),G(null),$(null),W(null)}),e.on("restriction-failed",(e,s)=>{console.error("File restriction failed:",null==e?void 0:e.name,s),$("File restriction failed: ".concat(s.message))}),e.on("error",e=>{console.error("Uppy error:",e),$("Upload error: ".concat(e.message))}),e.on("upload-error",(e,s,a)=>{console.error("Upload error for file:",null==e?void 0:e.name,s,a),$("Upload failed: ".concat(s.message))}),e.on("upload-start",()=>{console.log("Upload started"),E(!0),$(null),W(null)}),e.on("upload-progress",(e,s)=>{if(e&&s){C(Math.round(s.bytesUploaded/(s.bytesTotal||1)*100));let e=Date.now()-s.uploadStarted,a=s.bytesUploaded/(e/1e3),t=((s.bytesTotal||0)-s.bytesUploaded)/a;M(a),D(t)}}),e.on("upload-success",async(e,t)=>{console.log("Upload success:",null==e?void 0:e.name,t);try{var l,r;let i="";if(t.uploadURL){let e=new URL(t.uploadURL).pathname.substring(1);e.startsWith("omnipixel/")&&(e=e.substring(10)),i=e}else if(null==(l=t.body)?void 0:l.key){let e=String(t.body.key);e.startsWith("omnipixel/")&&(e=e.substring(10)),i=e}else if(null==(r=t.body)?void 0:r.location){let e=new URL(t.body.location).pathname.substring(1);e.startsWith("omnipixel/")&&(e=e.substring(10)),i=e}let n=await fetch("/api/upload/uppy/complete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filename:null==e?void 0:e.name,projectId:s,s3Key:i,fileSize:null==e?void 0:e.size})});if(!n.ok)throw Error("Failed to complete upload");let c=await n.json();console.log("Upload completion result:",c),W("Upload completed successfully: ".concat(null==e?void 0:e.name)),null==a||a(c)}catch(e){console.error("Error completing upload:",e),$("Upload completed but failed to register. Please try again."),null==o||o("Upload completed but failed to register. Please try again.")}}),e.on("upload-error",(e,s)=>{console.error("Upload error:",null==e?void 0:e.name,s),$("Upload failed: ".concat(s.message)),null==o||o(s.message)}),e.on("complete",e=>{console.log("Upload complete:",e),E(!1),C(0),M(0),D(0)}),e.on("cancel-all",()=>{(e.getFiles().length>0||F)&&console.log("Upload cancelled - this may be due to user action or an error"),E(!1),C(0),M(0),D(0)}),g.current=e,()=>{e.destroy()}},[s,a,o]);let K=(0,l.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),V(!0)},[]),Y=(0,l.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),V(!1)},[]),X=(0,l.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),V(!1);let a=Array.from(e.dataTransfer.files);if(a.length>0&&g.current)try{let e=a[0];g.current.getFiles().forEach(e=>{var s;null==(s=g.current)||s.removeFile(e.id)}),g.current.addFile({source:"drag-drop",name:e.name,type:e.type,data:e,meta:{projectId:s}})}catch(e){console.error("Error adding file:",e),$("Failed to add file. Please check the file type and size.")}},[s]),q=(0,l.useCallback)(()=>{var e;F||J||(console.log("Upload area clicked, opening file dialog"),null==(e=z.current)||e.click())},[F,J]),H=(0,l.useCallback)(e=>{let a=e.target.files;if(console.log("File input changed:",a),a&&a.length>0&&g.current)try{let e=a[0];console.log("Adding file to Uppy:",e.name,e.type,e.size),g.current.getFiles().forEach(e=>{var s;null==(s=g.current)||s.removeFile(e.id)}),g.current.addFile({source:"file-input",name:e.name,type:e.type,data:e,meta:{projectId:s}})}catch(e){console.error("Error adding file to Uppy:",e),$("Failed to add file. Please check the file type and size.")}e.target.value=""},[s]),Q=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB"][s]};return(0,t.jsx)("div",{className:x,children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Upload Game Build"})]}),(0,t.jsx)(n.BT,{children:"Upload your game build ZIP files with robust, pausable uploads supporting files up to 24GB"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:(0,d.cn)("relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",I?"border-primary bg-primary/5":"border-muted-foreground/25 hover:border-muted-foreground/50",J&&"border-green-500 bg-green-50"),onDragOver:K,onDragLeave:Y,onDrop:X,onClick:q,children:[(0,t.jsx)("input",{ref:z,type:"file",accept:".zip,application/zip,application/x-zip-compressed",onChange:H,className:"hidden"}),J?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center",children:(0,t.jsx)(v.A,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium text-green-700",children:"File Selected"}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:J.name}),(0,t.jsx)(m.E,{variant:"secondary",children:Q(J.size||0)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2 pt-2",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{g.current&&J&&(g.current.removeFile(J.id),G(null))},className:"flex items-center space-x-1",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Remove"})]}),!F&&(0,t.jsxs)(c.$,{onClick:()=>{g.current&&g.current.upload()},className:"flex items-center space-x-1",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Start Upload"})]})]})]})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center",children:(0,t.jsx)(j.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium",children:I?"Drop your file here":"Drag & drop your build file"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"or click to browse files"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Supports ZIP files up to 24GB"})]})]})]}),F&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n.ZB,{className:"text-base",children:["Uploading ",null==J?void 0:J.name]}),(0,t.jsx)(m.E,{variant:O?"secondary":"default",children:O?"Paused":"Uploading"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("span",{className:"font-medium",children:[P,"% complete"]}),(0,t.jsxs)("span",{className:"text-muted-foreground",children:[Q(T),"/s"]})]}),(0,t.jsx)(u,{value:P,className:"h-2"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-blue-500"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Speed:"}),(0,t.jsxs)("span",{className:"font-medium",children:[Q(T),"/s"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-orange-500"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"Time left:"}),(0,t.jsx)("span",{className:"font-medium",children:(e=>{if(!isFinite(e)||e<0)return"--";let s=Math.floor(e/3600),a=Math.floor(e%3600/60),t=Math.floor(e%60);return s>0?"".concat(s,"h ").concat(a,"m ").concat(t,"s"):a>0?"".concat(a,"m ").concat(t,"s"):"".concat(t,"s")})(B)})]})]}),(0,t.jsx)(f,{}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{g.current&&(O?(g.current.resumeAll(),R(!1)):(g.current.pauseAll(),R(!0)))},className:"flex items-center space-x-1",children:[O?(0,t.jsx)(A.A,{className:"h-4 w-4"}):(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:O?"Resume":"Pause"})]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>{g.current&&(g.current.cancelAll(),G(null))},className:"flex items-center space-x-1",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Cancel"})]})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Upload continues in background when tab is minimized"})]})]})]}),L&&(0,t.jsxs)(p,{className:"border-green-200 bg-green-50",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)(h,{className:"text-green-700",children:L})]}),Z&&(0,t.jsxs)(p,{variant:"destructive",children:[(0,t.jsx)(U.A,{className:"h-4 w-4"}),(0,t.jsx)(h,{children:Z})]})]})]})})}_(),a(8344)}}]);