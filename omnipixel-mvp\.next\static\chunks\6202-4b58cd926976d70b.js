"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6202],{430:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("lock-open",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]])},1154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2919:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3786:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5273:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},5339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>X,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(2115),o=r(5185),a=r(6101),l=r(6081),i=r(1285),d=r(5845),s=r(9178),c=r(7900),u=r(4378),p=r(8905),f=r(3655),y=r(2293),h=r(3795),g=r(8168),m=r(9708),v=r(5155),k="Dialog",[x,A]=(0,l.A)(k),[w,D]=x(k),b=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,d.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:k});return(0,v.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:s,children:r})};b.displayName=k;var j="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(j,r),i=(0,a.s)(t,l.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Z(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});C.displayName=j;var R="DialogPortal",[M,I]=x(R,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=D(R,t);return(0,v.jsx)(M,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(p.C,{present:r||l.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};N.displayName=R;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let r=I(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(O,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:n||a.open,children:(0,v.jsx)(F,{...o,ref:t})}):null});_.displayName=O;var E=(0,m.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(O,r);return(0,v.jsx)(h.A,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":Z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),q="DialogContent",P=n.forwardRef((e,t)=>{let r=I(q,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(q,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||a.open,children:a.modal?(0,v.jsx)(z,{...o,ref:t}):(0,v.jsx)(T,{...o,ref:t})})});P.displayName=q;var z=n.forwardRef((e,t)=>{let r=D(q,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,v.jsx)(V,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=D(q,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),V=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...d}=e,u=D(q,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,y.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,v.jsx)(s.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...d,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Y,{titleId:u.titleId}),(0,v.jsx)(Q,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(B,r);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});G.displayName=B;var H="DialogDescription",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(H,r);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});L.displayName=H;var W="DialogClose",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(W,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}S.displayName=W;var U="DialogTitleWarning",[J,K]=(0,l.q)(U,{contentName:q,titleName:B,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,r=K(U),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Q=e=>{let{contentRef:t,descriptionId:r}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},X=b,$=C,ee=N,et=_,er=P,en=G,eo=L,ea=S},5690:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},7918:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},8979:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},9621:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},9771:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]])}}]);