"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6227],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1275:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(2115),i=n(2712);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},4315:(e,t,n)=>{n.d(t,{jH:()=>o});var r=n(2115);n(5155);var i=r.createContext(void 0);function o(e){let t=r.useContext(i);return e||t||"ltr"}},5152:(e,t,n)=>{n.d(t,{Mz:()=>e1,i3:()=>e5,UC:()=>e2,bL:()=>e0,Bk:()=>eN});var r=n(2115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,s=Math.floor,f=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function h(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}let y=new Set(["top","bottom"]);function v(e){return y.has(h(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>c[e])}let x=["left","right"],b=["right","left"],A=["top","bottom"],R=["bottom","top"];function S(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function T(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function C(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,l=v(t),a=m(v(t)),s=g(a),f=h(t),u="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[s]/2-o[s]/2;switch(f){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(p(t)){case"start":r[a]-=y*(n&&u?-1:1);break;case"end":r[a]+=y*(n&&u?-1:1)}return r}let L=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),f=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:u,y:c}=E(f,r,s),d=r,h={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:v,reset:w}=await m({x:u,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:h,rects:f,platform:l,elements:{reference:e,floating:t}});u=null!=g?g:u,c=null!=y?y:c,h={...h,[o]:{...h[o],...v}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(f=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:u,y:c}=E(f,d,s)),n=-1)}return{x:u,y:c,placement:d,strategy:i,middlewareData:h}};async function O(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:s}=e,{boundary:f="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:h=!1,padding:p=0}=d(t,e),m=T(p),g=a[h?"floating"===c?"reference":"floating":c],y=C(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:f,rootBoundary:u,strategy:s})),v="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),x=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},b=C(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:s}):v);return{top:(y.top-b.top+m.top)/x.y,bottom:(b.bottom-y.bottom+m.bottom)/x.y,left:(y.left-b.left+m.left)/x.x,right:(b.right-y.right+m.right)/x.x}}function k(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return i.some(t=>e[t]>=0)}let F=new Set(["left","top"]);async function z(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=h(n),a=p(n),s="y"===v(n),f=F.has(l)?-1:1,u=o&&s?-1:1,c=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:y}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),s?{x:g*u,y:m*f}:{x:m*f,y:g*u}}function D(){return"undefined"!=typeof window}function M(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function H(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!D()&&(e instanceof Node||e instanceof j(e).Node)}function I(e){return!!D()&&(e instanceof Element||e instanceof j(e).Element)}function B(e){return!!D()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function W(e){return!!D()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}let V=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!V.has(i)}let _=new Set(["table","td","th"]),K=[":popover-open",":modal"];function X(e){return K.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],Y=["transform","translate","scale","rotate","perspective","filter"],U=["paint","layout","strict","content"];function $(e){let t=J(),n=I(e)?ee(e):e;return q.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||Y.some(e=>(n.willChange||"").includes(e))||U.some(e=>(n.contain||"").includes(e))}function J(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function Z(e){return Q.has(M(e))}function ee(e){return j(e).getComputedStyle(e)}function et(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===M(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||H(e);return W(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=en(t);return Z(n)?t.ownerDocument?t.ownerDocument.body:t.body:B(n)&&G(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=j(i);if(o){let e=ei(l);return t.concat(l,l.visualViewport||[],G(i)?i:[],e&&n?er(e):[])}return t.concat(i,er(i,[],n))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eo(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=B(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,s=a(n)!==o||a(r)!==l;return s&&(n=o,r=l),{width:n,height:r,$:s}}function el(e){return I(e)?e:e.contextElement}function ea(e){let t=el(e);if(!B(t))return f(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=eo(t),l=(o?a(n.width):n.width)/r,s=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let es=f(0);function ef(e){let t=j(e);return J()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:es}function eu(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=el(e),a=f(1);t&&(r?I(r)&&(a=ea(r)):a=ea(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===j(l))&&i)?ef(l):f(0),u=(o.left+s.x)/a.x,c=(o.top+s.y)/a.y,d=o.width/a.x,h=o.height/a.y;if(l){let e=j(l),t=r&&I(r)?j(r):r,n=e,i=ei(n);for(;i&&r&&t!==n;){let e=ea(i),t=i.getBoundingClientRect(),r=ee(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,h*=e.y,u+=o,c+=l,i=ei(n=j(i))}}return C({width:d,height:h,x:u,y:c})}function ec(e,t){let n=et(e).scrollLeft;return t?t.left+n:eu(H(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}let eh=new Set(["absolute","fixed"]);function ep(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=j(e),r=H(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,s=0;if(i){o=i.width,l=i.height;let e=J();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:o,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=H(e),n=et(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ec(e),s=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:s}}(H(e));else if(I(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=B(e)?ea(e):f(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=ef(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return C(r)}function em(e){return"static"===ee(e).position}function eg(e,t){if(!B(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return H(e)===n&&(n=n.ownerDocument.body),n}function ey(e,t){var n;let r=j(e);if(X(e))return r;if(!B(e)){let t=en(e);for(;t&&!Z(t);){if(I(t)&&!em(t))return t;t=en(t)}return r}let i=eg(e,t);for(;i&&(n=i,_.has(M(n)))&&em(i);)i=eg(i,t);return i&&Z(i)&&em(i)&&!$(i)?r:i||function(e){let t=en(e);for(;B(t)&&!Z(t);){if($(t))return t;if(X(t))break;t=en(t)}return null}(e)||r}let ev=async function(e){let t=this.getOffsetParent||ey,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=B(t),i=H(t),o="fixed"===n,l=eu(e,!0,o,t),a={scrollLeft:0,scrollTop:0},s=f(0);if(r||!r&&!o)if(("body"!==M(t)||G(i))&&(a=et(t)),r){let e=eu(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=ec(i));o&&!r&&i&&(s.x=ec(i));let u=!i||r||o?f(0):ed(i,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=H(r),a=!!t&&X(t.floating);if(r===l||a&&o)return n;let s={scrollLeft:0,scrollTop:0},u=f(1),c=f(0),d=B(r);if((d||!d&&!o)&&(("body"!==M(r)||G(l))&&(s=et(r)),B(r))){let e=eu(r);u=ea(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let h=!l||d||o?f(0):ed(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+h.x,y:n.y*u.y-s.scrollTop*u.y+c.y+h.y}},getDocumentElement:H,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?X(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>I(e)&&"body"!==M(e)),i=null,o="fixed"===ee(e).position,l=o?en(e):e;for(;I(l)&&!Z(l);){let t=ee(l),n=$(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&eh.has(i.position)||G(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!I(r)||Z(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],f=a.reduce((e,n)=>{let r=ep(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ep(t,s,i));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}},getOffsetParent:ey,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eo(e);return{width:t,height:n}},getScale:ea,isElement:I,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:s,elements:f,middlewareData:u}=t,{element:c,padding:h=0}=d(e,t)||{};if(null==c)return{};let y=T(h),w={x:n,y:r},x=m(v(i)),b=g(x),A=await s.getDimensions(c),R="y"===x,S=R?"clientHeight":"clientWidth",C=a.reference[b]+a.reference[x]-w[x]-a.floating[b],E=w[x]-a.reference[x],L=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c)),O=L?L[S]:0;O&&await (null==s.isElement?void 0:s.isElement(L))||(O=f.floating[S]||a.floating[b]);let k=O/2-A[b]/2-1,P=o(y[R?"top":"left"],k),F=o(y[R?"bottom":"right"],k),z=O-A[b]-F,D=O/2-A[b]/2+(C/2-E/2),M=l(P,o(D,z)),j=!u.arrow&&null!=p(i)&&D!==M&&a.reference[b]/2-(D<P?P:F)-A[b]/2<0,H=j?D<P?D-P:D-z:0;return{[x]:w[x]+H,data:{[x]:M,centerOffset:D-M-H,...j&&{alignmentOffset:H}},reset:j}}});var eA=n(7650),eR="undefined"!=typeof document?r.useLayoutEffect:function(){};function eS(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eS(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eS(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eT(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eC(e,t){let n=eT(e);return Math.round(t*n)/n}function eE(e){let t=r.useRef(e);return eR(()=>{t.current=e}),t}var eL=n(3655),eO=n(5155),ek=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eO.jsx)(eL.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eO.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ek.displayName="Arrow";var eP=n(6101),eF=n(6081),ez=n(9033),eD=n(2712),eM=n(1275),ej="Popper",[eH,eN]=(0,eF.A)(ej),[eI,eB]=eH(ej),eW=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eO.jsx)(eI,{scope:t,anchor:i,onAnchorChange:o,children:n})};eW.displayName=ej;var eV="PopperAnchor",eG=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eB(eV,n),a=r.useRef(null),s=(0,eP.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eO.jsx)(eL.sG.div,{...o,ref:s})});eG.displayName=eV;var e_="PopperContent",[eK,eX]=eH(e_),eq=r.forwardRef((e,t)=>{var n,i,a,f,u,c,y,T;let{__scopePopper:C,side:E="bottom",sideOffset:D=0,align:M="center",alignOffset:j=0,arrowPadding:N=0,avoidCollisions:I=!0,collisionBoundary:B=[],collisionPadding:W=0,sticky:V="partial",hideWhenDetached:G=!1,updatePositionStrategy:_="optimized",onPlaced:K,...X}=e,q=eB(e_,C),[Y,U]=r.useState(null),$=(0,eP.s)(t,e=>U(e)),[J,Q]=r.useState(null),Z=(0,eM.X)(J),ee=null!=(y=null==Z?void 0:Z.width)?y:0,et=null!=(T=null==Z?void 0:Z.height)?T:0,en="number"==typeof W?W:{top:0,right:0,bottom:0,left:0,...W},ei=Array.isArray(B)?B:[B],eo=ei.length>0,ea={padding:en,boundary:ei.filter(eJ),altBoundary:eo},{refs:es,floatingStyles:ef,placement:ec,isPositioned:ed,middlewareData:eh}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:f,open:u}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=r.useState(i);eS(h,i)||p(i);let[m,g]=r.useState(null),[y,v]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),x=r.useCallback(e=>{e!==S.current&&(S.current=e,v(e))},[]),b=l||m,A=a||y,R=r.useRef(null),S=r.useRef(null),T=r.useRef(c),C=null!=f,E=eE(f),O=eE(o),k=eE(u),P=r.useCallback(()=>{if(!R.current||!S.current)return;let e={placement:t,strategy:n,middleware:h};O.current&&(e.platform=O.current),((e,t,n)=>{let r=new Map,i={platform:ew,...n},o={...i.platform,_c:r};return L(e,t,{...i,platform:o})})(R.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};F.current&&!eS(T.current,t)&&(T.current=t,eA.flushSync(()=>{d(t)}))})},[h,t,n,O,k]);eR(()=>{!1===u&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[u]);let F=r.useRef(!1);eR(()=>(F.current=!0,()=>{F.current=!1}),[]),eR(()=>{if(b&&(R.current=b),A&&(S.current=A),b&&A){if(E.current)return E.current(b,A,P);P()}},[b,A,P,E,C]);let z=r.useMemo(()=>({reference:R,floating:S,setReference:w,setFloating:x}),[w,x]),D=r.useMemo(()=>({reference:b,floating:A}),[b,A]),M=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eC(D.floating,c.x),r=eC(D.floating,c.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eT(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,D.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:P,refs:z,elements:D,floatingStyles:M}),[c,P,z,D,M])}({strategy:"fixed",placement:E+("center"!==M?"-"+M:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:f=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,h=el(e),p=a||f?[...h?er(h):[],...er(t)]:[];p.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),f&&e.addEventListener("resize",n)});let m=h&&c?function(e,t){let n,r=null,i=H(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function f(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:h,top:p,width:m,height:g}=d;if(u||t(),!m||!g)return;let y=s(p),v=s(i.clientWidth-(h+m)),w={rootMargin:-y+"px "+-v+"px "+-s(i.clientHeight-(p+g))+"px "+-s(h)+"px",threshold:l(0,o(1,c))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!x)return f();r?f(!1,r):n=setTimeout(()=>{f(!1,1e-7)},1e3)}1!==r||ex(d,e.getBoundingClientRect())||f(),x=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(h,n):null,g=-1,y=null;u&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===h&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),h&&!d&&y.observe(h),y.observe(t));let v=d?eu(e):null;return d&&function t(){let r=eu(e);v&&!ex(v,r)&&n(),v=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{a&&e.removeEventListener("scroll",n),f&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===_})},elements:{reference:q.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,s=await z(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+s.x,y:o+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}))({mainAxis:D+et,alignmentAxis:j}),I&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:f={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=d(e,t),c={x:n,y:r},p=await O(t,u),g=v(h(i)),y=m(g),w=c[y],x=c[g];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+p[e],r=w-p[t];w=l(n,o(w,r))}if(s){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+p[e],r=x-p[t];x=l(n,o(x,r))}let b=f.fn({...t,[y]:w,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[g]:s}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===V?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:f=!0}=d(e,t),u={x:n,y:r},c=v(i),p=m(c),g=u[p],y=u[c],w=d(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+x.mainAxis,n=o.reference[p]+o.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(f){var b,A;let e="y"===p?"width":"height",t=F.has(h(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:x.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(A=l.offset)?void 0:A[c])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[p]:g,[c]:y}}}}(e),options:[e,t]}))():void 0,...ea}),I&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:s,rects:f,initialPlacement:u,platform:c,elements:y}=t,{mainAxis:T=!0,crossAxis:C=!0,fallbackPlacements:E,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:P=!0,...F}=d(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let z=h(a),D=v(u),M=h(u)===u,j=await (null==c.isRTL?void 0:c.isRTL(y.floating)),H=E||(M||!P?[S(u)]:function(e){let t=S(e);return[w(e),t,w(t)]}(u)),N="none"!==k;!E&&N&&H.push(...function(e,t,n,r){let i=p(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?A:R;default:return[]}}(h(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(u,P,k,j));let I=[u,...H],B=await O(t,F),W=[],V=(null==(r=s.flip)?void 0:r.overflows)||[];if(T&&W.push(B[z]),C){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),i=m(v(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=S(l)),[l,S(l)]}(a,f,j);W.push(B[e[0]],B[e[1]])}if(V=[...V,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=I[e];if(t&&("alignment"!==C||D===v(t)||V.every(e=>e.overflows[0]>0&&v(e.placement)===D)))return{data:{index:e,overflows:V},reset:{placement:t}};let n=null==(o=V.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(L){case"bestFit":{let e=null==(l=V.filter(e=>{if(N){let t=v(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ea}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:s,rects:f,platform:u,elements:c}=t,{apply:m=()=>{},...g}=d(e,t),y=await O(t,g),w=h(s),x=p(s),b="y"===v(s),{width:A,height:R}=f.floating;"top"===w||"bottom"===w?(i=w,a=x===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=w,i="end"===x?"top":"bottom");let S=R-y.top-y.bottom,T=A-y.left-y.right,C=o(R-y[i],S),E=o(A-y[a],T),L=!t.middlewareData.shift,k=C,P=E;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=S),L&&!x){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?P=A-2*(0!==e||0!==t?e+t:l(y.left,y.right)):k=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await m({...t,availableWidth:P,availableHeight:k});let F=await u.getDimensions(c.floating);return A!==F.width||R!==F.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ea,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),J&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:J,padding:N}),eQ({arrowWidth:ee,arrowHeight:et}),G&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=k(await O(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=k(await O(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ea})]}),[ep,em]=eZ(ec),eg=(0,ez.c)(K);(0,eD.N)(()=>{ed&&(null==eg||eg())},[ed,eg]);let ey=null==(n=eh.arrow)?void 0:n.x,ev=null==(i=eh.arrow)?void 0:i.y,ek=(null==(a=eh.arrow)?void 0:a.centerOffset)!==0,[eF,ej]=r.useState();return(0,eD.N)(()=>{Y&&ej(window.getComputedStyle(Y).zIndex)},[Y]),(0,eO.jsx)("div",{ref:es.setFloating,"data-radix-popper-content-wrapper":"",style:{...ef,transform:ed?ef.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eF,"--radix-popper-transform-origin":[null==(f=eh.transformOrigin)?void 0:f.x,null==(u=eh.transformOrigin)?void 0:u.y].join(" "),...(null==(c=eh.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eO.jsx)(eK,{scope:C,placedSide:ep,onArrowChange:Q,arrowX:ey,arrowY:ev,shouldHideArrow:ek,children:(0,eO.jsx)(eL.sG.div,{"data-side":ep,"data-align":em,...X,ref:$,style:{...X.style,animation:ed?void 0:"none"}})})})});eq.displayName=e_;var eY="PopperArrow",eU={top:"bottom",right:"left",bottom:"top",left:"right"},e$=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eX(eY,n),o=eU[i.placedSide];return(0,eO.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eO.jsx)(ek,{...r,ref:t,style:{...r.style,display:"block"}})})});function eJ(e){return null!==e}e$.displayName=eY;var eQ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:s,middlewareData:f}=t,u=(null==(n=f.arrow)?void 0:n.centerOffset)!==0,c=u?0:e.arrowWidth,d=u?0:e.arrowHeight,[h,p]=eZ(a),m={start:"0%",center:"50%",end:"100%"}[p],g=(null!=(o=null==(r=f.arrow)?void 0:r.x)?o:0)+c/2,y=(null!=(l=null==(i=f.arrow)?void 0:i.y)?l:0)+d/2,v="",w="";return"bottom"===h?(v=u?m:"".concat(g,"px"),w="".concat(-d,"px")):"top"===h?(v=u?m:"".concat(g,"px"),w="".concat(s.floating.height+d,"px")):"right"===h?(v="".concat(-d,"px"),w=u?m:"".concat(y,"px")):"left"===h&&(v="".concat(s.floating.width+d,"px"),w=u?m:"".concat(y,"px")),{data:{x:v,y:w}}}});function eZ(e){let[t,n="center"]=e.split("-");return[t,n]}var e0=eW,e1=eG,e2=eq,e5=e$},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function i(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function o(e,t,n){var i=r(e,t,"set");if(i.set)i.set.call(e,n);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=n}return n}n.d(t,{N:()=>d});var l,a=n(2115),s=n(6081),f=n(6101),u=n(9708),c=n(5155);function d(e){let t=e+"CollectionProvider",[n,r]=(0,s.A)(t),[i,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),o=a.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:o,collectionRef:r,children:n})};l.displayName=t;let d=e+"CollectionSlot",h=(0,u.TL)(d),p=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=o(d,n),l=(0,f.s)(t,i.collectionRef);return(0,c.jsx)(h,{ref:l,children:r})});p.displayName=d;let m=e+"CollectionItemSlot",g="data-radix-collection-item",y=(0,u.TL)(m),v=a.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,l=a.useRef(null),s=(0,f.s)(t,l),u=o(m,n);return a.useEffect(()=>(u.itemMap.set(l,{ref:l,...i}),()=>void u.itemMap.delete(l))),(0,c.jsx)(y,{...{[g]:""},ref:s,children:r})});return v.displayName=m,[{Provider:l,Slot:p,ItemSlot:v},function(t){let n=o(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var h=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),i=r>=0?r:n+r;return i<0||i>=n?-1:i}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap,class e extends Map{set(e,t){return h.get(this)&&(this.has(e)?i(this,l)[i(this,l).indexOf(e)]=e:i(this,l).push(e)),super.set(e,t),this}insert(e,t,n){let r,o=this.has(t),a=i(this,l).length,s=m(e),f=s>=0?s:a+s,u=f<0||f>=a?-1:f;if(u===this.size||o&&u===this.size-1||-1===u)return this.set(t,n),this;let c=this.size+ +!o;s<0&&f++;let d=[...i(this,l)],h=!1;for(let e=f;e<c;e++)if(f===e){let i=d[e];d[e]===t&&(i=d[e+1]),o&&this.delete(t),r=this.get(i),this.set(t,n)}else{h||d[e-1]!==t||(h=!0);let n=d[h?e:e-1],i=r;r=this.get(n),this.delete(n),this.set(n,i)}return this}with(t,n,r){let i=new e(this);return i.insert(t,n,r),i}before(e){let t=i(this,l).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=i(this,l).indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=i(this,l).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=i(this,l).indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return o(this,l,[]),super.clear()}delete(e){let t=super.delete(e);return t&&i(this,l).splice(i(this,l).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=p(i(this,l),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=p(i(this,l),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return i(this,l).indexOf(e)}keyAt(e){return p(i(this,l),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],i=0;for(let e of this)Reflect.apply(t,n,[e,i,this])&&r.push(e),i++;return new e(r)}map(t,n){let r=[],i=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,i,this])]),i++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i]=t,o=0,l=null!=i?i:this.at(0);for(let e of this)l=0===o&&1===t.length?e:Reflect.apply(r,this,[l,e,o,this]),o++;return l}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i]=t,o=null!=i?i:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);o=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[o,n,e,this])}return o}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let i=[...this.entries()];return i.splice(...n),new e(i)}slice(t,n){let r=new e,i=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(i=n-1);for(let e=t;e<=i;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,l,{writable:!0,value:void 0}),o(this,l,[...super.keys()]),h.set(this,!0)}}},9196:(e,t,n)=>{n.d(t,{RG:()=>b,bL:()=>k,q7:()=>P});var r=n(2115),i=n(5185),o=n(7328),l=n(6101),a=n(6081),s=n(1285),f=n(3655),u=n(9033),c=n(5845),d=n(4315),h=n(5155),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[y,v,w]=(0,o.N)(g),[x,b]=(0,a.A)(g,[w]),[A,R]=x(g),S=r.forwardRef((e,t)=>(0,h.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(T,{...e,ref:t})})}));S.displayName=g;var T=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:o,loop:a=!1,dir:s,currentTabStopId:y,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:x,onEntryFocus:b,preventScrollOnEntryFocus:R=!1,...S}=e,T=r.useRef(null),C=(0,l.s)(t,T),E=(0,d.jH)(s),[L,k]=(0,c.i)({prop:y,defaultProp:null!=w?w:null,onChange:x,caller:g}),[P,F]=r.useState(!1),z=(0,u.c)(b),D=v(n),M=r.useRef(!1),[j,H]=r.useState(0);return r.useEffect(()=>{let e=T.current;if(e)return e.addEventListener(p,z),()=>e.removeEventListener(p,z)},[z]),(0,h.jsx)(A,{scope:n,orientation:o,dir:E,loop:a,currentTabStopId:L,onItemFocus:r.useCallback(e=>k(e),[k]),onItemShiftTab:r.useCallback(()=>F(!0),[]),onFocusableItemAdd:r.useCallback(()=>H(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>H(e=>e-1),[]),children:(0,h.jsx)(f.sG.div,{tabIndex:P||0===j?-1:0,"data-orientation":o,...S,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);O([e.find(e=>e.active),e.find(e=>e.id===L),...e].filter(Boolean).map(e=>e.ref.current),R)}}M.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>F(!1))})})}),C="RovingFocusGroupItem",E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:o=!0,active:l=!1,tabStopId:a,children:u,...c}=e,d=(0,s.B)(),p=a||d,m=R(C,n),g=m.currentTabStopId===p,w=v(n),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:A}=m;return r.useEffect(()=>{if(o)return x(),()=>b()},[o,x,b]),(0,h.jsx)(y.ItemSlot,{scope:n,id:p,focusable:o,active:l,children:(0,h.jsx)(f.sG.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let i=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return L[i]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>O(n))}}),children:"function"==typeof u?u({isCurrentTabStop:g,hasTabStop:null!=A}):u})})});E.displayName=C;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var k=S,P=E}}]);