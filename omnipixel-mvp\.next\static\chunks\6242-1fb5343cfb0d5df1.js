"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6242],{1089:(e,t,r)=>{r.d(t,{AuthProvider:()=>d,A:()=>o});var a=r(5155),n=r(2115);let s=(0,r(3865).createBrowserClient)("https://qrnstvofnizsgdlubtbt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU"),i=(0,n.createContext)(void 0);function d(e){let{children:t}=e,[r,d]=(0,n.useState)(null),[o,l]=(0,n.useState)(null),[c,u]=(0,n.useState)(null),[m,x]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{try{let{data:{user:e},error:t}=await s.auth.getUser();if(t){console.error("Error getting user:",t),d(null),u(null),x(!1);return}if(e){let{data:{session:t}}=await s.auth.getSession();d(e),u(t),await f(e.id)}else d(null),u(null),x(!1)}catch(e){console.error("Error in getInitialUser:",e),d(null),u(null),x(!1)}})();let{data:{subscription:e}}=s.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_OUT"!==e&&t)try{let{data:{user:e},error:r}=await s.auth.getUser();if(r||!e){d(null),u(null),l(null),x(!1);return}d(e),u(t),await f(e.id)}catch(e){console.error("Error verifying user:",e),d(null),u(null),l(null),x(!1)}else d(null),u(null),l(null),x(!1)});return()=>e.unsubscribe()},[]);let f=async e=>{try{let{data:t,error:r}=await s.from("profiles").select("*").eq("id",e).single();r?(console.error("Error fetching profile:",r),l(null)):l(t)}catch(e){console.error("Error fetching profile:",e),l(null)}finally{x(!1)}},v=async()=>{await s.auth.signOut()};return(0,a.jsx)(i.Provider,{value:{user:r,profile:o,session:c,loading:m,signOut:v},children:t})}function o(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1684:(e,t,r)=>{r.d(t,{V:()=>w});var a=r(5155),n=r(6874),s=r.n(n),i=r(5695),d=r(1089),o=r(7168);r(2115);var l=r(8698),c=r(3999);function u(e){let{...t}=e;return(0,a.jsx)(l.bL,{"data-slot":"dropdown-menu",...t})}function m(e){let{...t}=e;return(0,a.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...t})}function x(e){let{className:t,sideOffset:r=4,...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{"data-slot":"dropdown-menu-content",sideOffset:r,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function f(e){let{className:t,inset:r,variant:n="default",...s}=e;return(0,a.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":r,"data-variant":n,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function v(e){let{className:t,...r}=e;return(0,a.jsx)(l.wv,{"data-slot":"dropdown-menu-separator",className:(0,c.cn)("bg-border -mx-1 my-1 h-px",t),...r})}var h=r(5525),g=r(1007),p=r(381),b=r(4835);function w(){let{user:e,profile:t,signOut:r}=(0,d.A)(),n=(0,i.useRouter)(),l=async()=>{await r(),n.push("/login")};return e?(0,a.jsx)("nav",{className:"border-b bg-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(s(),{href:"/dashboard",className:"flex-shrink-0",children:(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Omnipixel"})}),(0,a.jsxs)("div",{className:"hidden md:ml-6 md:flex md:space-x-8",children:[(0,a.jsx)(s(),{href:"/dashboard",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium",children:"Dashboard"}),(null==t?void 0:t.role)==="platform_admin"&&(0,a.jsxs)(s(),{href:"/admin",className:"text-gray-900 hover:text-gray-700 px-3 py-2 text-sm font-medium flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"Admin"]})]})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(u,{children:[(0,a.jsx)(m,{asChild:!0,children:(0,a.jsx)(o.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(x,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsx)("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[(0,a.jsx)("p",{className:"font-medium",children:e.email}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:(null==t?void 0:t.role)==="platform_admin"?"Platform Admin":"User"})]})}),(0,a.jsx)(v,{}),(0,a.jsxs)(f,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]}),(null==t?void 0:t.role)==="platform_admin"&&(0,a.jsx)(f,{asChild:!0,children:(0,a.jsxs)(s(),{href:"/admin",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Admin Panel"})]})}),(0,a.jsx)(v,{}),(0,a.jsxs)(f,{onClick:l,children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Log out"})]})]})]})})]})})}):null}},3999:(e,t,r)=>{r.d(t,{cn:()=>s});var a=r(2596),n=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}},7168:(e,t,r)=>{r.d(t,{$:()=>o});var a=r(5155);r(2115);var n=r(9708),s=r(2085),i=r(3999);let d=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...l}=e,c=o?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:s,className:t})),...l})}},8482:(e,t,r)=>{r.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>s,aR:()=>i});var a=r(5155);r(2115);var n=r(3999);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}},9852:(e,t,r)=>{r.d(t,{p:()=>s});var a=r(5155);r(2115);var n=r(3999);function s(e){let{className:t,type:r,...s}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}}}]);