"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8167],{88:(e,s,a)=>{a.d(s,{d:()=>l});var t=a(5155),n=a(2115),r=a(4884),c=a(3999);let l=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(r.bL,{className:(0,c.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...n,ref:s,children:(0,t.jsx)(r.zi,{className:(0,c.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=r.bL.displayName},2714:(e,s,a)=>{a.d(s,{J:()=>c});var t=a(5155);a(2115);var n=a(968),r=a(3999);function c(e){let{className:s,...a}=e;return(0,t.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},8167:(e,s,a)=>{a.d(s,{g:()=>F});var t=a(5155),n=a(2115),r=a(7168),c=a(8482),l=a(9852),o=a(2714),i=a(88),d=a(8715),u=a(6474),m=a(7863),x=a(5196),h=a(3999);let p=d.bL;d.YJ;let g=d.WT,j=n.forwardRef((e,s)=>{let{className:a,children:n,...r}=e;return(0,t.jsxs)(d.l9,{ref:s,className:(0,h.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...r,children:[n,(0,t.jsx)(d.In,{asChild:!0,children:(0,t.jsx)(u.A,{className:"h-4 w-4 opacity-50"})})]})});j.displayName=d.l9.displayName;let f=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(d.PP,{ref:s,className:(0,h.cn)("flex cursor-default items-center justify-center py-1",a),...n,children:(0,t.jsx)(m.A,{className:"h-4 w-4"})})});f.displayName=d.PP.displayName;let y=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(d.wn,{ref:s,className:(0,h.cn)("flex cursor-default items-center justify-center py-1",a),...n,children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})});y.displayName=d.wn.displayName;let v=n.forwardRef((e,s)=>{let{className:a,children:n,position:r="popper",...c}=e;return(0,t.jsx)(d.ZL,{children:(0,t.jsxs)(d.UC,{ref:s,className:(0,h.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:r,...c,children:[(0,t.jsx)(f,{}),(0,t.jsx)(d.LM,{className:(0,h.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,t.jsx)(y,{})]})})});v.displayName=d.UC.displayName,n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(d.JU,{ref:s,className:(0,h.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...n})}).displayName=d.JU.displayName;let N=n.forwardRef((e,s)=>{let{className:a,children:n,...r}=e;return(0,t.jsxs)(d.q7,{ref:s,className:(0,h.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...r,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(d.VF,{children:(0,t.jsx)(x.A,{className:"h-4 w-4"})})}),(0,t.jsx)(d.p4,{children:n})]})});N.displayName=d.q7.displayName,n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(d.wv,{ref:s,className:(0,h.cn)("-mx-1 my-1 h-px bg-muted",a),...n})}).displayName=d.wv.displayName;var b=a(704);let w=b.bL,C=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(b.B8,{ref:s,className:(0,h.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...n})});C.displayName=b.B8.displayName;let M=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(b.l9,{ref:s,className:(0,h.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...n})});M.displayName=b.l9.displayName;let k=n.forwardRef((e,s)=>{let{className:a,...n}=e;return(0,t.jsx)(b.UC,{ref:s,className:(0,h.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...n})});k.displayName=b.UC.displayName;var P=a(381),S=a(133),R=a(1154),B=a(4229);function F(e){let{projectId:s,currentConfig:a,onConfigUpdate:d,isAdmin:u=!1,className:m=""}=e,[x,h]=(0,n.useState)(!1),[f,y]=(0,n.useState)(null),[b,F]=(0,n.useState)({autoConnect:!1,touchInput:!0,keyBoardInput:!0,resolutionMode:"Dynamic Resolution Mode",maxStreamQuality:"1080p (1920x1080)",primaryCodec:"H264",fallBackCodec:"VP8",isPasswordProtected:!1,password:"",loadingMessage:"Loading stream...",connectingMessage:"Connecting to stream...",disconnectedMessage:"Stream disconnected",reconnectingMessage:"Reconnecting...",errorMessage:"Stream error occurred",connectButtonText:"Connect to Stream",...a});(0,n.useEffect)(()=>{F({autoConnect:!1,touchInput:!0,keyBoardInput:!0,resolutionMode:"Dynamic Resolution Mode",maxStreamQuality:"1080p (1920x1080)",primaryCodec:"H264",fallBackCodec:"VP8",isPasswordProtected:!1,password:"",loadingMessage:"Loading stream...",connectingMessage:"Connecting to stream...",disconnectedMessage:"Stream disconnected",reconnectingMessage:"Reconnecting...",errorMessage:"Stream error occurred",connectButtonText:"Connect to Stream",...a})},[a]);let J=async()=>{try{h(!0),y(null);let e=u?{project_id:s,config:b}:{config:b},a=await fetch(u?"/api/admin/projects":"/api/projects/".concat(s),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to save configuration")}null==d||d(b),alert("Configuration saved successfully!")}catch(e){y(e.message),console.error("Error saving config:",e)}finally{h(!1)}},V=(e,s)=>{F(a=>({...a,[e]:s}))};return(0,t.jsxs)(c.Zp,{className:m,children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center",children:[(0,t.jsx)(P.A,{className:"h-5 w-5 mr-2"}),"Stream Configuration"]}),(0,t.jsx)(c.BT,{children:"Configure stream settings, security, and user interface messages"})]}),(0,t.jsxs)(c.Wu,{children:[f&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-600",children:f})}),(0,t.jsxs)(w,{defaultValue:"stream",className:"space-y-4",children:[(0,t.jsxs)(C,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(M,{value:"stream",children:"Stream Settings"}),(0,t.jsx)(M,{value:"security",children:"Security"}),(0,t.jsx)(M,{value:"messages",children:"Messages"})]}),(0,t.jsx)(k,{value:"stream",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"autoConnect",children:"Auto Connect"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.d,{id:"autoConnect",checked:b.autoConnect,onCheckedChange:e=>V("autoConnect",e)}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:b.autoConnect?"Automatically connect on load":"Show connect button"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"touchInput",children:"Touch Input"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.d,{id:"touchInput",checked:b.touchInput,onCheckedChange:e=>V("touchInput",e)}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Enable touch controls"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"keyBoardInput",children:"Keyboard Input"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.d,{id:"keyBoardInput",checked:b.keyBoardInput,onCheckedChange:e=>V("keyBoardInput",e)}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Enable keyboard controls"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"resolutionMode",children:"Resolution Mode"}),(0,t.jsxs)(p,{value:b.resolutionMode,onValueChange:e=>V("resolutionMode",e),children:[(0,t.jsx)(j,{children:(0,t.jsx)(g,{})}),(0,t.jsxs)(v,{children:[(0,t.jsx)(N,{value:"Dynamic Resolution Mode",children:"Dynamic Resolution"}),(0,t.jsx)(N,{value:"Fixed Resolution Mode",children:"Fixed Resolution"}),(0,t.jsx)(N,{value:"Adaptive Resolution Mode",children:"Adaptive Resolution"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"maxStreamQuality",children:"Max Stream Quality"}),(0,t.jsxs)(p,{value:b.maxStreamQuality,onValueChange:e=>V("maxStreamQuality",e),children:[(0,t.jsx)(j,{children:(0,t.jsx)(g,{})}),(0,t.jsxs)(v,{children:[(0,t.jsx)(N,{value:"4K (3840x2160)",children:"4K (3840x2160)"}),(0,t.jsx)(N,{value:"1440p (2560x1440)",children:"1440p (2560x1440)"}),(0,t.jsx)(N,{value:"1080p (1920x1080)",children:"1080p (1920x1080)"}),(0,t.jsx)(N,{value:"720p (1280x720)",children:"720p (1280x720)"}),(0,t.jsx)(N,{value:"480p (854x480)",children:"480p (854x480)"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"primaryCodec",children:"Primary Codec"}),(0,t.jsxs)(p,{value:b.primaryCodec,onValueChange:e=>V("primaryCodec",e),children:[(0,t.jsx)(j,{children:(0,t.jsx)(g,{})}),(0,t.jsxs)(v,{children:[(0,t.jsx)(N,{value:"H264",children:"H264"}),(0,t.jsx)(N,{value:"H265",children:"H265"}),(0,t.jsx)(N,{value:"VP8",children:"VP8"}),(0,t.jsx)(N,{value:"VP9",children:"VP9"}),(0,t.jsx)(N,{value:"AV1",children:"AV1"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"fallBackCodec",children:"Fallback Codec"}),(0,t.jsxs)(p,{value:b.fallBackCodec,onValueChange:e=>V("fallBackCodec",e),children:[(0,t.jsx)(j,{children:(0,t.jsx)(g,{})}),(0,t.jsxs)(v,{children:[(0,t.jsx)(N,{value:"H264",children:"H264"}),(0,t.jsx)(N,{value:"VP8",children:"VP8"}),(0,t.jsx)(N,{value:"VP9",children:"VP9"})]})]})]})]})}),(0,t.jsx)(k,{value:"security",className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"isPasswordProtected",children:"Password Protection"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.d,{id:"isPasswordProtected",checked:b.isPasswordProtected,onCheckedChange:e=>V("isPasswordProtected",e)}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:b.isPasswordProtected?"Stream is password protected":"Stream is public"})]})]}),b.isPasswordProtected&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"password",children:"Stream Password"}),(0,t.jsx)(l.p,{id:"password",type:"password",value:b.password,onChange:e=>V("password",e.target.value),placeholder:"Enter stream password"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Users will need this password to access the stream"})]})]})}),(0,t.jsx)(k,{value:"messages",className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"loadingMessage",children:"Loading Message"}),(0,t.jsx)(l.p,{id:"loadingMessage",value:b.loadingMessage,onChange:e=>V("loadingMessage",e.target.value),placeholder:"Loading stream..."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"connectingMessage",children:"Connecting Message"}),(0,t.jsx)(l.p,{id:"connectingMessage",value:b.connectingMessage,onChange:e=>V("connectingMessage",e.target.value),placeholder:"Connecting to stream..."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"disconnectedMessage",children:"Disconnected Message"}),(0,t.jsx)(l.p,{id:"disconnectedMessage",value:b.disconnectedMessage,onChange:e=>V("disconnectedMessage",e.target.value),placeholder:"Stream disconnected"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"reconnectingMessage",children:"Reconnecting Message"}),(0,t.jsx)(l.p,{id:"reconnectingMessage",value:b.reconnectingMessage,onChange:e=>V("reconnectingMessage",e.target.value),placeholder:"Reconnecting..."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"errorMessage",children:"Error Message"}),(0,t.jsx)(l.p,{id:"errorMessage",value:b.errorMessage,onChange:e=>V("errorMessage",e.target.value),placeholder:"Stream error occurred"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"connectButtonText",children:"Connect Button Text"}),(0,t.jsx)(l.p,{id:"connectButtonText",value:b.connectButtonText,onChange:e=>V("connectButtonText",e.target.value),placeholder:"Connect to Stream"})]})]})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t",children:[(0,t.jsxs)(r.$,{variant:"outline",onClick:()=>{F({autoConnect:!1,touchInput:!0,keyBoardInput:!0,resolutionMode:"Dynamic Resolution Mode",maxStreamQuality:"1080p (1920x1080)",primaryCodec:"H264",fallBackCodec:"VP8",isPasswordProtected:!1,password:"",loadingMessage:"Loading stream...",connectingMessage:"Connecting to stream...",disconnectedMessage:"Stream disconnected",reconnectingMessage:"Reconnecting...",errorMessage:"Stream error occurred",connectButtonText:"Connect to Stream",...a}),y(null)},disabled:x,children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Reset"]}),(0,t.jsx)(r.$,{onClick:J,disabled:x,children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Save Configuration"]})})]})]})]})}}}]);