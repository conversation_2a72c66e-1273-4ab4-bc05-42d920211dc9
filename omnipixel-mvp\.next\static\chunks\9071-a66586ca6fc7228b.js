"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9071],{8145:(e,t,n)=>{n.d(t,{E:()=>i});var s=n(5155);n(2115);var a=n(9708),r=n(2085),c=n(3999);let l=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:n,asChild:r=!1,...i}=e,o=r?a.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,c.cn)(l({variant:n}),t),...i})}},9071:(e,t,n)=>{n.d(t,{StreamPlayer:()=>N});var s=n(5155),a=n(2115),r=n(2476),c=n(7168),l=n(8482),i=n(8145),o=n(9852),d=n(9840),u=n(2919),m=n(430),x=n(1154),g=n(5339),h=n(5690),p=n(8979),v=n(9771),f=n(5273),b=n(7918),j=n(9621),w=n(3786);function N(e){var t,n,N,y,C,k;let{projectId:S,buildId:E,className:z="",config:A,showControls:M=!0,showHeader:R=!0,showEmbedButton:D=!0,width:P=1280,height:I=720,isEmbedded:$=!1,enableIframeComms:B=!1}=e,[L,_]=(0,a.useState)(!1),[F,W]=(0,a.useState)(!1),[q,O]=(0,a.useState)(!1),[T,U]=(0,a.useState)(null),[H,V]=(0,a.useState)(null),[Z,G]=(0,a.useState)(null),[Q,J]=(0,a.useState)(!1),[K,X]=(0,a.useState)(null),[Y,ee]=(0,a.useState)("disconnected"),[et,en]=(0,a.useState)("Loading stream..."),[es,ea]=(0,a.useState)(!1),[er,ec]=(0,a.useState)(!1),[el,ei]=(0,a.useState)(""),[eo,ed]=(0,a.useState)(!1),[eu,em]=(0,a.useState)(!1),ex=(0,a.useRef)(null),eg=(0,a.useRef)(null),eh=(0,a.useRef)(null),ep=(0,a.useRef)(null),ev=(0,a.useRef)(null),ef=(0,a.useRef)(!1),eb=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=async()=>{try{_(!0),X(null);let e=await fetch("/api/projects/".concat(S));if(!e.ok)throw Error("Failed to fetch project data");let t=await e.json(),n=t.project||t;G(n),U(n.stream_project_id);let s=A||n.config||{};if(V(s),!n.stream_project_id)throw Error("No StreamPixel ID found in project data")}catch(e){X("Failed to load project configuration")}finally{_(!1)}};S&&e()},[S,A]),(0,a.useEffect)(()=>()=>{eN()},[]),(0,a.useEffect)(()=>{(null==H?void 0:H.autoConnect)&&T&&!F&&!eb.current&&(console.log("\uD83D\uDE80 Auto-connecting stream...",{autoConnect:null==H?void 0:H.autoConnect,streamProjectId:T,isPlaying:F,isInitializing:eb.current}),ek())},[null==H?void 0:H.autoConnect,T,F]);let ej={loading:null!=(t=null==H?void 0:H.loadingMessage)?t:"Loading stream...",connecting:null!=(n=null==H?void 0:H.connectingMessage)?n:"Connecting to stream...",disconnected:null!=(N=null==H?void 0:H.disconnectedMessage)?N:"Stream disconnected",reconnecting:null!=(y=null==H?void 0:H.reconnectingMessage)?y:"Reconnecting...",error:null!=(C=null==H?void 0:H.errorMessage)?C:"Stream error occurred",connectButton:null!=(k=null==H?void 0:H.connectButtonText)?k:"Connect to Stream"},ew=async()=>{if(!eb.current){eb.current=!0,_(!0),X(null);try{var e,t,n,s,a,c;let l=T&&H?{AutoConnect:!0,appId:T,touchInput:null==(e=null==H?void 0:H.touchInput)||e,keyBoardInput:null==(t=null==H?void 0:H.keyBoardInput)||t,resolutionMode:null!=(n=null==H?void 0:H.resolutionMode)?n:"Dynamic Resolution Mode",maxStreamQuality:null!=(s=null==H?void 0:H.maxStreamQuality)?s:"1080p (1920x1080)",primaryCodec:null!=(a=null==H?void 0:H.primaryCodec)?a:"H264",fallBackCodec:null!=(c=null==H?void 0:H.fallBackCodec)?c:"VP8"}:null;if(!l)throw Error("Cannot create stream config: missing required data");let{appStream:i,pixelStreaming:o,UIControl:d}=await (0,r.ZN)(l);eh.current=i,ep.current=o,ev.current=d,eg.current&&i.rootElement&&(i.rootElement.parentNode&&i.rootElement.parentNode.removeChild(i.rootElement),eg.current.innerHTML="",eg.current.appendChild(i.rootElement)),eC(),ef.current=!0,_(!1)}catch(e){X(e.message||"Failed to initialize stream"),_(!1)}finally{eb.current=!1}}},eN=()=>{try{var e,t,n;(null==(t=eh.current)||null==(e=t.stream)?void 0:e.disconnect)&&eh.current.stream.disconnect(),(null==(n=ep.current)?void 0:n.disconnect)&&ep.current.disconnect(),eg.current&&(eg.current.innerHTML="")}catch(e){}eh.current=null,ep.current=null,ev.current=null,ef.current=!1,eb.current=!1},ey=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(B&&window.parent!==window)try{window.parent.postMessage({type:"omnipixel-".concat(e),projectId:S,buildId:E,timestamp:new Date().toISOString(),...t},"*")}catch(e){console.warn("Failed to send message to parent:",e)}};(0,a.useEffect)(()=>{if(!B)return;let e=e=>{var t,n;if(!(null==(n=e.data)||null==(t=n.type)?void 0:t.startsWith("omnipixel-")))return;let{type:s,data:a}=e.data;switch(console.log("\uD83D\uDCE8 Received message from parent:",s,a),s){case"omnipixel-connect":F||ek();break;case"omnipixel-disconnect":F&&eS();break;case"omnipixel-mute":O(!0);break;case"omnipixel-unmute":O(!1);break;case"omnipixel-fullscreen":J(!0);break;case"omnipixel-exit-fullscreen":J(!1);break;case"omnipixel-send-input":ep.current&&a.input&&("keyboard"===a.input.type||a.input.type)}};return window.addEventListener("message",e),()=>window.removeEventListener("message",e)},[B,F,S,E]),(0,a.useEffect)(()=>{B&&ey("status-change",{isLoading:L,isPlaying:F,streamStatus:Y,error:K,isMuted:q,isFullscreen:Q})},[B,L,F,Y,K,q,Q]);let eC=()=>{eh.current&&(eh.current.onConnectAction=()=>{var e;console.log("\uD83D\uDD17 StreamPixel: Connect action triggered"),ee("connecting"),_(!0),ea(!1),en(null!=(e=null==H?void 0:H.connectingMessage)?e:"Connecting to stream..."),ey("connect-started")},eh.current.onWebRtcConnecting=()=>{var e;console.log("\uD83C\uDF10 StreamPixel: WebRTC connecting"),ee("connecting"),en(null!=(e=null==H?void 0:H.connectingMessage)?e:"Establishing connection..."),ey("webrtc-connecting")},eh.current.onWebRtcConnected=()=>{console.log("✅ StreamPixel: WebRTC connected"),ee("connected"),en("Initializing video stream..."),ey("webrtc-connected")},eh.current.onVideoInitialized=()=>{console.log("\uD83C\uDFA5 StreamPixel: Video initialized and ready"),console.log("\uD83D\uDD04 Setting loading state to false - video ready"),_(!1),ea(!0),ee("connected"),W(!0),ey("video-initialized")},eh.current.onDisconnect=()=>{var e;console.log("\uD83D\uDD0C StreamPixel: Disconnected"),W(!1),_(!1),ea(!1),ee("disconnected"),en(null!=(e=null==H?void 0:H.disconnectedMessage)?e:"Stream disconnected"),ey("disconnected"),requestAnimationFrame(()=>{eN()})},eh.current.onDataChannelOpen&&(eh.current.onDataChannelOpen=()=>{console.log("\uD83D\uDCE1 StreamPixel: Data channel opened"),ey("data-channel-open")}),eh.current.onDataChannelMessage&&(eh.current.onDataChannelMessage=e=>{console.log("\uD83D\uDCE8 StreamPixel: Data channel message",e),ey("data-channel-message",{message:e})}),eh.current.onUnrealMessage&&(eh.current.onUnrealMessage=e=>{console.log("\uD83C\uDFAE Unreal Engine message:",e),ey("unreal-message",{message:e})}))},ek=async()=>{var e;console.log("▶️ Play button clicked"),ey("play-requested"),console.log("\uD83D\uDD04 Setting loading state to true"),_(!0),ea(!1),en(null!=(e=null==H?void 0:H.loadingMessage)?e:"Initializing stream..."),eN(),await ew(),ee("connecting"),W(!0)},eS=()=>{var e,t,n;console.log("⏸️ Pause button clicked"),ey("pause-requested"),null==(e=ep.current)||e.disconnect(),null==(n=eh.current)||null==(t=n.stream)||t.disconnect(),requestAnimationFrame(()=>{W(!1),ee("disconnected")}),eN(),window.location.reload()},eE=()=>{console.log("\uD83D\uDD07 Mute button clicked"),ev.current&&(ev.current.toggleAudio(),O(!q),ey("mute-toggled",{isMuted:!q}))},ez=()=>{var e,t,n,s;ex.current&&(Q?null==(n=(s=document).exitFullscreen)||n.call(s):null==(e=(t=ex.current).requestFullscreen)||e.call(t),J(!Q))},eA=()=>{el===(null==H?void 0:H.password)?(ed(!0),ec(!1)):X("Invalid password")},eM=()=>{let e=window.location.origin,t=new URLSearchParams;M||t.set("hideControls","true"),R||t.set("hideHeader","true"),D||t.set("hideEmbedButton","true"),(null==H?void 0:H.autoConnect)&&t.set("autoConnect","true");let n=t.toString(),s="".concat(e,"/embed/").concat(S).concat(n?"?".concat(n):"");return'\x3c!-- OmniPixel Interactive Stream Embed --\x3e\n<iframe\n  src="'.concat(s,'"\n  width="').concat(P,'"\n  height="').concat(I,"\"\n  frameborder=\"0\"\n  allowfullscreen\n  allow=\"camera; microphone; fullscreen\"\n  style=\"border: none; border-radius: 8px;\">\n</iframe>\n\n\x3c!-- Optional: Listen for iframe events --\x3e\n<script>\nwindow.addEventListener('message', function(event) {\n  if (event.data?.type?.startsWith('omnipixel-')) {\n    console.log('Stream event:', event.data.type, event.data);\n\n    // Handle specific events\n    switch(event.data.type) {\n      case 'omnipixel-status-change':\n        console.log('Stream status:', event.data.streamStatus);\n        break;\n      case 'omnipixel-webrtc-connected':\n        console.log('Stream connected successfully');\n        break;\n      case 'omnipixel-unreal-message':\n        console.log('Unreal Engine message:', event.data.message);\n        break;\n    }\n  }\n});\n\n// Optional: Send commands to the stream\nfunction connectStream() {\n  document.querySelector('iframe').contentWindow.postMessage({\n    type: 'omnipixel-connect'\n  }, '*');\n}\n\nfunction disconnectStream() {\n  document.querySelector('iframe').contentWindow.postMessage({\n    type: 'omnipixel-disconnect'\n  }, '*');\n}\n<\/script>")},eR=async()=>{try{await navigator.clipboard.writeText(eM()),alert("Embed code copied to clipboard!")}catch(e){}},eD=()=>{switch(Y){case"connected":return"bg-green-100 text-green-800";case"connecting":return"bg-yellow-100 text-yellow-800";case"disconnected":default:return"bg-gray-100 text-gray-800";case"error":return"bg-red-100 text-red-800"}},eP=()=>{switch(Y){case"connected":return"Connected";case"connecting":return"Connecting...";case"disconnected":return"Disconnected";case"error":return"Error";default:return"Unknown"}};return(null==H?void 0:H.isPasswordProtected)&&!eo&&er?(0,s.jsx)(l.Zp,{className:z,children:(0,s.jsx)(l.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)(u.A,{className:"h-12 w-12 mx-auto text-gray-400"}),(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Password Protected"}),(0,s.jsx)("p",{className:"text-gray-600",children:"This stream requires a password to access."}),(0,s.jsxs)("div",{className:"max-w-sm mx-auto space-y-3",children:[(0,s.jsx)(o.p,{type:"password",placeholder:"Enter password",value:el,onChange:e=>ei(e.target.value),onKeyDown:e=>"Enter"===e.key&&eA()}),(0,s.jsxs)(c.$,{onClick:eA,className:"w-full",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Access Stream"]})]}),K&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:K})]})})}):$&&B?(0,s.jsx)("div",{className:"relative w-full h-full ".concat(z),children:(0,s.jsxs)("div",{ref:ex,className:"absolute inset-0 bg-black",children:[(0,s.jsx)("div",{ref:eg,className:"absolute inset-0 w-full h-full"}),(L||"connecting"===Y)&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-20",children:(0,s.jsxs)("div",{className:"text-center text-white",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium mb-2",children:et}),(0,s.jsxs)("p",{className:"text-sm opacity-75",children:["Status: ",eP()]})]})}),K&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-red-900 z-20",children:(0,s.jsxs)("div",{className:"text-center text-white p-4",children:[(0,s.jsx)(g.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,s.jsx)("p",{children:K})]})}),M&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"absolute top-4 left-4 z-30 flex items-center space-x-2",children:[(0,s.jsx)(i.E,{className:eD(),children:eP()}),E&&(0,s.jsxs)(i.E,{variant:"outline",className:"text-white border-white/50 bg-black/70 backdrop-blur-sm",children:["Build: ",E.slice(0,8),"..."]})]}),(0,s.jsxs)("div",{className:"absolute bottom-4 right-4 z-30 flex items-center space-x-2",children:[F?(0,s.jsxs)(c.$,{onClick:eS,variant:"destructive",size:"sm",className:"bg-red-600/90 hover:bg-red-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Disconnect"]}):(0,s.jsxs)(c.$,{onClick:ek,size:"sm",className:"bg-green-600/90 hover:bg-green-700 text-white font-medium px-4 backdrop-blur-sm shadow-lg",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Connect"]}),(0,s.jsx)(c.$,{onClick:eE,variant:"outline",size:"sm",className:"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg",children:q?(0,s.jsx)(v.A,{className:"h-4 w-4"}):(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(c.$,{onClick:ez,variant:"outline",size:"sm",className:"text-white border-white/50 bg-black/70 hover:bg-black/80 hover:text-white backdrop-blur-sm shadow-lg",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]})]})]})}):(0,s.jsxs)(l.Zp,{className:z,children:[R&&(0,s.jsx)(l.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.ZB,{children:"Interactive Stream"}),(0,s.jsx)(l.BT,{children:"Real-time interactive streaming experience"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.E,{className:eD(),children:eP()}),E&&(0,s.jsxs)(i.E,{variant:"outline",children:["Build: ",E.slice(0,8),"..."]}),D&&(0,s.jsxs)(d.lG,{open:eu,onOpenChange:em,children:[(0,s.jsx)(d.zM,{asChild:!0,children:(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Embed"]})}),(0,s.jsxs)(d.Cf,{children:[(0,s.jsxs)(d.c7,{children:[(0,s.jsx)(d.L3,{children:"Embed Stream"}),(0,s.jsx)(d.rr,{children:"Copy this code to embed the stream in your website"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Embed Code:"}),(0,s.jsx)("textarea",{className:"w-full mt-1 p-2 border rounded text-sm font-mono",rows:6,readOnly:!0,value:eM()})]}),(0,s.jsx)(c.$,{onClick:eR,className:"w-full",children:"Copy Embed Code"})]})]})]})]})]})}),(0,s.jsxs)(l.Wu,{children:[K&&(0,s.jsx)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-red-500 mr-2"}),(0,s.jsx)("p",{className:"text-sm text-red-700",children:K})]})}),(0,s.jsxs)("div",{ref:ex,className:"relative bg-black rounded-lg overflow-hidden",style:{width:"100%",aspectRatio:"".concat(P,"/").concat(I),minHeight:"400px"},children:[(L||"connecting"===Y)&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900/95 z-10",children:(0,s.jsxs)("div",{className:"text-center text-white",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium mb-2",children:et}),(0,s.jsxs)("p",{className:"text-sm opacity-75",children:["Status: ",eP()]})]})}),!L&&!F&&!K&&"disconnected"===Y&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900 z-10",children:(0,s.jsxs)("div",{className:"text-center text-white space-y-4",children:[(0,s.jsx)(h.A,{className:"h-16 w-16 mx-auto opacity-50"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg mb-2",children:"Ready to Connect"}),(0,s.jsx)("p",{className:"text-sm opacity-75 mb-4",children:ej.disconnected}),!(null==H?void 0:H.autoConnect)&&(0,s.jsxs)(c.$,{onClick:ek,size:"lg",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 mr-2"}),ej.connectButton]})]})]})}),"error"===Y&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-900 z-10",children:(0,s.jsxs)("div",{className:"text-center text-white",children:[(0,s.jsx)(g.A,{className:"h-16 w-16 mx-auto mb-4 text-red-500"}),(0,s.jsx)("p",{className:"text-lg mb-2",children:"Connection Error"}),(0,s.jsx)("p",{className:"text-sm opacity-75",children:K||ej.error})]})}),(0,s.jsx)("div",{ref:eg,className:"absolute inset-0 w-full h-full",style:{zIndex:0,pointerEvents:L?"none":"auto"}})]}),M&&(0,s.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:["disconnected"===Y||"error"===Y?(0,s.jsxs)(c.$,{onClick:ek,disabled:L,children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"Connect"]}):"connecting"===Y?(0,s.jsxs)(c.$,{disabled:!0,children:[(0,s.jsx)(x.A,{className:"h-4 w-4 animate-spin"}),ej.connecting]}):(0,s.jsxs)(c.$,{variant:"outline",onClick:eS,children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),"Disconnect"]}),(0,s.jsx)(c.$,{variant:"outline",onClick:eE,children:q?(0,s.jsx)(v.A,{className:"h-4 w-4"}):(0,s.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.$,{variant:"outline",size:"sm",onClick:ez,children:(0,s.jsx)(b.A,{className:"h-4 w-4"})}),(0,s.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>{{let e="".concat(window.location.origin,"/projects/").concat(S);window.open(e,"_blank","noopener,noreferrer")}},title:"Open in new tab",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[(0,s.jsxs)("p",{children:["Stream ID: ",T]}),E&&(0,s.jsxs)("p",{children:["Build ID: ",E]})]})]})]})}},9840:(e,t,n)=>{n.d(t,{Cf:()=>u,L3:()=>x,c7:()=>m,lG:()=>l,rr:()=>g,zM:()=>i});var s=n(5155);n(2115);var a=n(5452),r=n(4416),c=n(3999);function l(e){let{...t}=e;return(0,s.jsx)(a.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...t})}function o(e){let{...t}=e;return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...n}=e;return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...n})}function u(e){let{className:t,children:n,showCloseButton:l=!0,...i}=e;return(0,s.jsxs)(o,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,children:[n,l&&(0,s.jsxs)(a.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(r.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...n}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,c.cn)("flex flex-col gap-2 text-center sm:text-left",t),...n})}function x(e){let{className:t,...n}=e;return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,c.cn)("text-lg leading-none font-semibold",t),...n})}function g(e){let{className:t,...n}=e;return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,c.cn)("text-muted-foreground text-sm",t),...n})}}}]);