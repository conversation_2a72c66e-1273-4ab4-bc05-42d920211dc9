(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{2714:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var t=a(5155);a(2115);var r=a(968),i=a(3999);function l(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},7161:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>_});var t=a(5155),r=a(2115),i=a(5695),l=a(1089),n=a(1684),c=a(7168),d=a(9852),o=a(2714),m=a(8482),x=a(9840),h=a(8145),u=a(1154),j=a(5169),p=a(7580),g=a(4616),f=a(7108),v=a(1007),b=a(9074),N=a(2657),y=a(6287),w=a(2525);function _(){let{user:e,profile:s,loading:a}=(0,l.A)(),_=(0,i.useRouter)(),[C,k]=(0,r.useState)([]),[A,E]=(0,r.useState)(!0),[S,z]=(0,r.useState)(null),[P,F]=(0,r.useState)(!1),[$,D]=(0,r.useState)(!1),[T,U]=(0,r.useState)({name:"",stream_project_id:"",user_email:""}),J=async()=>{try{E(!0),z(null);let e=await fetch("/api/admin/projects");if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch projects")}let s=await e.json();k(s.projects)}catch(e){z(e instanceof Error?e.message:"Unknown Error"),console.error("Error fetching admin projects:",e)}finally{E(!1)}};(0,r.useEffect)(()=>{if(e&&s){if("platform_admin"!==s.role)return void _.push("/dashboard");J()}},[e,s,_]);let L=async e=>{if(e.preventDefault(),T.name&&T.stream_project_id&&T.user_email)try{D(!0);let e=await fetch("/api/admin/projects",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(T)});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to create project")}let s=await e.json();U({name:"",stream_project_id:"",user_email:""}),F(!1),await J(),alert(s.message)}catch(e){console.error("Error creating project:",e),alert("Failed to create project: "+(e instanceof Error?e.message:"Unknown error"))}finally{D(!1)}},Z=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),B=async(e,s)=>{if(confirm('Are you sure you want to delete "'.concat(s,'"? This action cannot be undone and will delete all associated builds.')))try{let s=await fetch("/api/admin/projects",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({project_id:e})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete project")}alert("Project deleted successfully!"),await J()}catch(e){console.error("Error deleting project:",e),alert("Failed to delete project: "+(e instanceof Error?e.message:"Unknown error"))}};return a||A?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"h-8 w-8 animate-spin"})}):e&&(null==s?void 0:s.role)==="platform_admin"?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(n.V,{}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)(c.$,{variant:"ghost",onClick:()=>_.push("/dashboard"),className:"mb-4",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Panel"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage projects and users across the platform"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(c.$,{variant:"outline",onClick:()=>_.push("/admin/users"),children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Manage Users"]}),(0,t.jsxs)(x.lG,{open:P,onOpenChange:F,children:[(0,t.jsx)(x.zM,{asChild:!0,children:(0,t.jsxs)(c.$,{children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Create Project"]})}),(0,t.jsxs)(x.Cf,{children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsx)(x.L3,{children:"Create New Project"}),(0,t.jsx)(x.rr,{children:"Create a new project and assign it to a user. The user will be able to upload builds and manage the project."})]}),(0,t.jsxs)("form",{onSubmit:L,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"name",children:"Project Name"}),(0,t.jsx)(d.p,{id:"name",value:T.name,onChange:e=>U({...T,name:e.target.value}),placeholder:"My Awesome Game",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"stream_project_id",children:"Stream Project ID"}),(0,t.jsx)(d.p,{id:"stream_project_id",value:T.stream_project_id,onChange:e=>U({...T,stream_project_id:e.target.value}),placeholder:"stream_project_123",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(o.J,{htmlFor:"user_email",children:"User Email"}),(0,t.jsx)(d.p,{id:"user_email",type:"email",value:T.user_email,onChange:e=>U({...T,user_email:e.target.value}),placeholder:"<EMAIL>",required:!0})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>F(!1),children:"Cancel"}),(0,t.jsx)(c.$,{type:"submit",disabled:$,children:$?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):"Create Project"})]})]})]})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)(m.Zp,{children:(0,t.jsx)(m.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-500"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Projects"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.length})]})]})})}),(0,t.jsx)(m.Zp,{children:(0,t.jsx)(m.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-green-500"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Users"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:new Set(C.map(e=>e.profiles.email)).size})]})]})})}),(0,t.jsx)(m.Zp,{children:(0,t.jsx)(m.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-purple-500"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Builds"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.reduce((e,s)=>e+s.builds.length,0)})]})]})})})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"All Projects"}),(0,t.jsx)(m.BT,{children:"Manage all projects across the platform"})]}),(0,t.jsx)(m.Wu,{children:S?(0,t.jsxs)("div",{className:"text-center text-red-600 py-8",children:[(0,t.jsxs)("p",{children:["Error loading projects: ",S]}),(0,t.jsx)(c.$,{variant:"outline",onClick:J,className:"mt-4",children:"Retry"})]}):0===C.length?(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(f.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No projects created yet"}),(0,t.jsx)("p",{className:"text-sm",children:"Create your first project to get started"})]}):(0,t.jsx)("div",{className:"space-y-4",children:C.map(e=>(0,t.jsx)("div",{className:"border rounded-lg p-6 hover:bg-gray-50",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,t.jsx)(h.E,{variant:"outline",children:e.stream_project_id})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-600 mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-1"}),e.profiles.email]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-1"}),e.builds.length," build",1!==e.builds.length?"s":""]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"Created ",Z(e.created_at)]})]}),e.builds.length>0&&(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,t.jsxs)("p",{className:"text-sm font-medium text-gray-900 mb-1",children:["Latest Build: ",e.builds[0].original_filename||e.builds[0].filename]}),(0,t.jsxs)("p",{className:"text-xs text-gray-600",children:["Version ",e.builds[0].version," • ",Z(e.builds[0].created_at)]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2 ml-4",children:[(0,t.jsxs)(c.$,{size:"sm",variant:"outline",onClick:()=>_.push("/admin/projects/".concat(e.id)),children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,t.jsxs)(c.$,{size:"sm",variant:"outline",onClick:()=>_.push("/admin/projects/".concat(e.id)),children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,t.jsxs)(c.$,{size:"sm",variant:"destructive",onClick:()=>B(e.id,e.name),children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]})},e.id))})})]})]})]}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(m.Zp,{children:(0,t.jsx)(m.Wu,{className:"py-8",children:(0,t.jsxs)("div",{className:"text-center text-red-600",children:[(0,t.jsx)("p",{children:"Access denied. Platform admin role required."}),(0,t.jsxs)(c.$,{variant:"outline",onClick:()=>_.push("/dashboard"),className:"mt-4",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})]})})})})}},8145:(e,s,a)=>{"use strict";a.d(s,{E:()=>c});var t=a(5155);a(2115);var r=a(9708),i=a(2085),l=a(3999);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:a,asChild:i=!1,...c}=e,d=i?r.DX:"span";return(0,t.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(n({variant:a}),s),...c})}},8627:(e,s,a)=>{Promise.resolve().then(a.bind(a,7161))},9840:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>m,L3:()=>h,c7:()=>x,lG:()=>n,rr:()=>u,zM:()=>c});var t=a(5155);a(2115);var r=a(5452),i=a(4416),l=a(3999);function n(e){let{...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...s})}function c(e){let{...s}=e;return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...s})}function d(e){let{...s}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...s})}function o(e){let{className:s,...a}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...a})}function m(e){let{className:s,children:a,showCloseButton:n=!0,...c}=e;return(0,t.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,t.jsx)(o,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...c,children:[a,n&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",s),...a})}function h(e){let{className:s,...a}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",s),...a})}function u(e){let{className:s,...a}=e;return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",s),...a})}}},e=>{e.O(0,[4134,5389,3865,9771,6227,2285,1515,6242,8441,5964,7358],()=>e(e.s=8627)),_N_E=e.O()}]);