(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{7108:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7579:(e,s,a)=>{Promise.resolve().then(a.bind(a,7620))},7620:(e,s,a)=>{"use strict";a.d(s,{default:()=>C});var t=a(5155),i=a(2115),r=a(5695),l=a(1684),n=a(7611),c=a(8167),d=a(4574),o=a(9071),m=a(7168),u=a(9852),j=a(3999);function x(e){let{className:s,...a}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,j.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...a})}var h=a(8482),p=a(8145),f=a(1154),g=a(5169),y=a(6287),b=a(2525),v=a(4229),N=a(4416),w=a(7108),_=a(1788),k=a(381);function C(e){var s,a,j,C,E;let{initialProject:A}=e,S=(0,r.useRouter)(),[B,P]=(0,i.useState)(A),[O,F]=(0,i.useState)(!1),[T,D]=(0,i.useState)(null),[J,R]=(0,i.useState)(!1),[Z,I]=(0,i.useState)(!1),[U,V]=(0,i.useState)(!1),[$,L]=(0,i.useState)({name:A.name,stream_project_id:A.stream_project_id,user_email:A.profiles.email,config:JSON.stringify(A.config||{},null,2)}),W=async()=>{F(!0),D(null);try{let e=await fetch("/api/admin/projects/".concat(B.id));if(!e.ok)throw Error("Failed to refresh project");let s=await e.json();P(s.project),L({name:s.project.name,stream_project_id:s.project.stream_project_id,user_email:s.project.profiles.email,config:JSON.stringify(s.project.config||{},null,2)})}catch(e){D(e instanceof Error?e.message:"An unknown error occurred")}finally{F(!1)}},M=async()=>{if(B){I(!0),D(null);try{let e;try{e=JSON.parse($.config)}catch(e){throw Error("Invalid JSON in config field")}let s=await fetch("/api/admin/projects",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({project_id:B.id,name:$.name,stream_project_id:$.stream_project_id,user_email:$.user_email,config:e})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update project")}let a=await s.json();P(a.project),R(!1),alert("Project updated successfully!")}catch(e){D(e instanceof Error?e.message:"An unknown error occurred"),alert("Failed to update project: "+e)}finally{I(!1)}}},z=async()=>{if(B&&confirm('Are you sure you want to delete "'.concat(B.name,'"? This action cannot be undone.'))){V(!0),D(null);try{let e=await fetch("/api/admin/projects",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({project_id:B.id})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to delete project")}alert("Project deleted successfully!"),S.push("/admin")}catch(e){D(e instanceof Error?e.message:"An unknown error occurred"),alert("Failed to delete project: "+e)}finally{V(!1)}}},H=async e=>{try{if(!(await fetch("/api/projects/".concat(B.id,"/builds/revert"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({build_id:e})})).ok)throw Error("Failed to revert build");W(),alert("Build reverted successfully!")}catch(e){alert("Failed to revert build: "+(e instanceof Error?e.message:"Unknown error"))}},q=async e=>{try{if(!(await fetch("/api/projects/".concat(B.id,"/builds/").concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete build");W(),alert("Build deleted successfully!")}catch(e){alert("Failed to delete build: "+e)}},G=async e=>{try{if(!(await fetch("/api/projects/".concat(B.id,"/builds/").concat(e),{method:"PATCH"})).ok)throw Error("Failed to activate build");W(),alert("Build activated successfully!")}catch(e){alert("Failed to activate build: "+(e instanceof Error?e.message:"Unknown error"))}};return O?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("div",{className:"flex items-center justify-center py-16",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 animate-spin"})," Loading..."]})]}):T||!B?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(l.V,{}),(0,t.jsx)("div",{className:"flex items-center justify-center py-16",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Error"}),(0,t.jsx)("p",{className:"text-red-600 mt-2",children:T||"Project not found"}),(0,t.jsxs)(m.$,{onClick:()=>S.push("/admin"),className:"mt-4",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Back to Admin Panel"]})]})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)(m.$,{variant:"ghost",onClick:()=>S.push("/admin"),className:"mb-4",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"})," Back to Admin Panel"]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:B.name}),(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:["Owned by ",B.profiles.email," • Stream ID: ",B.stream_project_id]})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:J?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(m.$,{onClick:M,disabled:Z,children:[Z?(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]}),(0,t.jsxs)(m.$,{variant:"outline",onClick:()=>{B&&(L({name:B.name,stream_project_id:B.stream_project_id,user_email:B.profiles.email,config:JSON.stringify(B.config||{},null,2)}),R(!1))},children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"})," Cancel"]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(m.$,{onClick:()=>R(!0),children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"})," Edit Project"]}),(0,t.jsxs)(m.$,{variant:"destructive",onClick:z,disabled:U,children:[U?(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Delete Project"]})]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[!J&&B.stream_project_id&&(0,t.jsx)(o.StreamPlayer,{projectId:B.id,buildId:null==(a=B.builds)||null==(s=a.find(e=>e.is_current))?void 0:s.id,config:B.config,showControls:!0,showHeader:!0,showEmbedButton:!0}),J&&(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:"Edit Project Details"}),(0,t.jsx)(h.BT,{children:"Update project information and configuration"})]}),(0,t.jsxs)(h.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Project Name"}),(0,t.jsx)(u.p,{value:$.name,onChange:e=>L({...$,name:e.target.value})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Stream Project ID"}),(0,t.jsx)(u.p,{value:$.stream_project_id,onChange:e=>L({...$,stream_project_id:e.target.value})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Owner Email"}),(0,t.jsx)(u.p,{value:$.user_email,onChange:e=>L({...$,user_email:e.target.value})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Project Configuration (JSON)"}),(0,t.jsx)(x,{value:$.config,onChange:e=>L({...$,config:e.target.value}),rows:8,className:"font-mono text-sm"})]})]})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:"Upload New Build"}),(0,t.jsx)(h.BT,{children:"Upload a new build file for this project as an admin."})]}),B.builds.length<2&&(0,t.jsx)(h.Wu,{children:(0,t.jsx)(n.s,{projectId:B.id,onUploadComplete:()=>{alert("Build uploaded successfully!"),W()},onUploadError:e=>{alert("Upload failed: "+e)}})})]}),(0,t.jsx)(c.g,{projectId:B.id,currentConfig:B.config||{},onConfigUpdate:e=>P({...B,config:{...e,primaryCodec:e.primaryCodec,fallBackCodec:e.fallBackCodec,resolutionMode:e.resolutionMode,appId:B.id}}),isAdmin:!0}),(0,t.jsx)(d.T,{projectId:B.id,builds:(B.builds||[]).map((e,s)=>({...e,id:e.id||"admin-build-".concat(s,"-").concat(e.filename),project_id:B.id,s3_key:e.filename,status:"active",updated_at:e.created_at})),onBuildRevert:H,onBuildDelete:q,onBuildActivate:G,onRefresh:W,isAdmin:!0})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(h.Zp,{children:[(0,t.jsx)(h.aR,{children:(0,t.jsxs)(h.ZB,{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"h-5 w-5 mr-2"}),"Project Statistics"]})}),(0,t.jsxs)(h.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Builds:"}),(0,t.jsx)(p.E,{variant:"secondary",children:(null==(j=B.builds)?void 0:j.length)||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Latest Version:"}),(0,t.jsx)(p.E,{variant:"outline",children:(null==(E=B.builds)||null==(C=E[0])?void 0:C.version)||"None"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Owner Role:"}),(0,t.jsx)(p.E,{variant:"platform_admin"===B.profiles.role?"default":"secondary",children:B.profiles.role})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Created:"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:new Date(B.created_at).toLocaleDateString()})]})]})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsx)(h.aR,{children:(0,t.jsx)(h.ZB,{children:"Recent Builds"})}),(0,t.jsx)(h.Wu,{children:B.builds&&B.builds.length>0?(0,t.jsx)("div",{className:"space-y-3",children:B.builds.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:e.original_filename||e.filename}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Version ",e.version," • ",new Date(e.created_at).toLocaleDateString()]})]}),(0,t.jsx)(m.$,{size:"sm",variant:"outline",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})})]},e.id))}):(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No builds uploaded yet"})})]}),!J&&(0,t.jsxs)(h.Zp,{children:[(0,t.jsx)(h.aR,{children:(0,t.jsxs)(h.ZB,{className:"flex items-center",children:[(0,t.jsx)(k.A,{className:"h-5 w-5 mr-2"})," Current Configuration"]})}),(0,t.jsx)(h.Wu,{children:(0,t.jsx)("pre",{className:"text-xs bg-gray-50 p-3 rounded-lg overflow-auto",children:JSON.stringify(B.config||{},null,2)})})]})]})]})]})]})}}},e=>{e.O(0,[7113,4306,4134,5389,3865,9771,6227,2285,6202,5580,2405,6242,9071,8167,5860,8441,5964,7358],()=>e(e.s=7579)),_N_E=e.O()}]);