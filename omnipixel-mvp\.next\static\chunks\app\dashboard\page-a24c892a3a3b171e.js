(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{3852:(e,s,a)=>{"use strict";a.d(s,{default:()=>N});var l=a(5155),r=a(2115),t=a(1684),i=a(6874),n=a.n(i),c=a(8482),d=a(7168),m=a(381),x=a(5690),o=a(9869),h=a(757);function j(e){var s;let{project:a}=e,r=(null==(s=a.builds)?void 0:s.filter(e=>!["failed","archived"].includes(e.status||"")))||[],t=r[0],i=r.length;return(0,l.jsxs)(c.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,l.jsx)(c.aR,{children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(c.ZB,{className:"text-lg",children:a.name}),(0,l.jsxs)(c.BT,{children:["Stream ID: ",a.stream_project_id]})]}),(0,l.jsx)("div",{className:"flex gap-2",children:(0,l.jsx)(d.$,{variant:"outline",size:"sm",asChild:!0,children:(0,l.jsxs)(n(),{href:"/projects/".concat(a.id),children:[(0,l.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Manage"]})})})]})}),(0,l.jsx)(c.Wu,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,l.jsxs)("span",{children:[i," build",1!==i?"s":""]}),t&&(0,l.jsxs)("span",{children:["Last updated ",(0,h.m)(new Date(t.created_at),{addSuffix:!0})]})]}),t&&(0,l.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium text-sm",children:"Latest Build"}),(0,l.jsx)("p",{className:"text-xs text-gray-600",children:t.original_filename||t.filename}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:["Version ",t.version]})]}),(0,l.jsx)(d.$,{size:"sm",asChild:!0,children:(0,l.jsxs)(n(),{href:"/projects/".concat(a.id,"/stream"),children:[(0,l.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"Stream"]})})]})}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(d.$,{variant:"outline",className:"flex-1",asChild:!0,children:(0,l.jsxs)(n(),{href:"/projects/".concat(a.id),children:[(0,l.jsx)(o.A,{className:"h-4 w-4 mr-1"}),"Upload Build"]})}),t&&(0,l.jsx)(d.$,{className:"flex-1",asChild:!0,children:(0,l.jsxs)(n(),{href:"/projects/".concat(a.id,"/stream"),children:[(0,l.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"Stream"]})})]})]})})]})}var u=a(9852),p=a(4616),f=a(7924);function N(e){let{user:s,profile:a,projects:i}=e,[n,m]=(0,r.useState)(""),x=(null==a?void 0:a.role)==="platform_admin",o=(0,r.useMemo)(()=>i.filter(e=>{var s,a;return(null==(s=e.name)?void 0:s.toLowerCase().includes(n.toLowerCase()))||(null==(a=e.stream_project_id)?void 0:a.toLowerCase().includes(n.toLowerCase()))}),[i,n]);return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)(t.V,{}),(0,l.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,l.jsxs)("p",{className:"text-gray-600 mt-1",children:["Welcome back, ",s.email,x&&(0,l.jsx)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:"Platform Admin"})]})]}),x&&(0,l.jsxs)(d.$,{onClick:()=>window.location.href="/admin",children:[(0,l.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Admin Panel"]})]})}),(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsxs)("div",{className:"relative max-w-md",children:[(0,l.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,l.jsx)(u.p,{placeholder:"Search projects...",value:n,onChange:e=>m(e.target.value),className:"pl-10"})]})}),0===o.length?(0,l.jsx)(c.Zp,{children:(0,l.jsx)(c.Wu,{className:"py-12",children:(0,l.jsx)("div",{className:"text-center",children:0===i.length?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No projects assigned"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:x?"Use the Admin Panel to create projects for users.":"Contact your administrator to get projects assigned to your account."}),x&&(0,l.jsxs)(d.$,{onClick:()=>window.location.href="/admin",children:[(0,l.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Go to Admin Panel"]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No projects found"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search terms."})]})})})}):(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map(e=>(0,l.jsx)(j,{project:e},e.id))})]})]})}},7355:(e,s,a)=>{Promise.resolve().then(a.bind(a,3852))}},e=>{e.O(0,[4134,5389,3865,9771,6227,2285,8914,6242,8441,5964,7358],()=>e(e.s=7355)),_N_E=e.O()}]);