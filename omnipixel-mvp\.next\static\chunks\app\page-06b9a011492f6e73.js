(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{1057:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var l=r(5155),n=r(2115),s=r(5695),i=r(1089);function u(){let{user:e,loading:t}=(0,i.A)(),r=(0,s.useRouter)();return((0,n.useEffect)(()=>{t||(e?r.push("/dashboard"):r.push("/login"))},[e,t,r]),t)?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)("div",{className:"text-lg",children:"Loading..."})}):(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)("div",{className:"text-lg",children:"Redirecting..."})})}},1089:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>u,A:()=>a});var l=r(5155),n=r(2115);let s=(0,r(3865).createBrowserClient)("https://qrnstvofnizsgdlubtbt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.D0KmXra-FWDKBlJUvlDOmDcIXdC1mquCUA1vQFfpFVU"),i=(0,n.createContext)(void 0);function u(e){let{children:t}=e,[r,u]=(0,n.useState)(null),[a,c]=(0,n.useState)(null),[o,d]=(0,n.useState)(null),[h,f]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{try{let{data:{user:e},error:t}=await s.auth.getUser();if(t){console.error("Error getting user:",t),u(null),d(null),f(!1);return}if(e){let{data:{session:t}}=await s.auth.getSession();u(e),d(t),await m(e.id)}else u(null),d(null),f(!1)}catch(e){console.error("Error in getInitialUser:",e),u(null),d(null),f(!1)}})();let{data:{subscription:e}}=s.auth.onAuthStateChange(async(e,t)=>{if("SIGNED_OUT"!==e&&t)try{let{data:{user:e},error:r}=await s.auth.getUser();if(r||!e){u(null),d(null),c(null),f(!1);return}u(e),d(t),await m(e.id)}catch(e){console.error("Error verifying user:",e),u(null),d(null),c(null),f(!1)}else u(null),d(null),c(null),f(!1)});return()=>e.unsubscribe()},[]);let m=async e=>{try{let{data:t,error:r}=await s.from("profiles").select("*").eq("id",e).single();r?(console.error("Error fetching profile:",r),c(null)):c(t)}catch(e){console.error("Error fetching profile:",e),c(null)}finally{f(!1)}},g=async()=>{await s.auth.signOut()};return(0,l.jsx)(i.Provider,{value:{user:r,profile:a,session:o,loading:h,signOut:g},children:t})}function a(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1216:(e,t,r)=>{Promise.resolve().then(r.bind(r,1057))},5695:(e,t,r)=>{"use strict";var l=r(8999);r.o(l,"useRouter")&&r.d(t,{useRouter:function(){return l.useRouter}})}},e=>{e.O(0,[4134,3865,8441,5964,7358],()=>e(e.s=1216)),_N_E=e.O()}]);