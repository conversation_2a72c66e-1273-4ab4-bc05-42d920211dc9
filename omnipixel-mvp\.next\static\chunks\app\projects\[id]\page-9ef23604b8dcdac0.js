(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6801],{798:(e,t,s)=>{Promise.resolve().then(s.bind(s,1326))},1326:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var a=s(5155),r=s(2115),i=s(5695),n=s(1684),l=s(7611),c=s(8167),o=s(4574),d=s(9071),h=s(7168),u=s(9852),m=s(88),x=s(2714),j=s(8482),p=s(8145),f=s(5169),g=s(4229),y=s(4416),v=s(6287),w=s(1154);function b(e){var t;let{user:s,project:b,initialBuilds:N}=e,k=(0,i.useRouter)(),[A,C]=(0,r.useState)(N),[E,S]=(0,r.useState)(!1),[B,_]=(0,r.useState)(b.name),[T,F]=(0,r.useState)(b.auto_release),[P,D]=(0,r.useState)(!1),[R,I]=(0,r.useState)(null),O=async()=>{try{D(!0),I(null);let e=await fetch("/api/projects/".concat(b.id,"/builds"));if(!e.ok)throw Error("Could not fetch builds");let t=await e.json();C(t.builds)}catch(e){I(e instanceof Error?e.message:"An unknown error occurred")}finally{D(!1)}},U=async e=>{try{if(D(!0),!(await fetch("/api/projects/".concat(b.id,"/builds/revert"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({build_id:e})})).ok)throw Error("Failed to revert build");await O(),alert("Build reverted successfully!")}catch(e){I(e instanceof Error?e.message:"An unknown error occurred")}finally{D(!1)}},z=async e=>{try{if(D(!0),!(await fetch("/api/projects/".concat(b.id,"/builds/").concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete build");await O(),alert("Build deleted successfully!")}catch(e){I(e instanceof Error?e.message:"An unknown error occurred")}finally{D(!1)}},H=async e=>{try{D(!0),I(null),console.log("Activating build:",e,"for project:",b.id);let t=await fetch("/api/projects/".concat(b.id,"/builds/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"}});if(console.log("Activation response status:",t.status),!t.ok){let e=await t.json();throw console.error("Activation failed:",e),Error(e.error||"Failed to activate build")}let s=await t.json();console.log("Activation result:",s),await O(),alert("Build activated successfully!")}catch(t){console.error("Build activation error:",t);let e=t instanceof Error?t.message:"An unknown error occurred";I(e),alert("Failed to activate build: "+e)}finally{D(!1)}},J=async()=>{if(B.trim())try{if(D(!0),!(await fetch("/api/projects/".concat(b.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:B.trim()})})).ok)throw Error("Failed to update project name");S(!1)}catch(e){I(e instanceof Error?e.message:"An unknown error occurred")}finally{D(!1)}},L=async e=>{try{if(D(!0),!(await fetch("/api/projects/".concat(b.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({auto_release:e})})).ok)throw Error("Failed to update auto-release setting");F(e)}catch(e){I(e instanceof Error?e.message:"An unknown error occurred")}finally{D(!1)}},Z=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(n.V,{}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)(h.$,{variant:"ghost",onClick:()=>k.push("/dashboard"),className:"mb-4",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"})," Back to Dashboard"]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 mb-2",children:E?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.p,{value:B,onChange:e=>_(e.target.value)}),(0,a.jsx)(h.$,{size:"sm",onClick:J,children:(0,a.jsx)(g.A,{className:"h-4 w-4"})}),(0,a.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>{_(b.name),S(!1)},children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:b.name}),(0,a.jsx)(h.$,{size:"sm",variant:"ghost",onClick:()=>S(!0),children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Stream Project ID: ",(0,a.jsx)("span",{className:"font-mono",children:b.stream_project_id})]}),(0,a.jsxs)(p.E,{variant:"secondary",children:[A.length," build",1!==A.length?"s":""]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsx)(d.StreamPlayer,{projectId:b.id,buildId:null==(t=A.find(e=>e.is_current))?void 0:t.id,config:b.config,showControls:!0,showHeader:!0,showEmbedButton:!0}),(0,a.jsx)(c.g,{projectId:b.id,currentConfig:b.config||{},onConfigUpdate:e=>{},isAdmin:!1}),A.length<2?(0,a.jsx)(l.s,{projectId:b.id,onUploadComplete:e=>{e.build?C(t=>[e.build,...t].sort((e,t)=>t.version-e.version)):O()},onUploadError:e=>I(e)}):(0,a.jsxs)(j.Zp,{children:[(0,a.jsxs)(j.aR,{children:[(0,a.jsx)(j.ZB,{children:"Build Limit Reached"}),(0,a.jsx)(j.BT,{children:"You have reached the maximum of 2 builds per project. Delete an existing build to upload a new one."})]}),(0,a.jsx)(j.Wu,{children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Active builds: ",A.filter(e=>!["failed","archived"].includes(e.status)).length,"/2"]})})]}),(0,a.jsx)(o.T,{projectId:b.id,builds:A,onBuildRevert:U,onBuildDelete:z,onBuildActivate:H,onRefresh:O,isAdmin:!1})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(j.Zp,{children:[(0,a.jsx)(j.aR,{children:(0,a.jsx)(j.ZB,{children:"Project Settings"})}),(0,a.jsxs)(j.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(x.J,{htmlFor:"auto-release",className:"text-sm font-medium text-gray-700",children:"Auto-Release"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Automatically activate new builds when uploaded"})]}),(0,a.jsx)(m.d,{id:"auto-release",checked:T,onCheckedChange:L,disabled:P})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Created"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:Z(b.created_at)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:Z(b.updated_at)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Builds"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:A.length})]})]})]})})]}),R&&(0,a.jsx)("div",{className:"mt-4 text-red-600 text-center",children:R}),P&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50",children:(0,a.jsx)(w.A,{className:"h-10 w-10 animate-spin text-white"})})]})]})}}},e=>{e.O(0,[7113,4306,4134,5389,3865,9771,6227,2285,6202,5580,2405,6242,9071,8167,5860,8441,5964,7358],()=>e(e.s=798)),_N_E=e.O()}]);