{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/supabase.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\r\nimport { StreamPixelConfig } from 'streampixelsdk'\r\n\r\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\r\nconst supabasePublishableKey = process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!\r\n\r\nexport const supabase = createBrowserClient(supabaseUrl, supabasePublishableKey)\r\n\r\n// Types for our database tables\r\nexport interface Profile {\r\n  id: string\r\n  email: string\r\n  role: 'user' | 'platform_admin'\r\n  created_at: string\r\n  updated_at: string\r\n}\r\n\r\nexport interface Project {\r\n  id: string\r\n  user_id: string\r\n  name: string\r\n  stream_project_id: string\r\n  auto_release: boolean\r\n  config?: StreamPixelConfig\r\n  created_at: string\r\n  updated_at: string\r\n}\r\n\r\nexport interface Build {\r\n  id: string\r\n  project_id: string\r\n  filename: string\r\n  original_filename?: string\r\n  s3_key: string\r\n  version: number\r\n  status: 'uploading' | 'processing' | 'active' | 'archived' | 'failed' | 'inactive'\r\n  is_current: boolean\r\n  file_size?: number\r\n  streampixel_build_id?: string\r\n  streampixel_status?: string\r\n  error_message?: string\r\n  created_at: string\r\n  updated_at: string\r\n}\r\n"], "names": [], "mappings": ";;;AAGoB;AAHpB;AAAA;;AAGA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Gautam/Projects/OmniPixel/omnipixel-mvp/lib/auth-context.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react'\r\nimport { User, Session } from '@supabase/supabase-js'\r\nimport { supabase } from './supabase'\r\nimport type { Profile } from './supabase'\r\n\r\ninterface AuthContextType {\r\n  user: User | null\r\n  profile: Profile | null\r\n  session: Session | null\r\n  loading: boolean\r\n  signOut: () => Promise<void>\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\r\n\r\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null)\r\n  const [profile, setProfile] = useState<Profile | null>(null)\r\n  const [session, setSession] = useState<Session | null>(null)\r\n  const [loading, setLoading] = useState(true)\r\n\r\n  useEffect(() => {\r\n    // Get initial authenticated user\r\n    const getInitialUser = async () => {\r\n      try {\r\n        const { data: { user }, error } = await supabase.auth.getUser()\r\n        if (error) {\r\n          console.error('Error getting user:', error)\r\n          setUser(null)\r\n          setSession(null)\r\n          setLoading(false)\r\n          return\r\n        }\r\n\r\n        if (user) {\r\n          // Get session for additional data\r\n          const { data: { session } } = await supabase.auth.getSession()\r\n          setUser(user)\r\n          setSession(session)\r\n          await fetchProfile(user.id)\r\n        } else {\r\n          setUser(null)\r\n          setSession(null)\r\n          setLoading(false)\r\n        }\r\n      } catch (error) {\r\n        console.error('Error in getInitialUser:', error)\r\n        setUser(null)\r\n        setSession(null)\r\n        setLoading(false)\r\n      }\r\n    }\r\n\r\n    getInitialUser()\r\n\r\n    // Listen for auth changes\r\n    const {\r\n      data: { subscription },\r\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\r\n      if (event === 'SIGNED_OUT' || !session) {\r\n        setUser(null)\r\n        setSession(null)\r\n        setProfile(null)\r\n        setLoading(false)\r\n      } else {\r\n        // Verify the user with getUser() for security\r\n        try {\r\n          const { data: { user }, error } = await supabase.auth.getUser()\r\n          if (error || !user) {\r\n            setUser(null)\r\n            setSession(null)\r\n            setProfile(null)\r\n            setLoading(false)\r\n            return\r\n          }\r\n\r\n          setUser(user)\r\n          setSession(session)\r\n          await fetchProfile(user.id)\r\n        } catch (error) {\r\n          console.error('Error verifying user:', error)\r\n          setUser(null)\r\n          setSession(null)\r\n          setProfile(null)\r\n          setLoading(false)\r\n        }\r\n      }\r\n    })\r\n\r\n    return () => subscription.unsubscribe()\r\n  }, [])\r\n\r\n  const fetchProfile = async (userId: string) => {\r\n    try {\r\n      const { data, error } = await supabase\r\n        .from('profiles')\r\n        .select('*')\r\n        .eq('id', userId)\r\n        .single()\r\n\r\n      if (error) {\r\n        console.error('Error fetching profile:', error)\r\n        // If profile doesn't exist, it might be because the trigger hasn't run yet\r\n        // or there was an issue with profile creation. Set profile to null but continue.\r\n        setProfile(null)\r\n      } else {\r\n        setProfile(data)\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching profile:', error)\r\n      setProfile(null)\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  const signOut = async () => {\r\n    await supabase.auth.signOut()\r\n  }\r\n\r\n  const value = {\r\n    user,\r\n    profile,\r\n    session,\r\n    loading,\r\n    signOut,\r\n  }\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\r\n}\r\n\r\nexport function useAuth() {\r\n  const context = useContext(AuthContext)\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider')\r\n  }\r\n  return context\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,iCAAiC;YACjC,MAAM;yDAAiB;oBACrB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,kHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;wBAC7D,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,uBAAuB;4BACrC,QAAQ;4BACR,WAAW;4BACX,WAAW;4BACX;wBACF;wBAEA,IAAI,MAAM;4BACR,kCAAkC;4BAClC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,kHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;4BAC5D,QAAQ;4BACR,WAAW;4BACX,MAAM,aAAa,KAAK,EAAE;wBAC5B,OAAO;4BACL,QAAQ;4BACR,WAAW;4BACX,WAAW;wBACb;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,kHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;oBAChD,IAAI,UAAU,gBAAgB,CAAC,SAAS;wBACtC,QAAQ;wBACR,WAAW;wBACX,WAAW;wBACX,WAAW;oBACb,OAAO;wBACL,8CAA8C;wBAC9C,IAAI;4BACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,kHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;4BAC7D,IAAI,SAAS,CAAC,MAAM;gCAClB,QAAQ;gCACR,WAAW;gCACX,WAAW;gCACX,WAAW;gCACX;4BACF;4BAEA,QAAQ;4BACR,WAAW;4BACX,MAAM,aAAa,KAAK,EAAE;wBAC5B,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,yBAAyB;4BACvC,QAAQ;4BACR,WAAW;4BACX,WAAW;4BACX,WAAW;wBACb;oBACF;gBACF;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,2EAA2E;gBAC3E,iFAAiF;gBACjF,WAAW;YACb,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,MAAM,kHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAlHgB;KAAA;AAoHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}