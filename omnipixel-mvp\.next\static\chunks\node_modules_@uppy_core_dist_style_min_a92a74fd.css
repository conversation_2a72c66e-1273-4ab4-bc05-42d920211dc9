/* [project]/node_modules/@uppy/core/dist/style.min.css [app-client] (css) */
.uppy-Root {
  box-sizing: border-box;
  color: #333;
  text-align: left;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: -apple-system, system-ui, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, Segoe UI Symbol, Segoe UI Emoji, Apple Color Emoji, Helvetica, Arial, sans-serif;
  line-height: 1;
  position: relative;
}

.uppy-Root[dir="rtl"], [dir="rtl"] .uppy-Root {
  text-align: right;
}

.uppy-Root *, .uppy-Root :after, .uppy-Root :before {
  box-sizing: inherit;
}

.uppy-Root [hidden] {
  display: none;
}

.uppy-u-reset {
  all: initial;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-sizing: border-box;
  font-family: -apple-system, system-ui, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, Segoe UI Symbol, Segoe UI Emoji, Apple Color Emoji, Helvetica, Arial, sans-serif;
  line-height: 1;
}

[dir="rtl"] .uppy-u-reset {
  text-align: right;
}

.uppy-c-textInput {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 8px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

.uppy-size--md .uppy-c-textInput {
  padding: 8px 10px;
}

.uppy-c-textInput:focus {
  border-color: rgba(18, 105, 207, .6);
  outline: none;
  box-shadow: 0 0 0 3px rgba(18, 105, 207, .15);
}

[data-uppy-theme="dark"] .uppy-c-textInput {
  color: #eaeaea;
  background-color: #333;
  border-color: #333;
}

[data-uppy-theme="dark"] .uppy-c-textInput:focus {
  box-shadow: none;
  border-color: #525252;
}

.uppy-c-icon {
  fill: currentColor;
  max-width: 100%;
  max-height: 100%;
  display: inline-block;
  overflow: hidden;
}

.uppy-c-btn {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  line-height: 1;
  transition-property: background-color, color;
  transition-duration: .3s;
  display: inline-flex;
}

.uppy-c-btn, [dir="rtl"] .uppy-c-btn {
  text-align: center;
}

.uppy-c-btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}

.uppy-c-btn::-moz-focus-inner {
  border: 0;
}

.uppy-c-btn-primary {
  color: #fff;
  background-color: #1269cf;
  border-radius: 4px;
  padding: 10px 18px;
  font-size: 14px;
}

.uppy-c-btn-primary:not(:disabled):hover {
  background-color: #0e51a0;
}

.uppy-c-btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(18, 105, 207, .4);
}

.uppy-size--md .uppy-c-btn-primary {
  padding: 13px 22px;
}

[data-uppy-theme="dark"] .uppy-c-btn-primary {
  color: #eaeaea;
}

[data-uppy-theme="dark"] .uppy-c-btn-primary:focus {
  outline: none;
}

[data-uppy-theme="dark"] .uppy-c-btn-primary::-moz-focus-inner {
  border: 0;
}

[data-uppy-theme="dark"] .uppy-c-btn-primary:focus {
  box-shadow: 0 0 0 2px rgba(170, 225, 255, .85);
}

.uppy-c-btn-primary.uppy-c-btn--disabled {
  background-color: #8eb2db;
}

.uppy-c-btn-link {
  background-color: initial;
  color: #525252;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 14px;
  line-height: 1;
}

.uppy-c-btn-link:hover {
  color: #333;
}

.uppy-c-btn-link:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(18, 105, 207, .25);
}

.uppy-size--md .uppy-c-btn-link {
  padding: 13px 18px;
}

[data-uppy-theme="dark"] .uppy-c-btn-link {
  color: #eaeaea;
}

[data-uppy-theme="dark"] .uppy-c-btn-link:focus {
  outline: none;
}

[data-uppy-theme="dark"] .uppy-c-btn-link::-moz-focus-inner {
  border: 0;
}

[data-uppy-theme="dark"] .uppy-c-btn-link:focus {
  box-shadow: 0 0 0 2px rgba(170, 225, 255, .85);
}

[data-uppy-theme="dark"] .uppy-c-btn-link:hover {
  color: #939393;
}

/*# sourceMappingURL=node_modules_%40uppy_core_dist_style_min_a92a74fd.css.map*/