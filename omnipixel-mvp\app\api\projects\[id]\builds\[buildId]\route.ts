import { NextRequest, NextResponse } from 'next/server'
import { deleteFileFromS3 } from '@/lib/aws'
import { createClient } from '@/utils/supabase/server';

// DELETE - Delete build and remove from Backblaze B2
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; buildId: string }> }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const {id:projectId, buildId} = await params

    // Verify user owns this project or is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to verify user profile' },
        { status: 500 }
      )
    }

    const isAdmin = profile?.role === 'platform_admin'

    // If not admin, verify project ownership
    if (!isAdmin) {
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id')
        .eq('id', projectId)
        .eq('user_id', user.id)
        .single()

      if (projectError || !project) {
        return NextResponse.json(
          { error: 'Project not found or access denied' },
          { status: 404 }
        )
      }
    }

    // Get the build to delete
    const { data: build, error: buildError } = await supabase
      .from('builds')
      .select('*')
      .eq('id', buildId)
      .eq('project_id', projectId)
      .single()

    if (buildError || !build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    // Check if this is the only build - prevent deletion if so
    const { data: allBuilds, error: countError } = await supabase
      .from('builds')
      .select('id')
      .eq('project_id', projectId)
      .neq('status', 'failed')

    if (countError) {
      return NextResponse.json(
        { error: 'Failed to check build count' },
        { status: 500 }
      )
    }

    // Allow deletion even if it's the only build - user might want to start fresh
    
    // Delete from Backblaze B2 first
    try {
      if (build.s3_key) {
        await deleteFileFromS3(build.s3_key)
      }
    } catch (b2Error: unknown) {
      console.error('Error deleting from Backblaze B2:', b2Error)
      // Continue with database deletion even if B2 deletion fails
      // The file might already be deleted or the key might be invalid
    }

    // Delete from database
    const { error: deleteError } = await supabase
      .from('builds')
      .delete()
      .eq('id', buildId)

    if (deleteError) {
      console.error('Error deleting build from database:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete build' },
        { status: 500 }
      )
    }

    // If we deleted the current build, check if there's another build to make current
    if (build.is_current && allBuilds.length > 1) {
      // Find the most recent remaining build
      const { data: remainingBuilds, error: remainingError } = await supabase
        .from('builds')
        .select('*')
        .eq('project_id', projectId)
        .neq('status', 'failed')
        .order('version', { ascending: false })
        .limit(1)

      if (!remainingError && remainingBuilds && remainingBuilds.length > 0) {
        // Set the most recent build as current
        await supabase
          .from('builds')
          .update({ 
            is_current: true, 
            status: 'active',
            updated_at: new Date().toISOString()
          })
          .eq('id', remainingBuilds[0].id)
      }
    }

    return NextResponse.json({
      message: 'Build deleted successfully',
      deletedBuild: {
        id: build.id,
        filename: build.filename,
        version: build.version
      }
    })

  } catch (error: unknown) {
    console.error('Error deleting build:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH - Activate build (set as current and upload to StreamPixel)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; buildId: string }> }
) {
  try {
    // Await params
    const { id: projectId, buildId } = await params
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Authentication error:', authError)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    console.log('Authenticated user:', user.id)

    // projectId and buildId already extracted from awaited params above

    // Verify user owns this project or is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to verify user profile' },
        { status: 500 }
      )
    }

    const isAdmin = profile?.role === 'platform_admin'

    // Get project details (needed for StreamPixel integration)
    let projectQuery;

    if(isAdmin){
      projectQuery = supabase.from('projects').select('*').eq('id', projectId)
    }else{
      projectQuery = supabase.from('projects').select('*').eq('id', projectId).eq('user_id', user.id)
    }

    const { data: project, error: projectError } = await projectQuery.single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Get the build to activate
    console.log('Looking for build:', buildId, 'in project:', projectId)

    const { data: build, error: buildError } = await supabase
      .from('builds')
      .select('*')
      .eq('id', buildId)
      .eq('project_id', projectId)
      .single()

    if (buildError) {
      console.error('Error finding build:', buildError)
      return NextResponse.json(
        { error: `Build not found: ${buildError.message}` },
        { status: 404 }
      )
    }

    if (!build) {
      console.error('Build not found - no data returned')
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    console.log('Found build:', {
      id: build.id,
      project_id: build.project_id,
      filename: build.filename,
      is_current: build.is_current,
      status: build.status
    })

    // Check if build is already current
    if (build.is_current) {
      return NextResponse.json(
        { error: 'This build is already active' },
        { status: 400 }
      )
    }

    // Set this build as current (triggers will handle the rest)
    console.log('Updating build:', buildId, 'in project:', projectId)

    // Use regular client for build update
    console.log('Using regular client for build update')

    const { data: exists, error: existsError } = await supabase
  .from('builds')
  .select('*')
  .eq('id', buildId)
  .eq('project_id', projectId);

console.log('Build exists check:', { exists, existsError, buildId, projectId });

    if (existsError) {
      console.error('Error checking if build exists:', existsError)
      return NextResponse.json(
        { error: 'Failed to verify build exists', details: existsError.message },
        { status: 500 }
      )
    }

    if (!exists || exists.length === 0) {
      console.error('Build not found:', { buildId, projectId })
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    // DON'T deactivate current build yet - wait for StreamPixel confirmation
    // Just set this build to processing status (but NOT current)
    const { data: updatedBuilds, error: updateError } = await supabase
      .from('builds')
      .update({
        is_current: false, // Keep as false until StreamPixel confirms
        status: 'processing', // Set to processing until StreamPixel webhook confirms
        updated_at: new Date().toISOString()
      })
      .eq('id', buildId)
      .eq('project_id', projectId)
      .select()

    if (updateError) {
      console.error('Error activating build:', updateError)
      return NextResponse.json(
        { error: 'Failed to activate build', details: updateError.message },
        { status: 500 }
      )
    }
    console.log('Updated builds:', updatedBuilds)

    if (!updatedBuilds || updatedBuilds.length === 0) {
      console.error('No builds were updated. Build might not exist or user might not have permission.')
      return NextResponse.json(
        { error: 'Build not found or permission denied' },
        { status: 404 }
      )
    }

    const updatedBuild = updatedBuilds[0]
    console.log('Build updated successfully:', updatedBuild)

    // Call StreamPixel upload API
    try {
      const streamPixelResponse = await fetch(`${request.url.split('/api')[0]}/api/streampixel/upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': request.headers.get('Cookie') || '',
        },
        body: JSON.stringify({
          buildId: buildId,
          projectId: projectId
        }),
      })

      if (!streamPixelResponse.ok) {
        const errorData = await streamPixelResponse.json()
        console.error('StreamPixel upload failed:', errorData)

        // Update build with error but still return success for activation
        await supabase
          .from('builds')
          .update({
            error_message: errorData.error || 'StreamPixel upload failed',
            updated_at: new Date().toISOString()
          })
          .eq('id', buildId)

        return NextResponse.json({
          message: 'Build activated successfully, but StreamPixel upload failed',
          build: updatedBuild,
          streamPixelError: errorData.error
        })
      }

      const streamPixelData = await streamPixelResponse.json()

      return NextResponse.json({
        message: 'Build activated and uploaded to StreamPixel successfully',
        build: updatedBuild,
        streamPixelResponse: streamPixelData
      })

    } catch (streamPixelError: unknown) {
      console.error('Error calling StreamPixel API:', streamPixelError)

      // Update build with error but still return success for activation
      const errorMessage = streamPixelError instanceof Error ? streamPixelError.message : 'Unknown error'
      await supabase
        .from('builds')
        .update({
          error_message: 'Failed to upload to StreamPixel: ' + errorMessage,
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      return NextResponse.json({
        message: 'Build activated successfully, but StreamPixel upload failed',
        build: updatedBuild,
        streamPixelError: errorMessage
      })
    }

  } catch (error: unknown) {
    console.error('Error activating build:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
