import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server';
import { Build } from '@/lib/supabase';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const {id:projectId} = await params

    // Parse request body
    const body = await request.json()
    const { name, config, auto_release } = body

    // Validate that at least one field is provided
    if (!name && !config && auto_release === undefined) {
      return NextResponse.json(
        { error: 'At least one field (name, config, or auto_release) must be provided' },
        { status: 400 }
      )
    }

    // Prepare update data (allow name, config, and auto_release updates for regular users)
    const updateData: Record<string, unknown> = {}
    if (name) updateData.name = name
    if (config) updateData.config = config
    if (auto_release !== undefined) updateData.auto_release = auto_release

    // Verify that the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, user_id, name')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Update the project
    const { data: updatedProject, error: updateError } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', projectId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating project:', updateError)
      return NextResponse.json(
        { error: 'Failed to update project' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      project: updatedProject,
      message: 'Project updated successfully',
    })

  } catch (error) {
    console.error('Error in project update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const {id:projectId} = await params

    // Fetch the project with builds
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        *,
        builds (
          id,
          filename,
          original_filename,
          s3_key,
          version,
          status,
          is_current,
          file_size,
          streampixel_build_id,
          streampixel_status,
          error_message,
          created_at,
          updated_at
        )
      `)
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Sort builds by version descending
    const projectWithSortedBuilds = {
      ...project,
      builds: project.builds
        ?.sort((a:Build, b:Build) => b.version - a.version) || []
    }

    return NextResponse.json({
      project: projectWithSortedBuilds,
    })

  } catch (error) {
    console.error('Error fetching project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
