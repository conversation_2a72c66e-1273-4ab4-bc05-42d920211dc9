import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { buildId, s3Key } = body

    // Validate required fields
    if (!buildId || !s3Key) {
      return NextResponse.json(
        { error: 'Missing required fields: buildId, s3Key' },
        { status: 400 }
      )
    }

    // Get build from database to verify ownership
    const { data: build, error: buildError } = await supabase
      .from('builds')
      .select('*, project:projects(*)')
      .eq('id', buildId)
      .single()

    if (buildError || !build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    // Verify user owns the project
    if (build.project.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Process StreamPixel upload directly (no Lambda needed)
    try {
      // Update database status to processing
      await supabase
        .from('builds')
        .update({
          streampixel_status: 'processing',
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      // Call the existing StreamPixel upload logic
      const uploadResponse = await fetch(`${request.nextUrl.origin}/api/streampixel/upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': request.headers.get('Cookie') || '', // Forward auth cookies
        },
        body: JSON.stringify({
          buildId: buildId,
          s3Key: s3Key
        })
      })

      const uploadResult = await uploadResponse.json()

      if (uploadResponse.ok && uploadResult.success) {
        return NextResponse.json({
          message: 'StreamPixel upload successful',
          build: {
            id: build.id,
            s3_key: s3Key,
            streampixel_status: 'uploaded',
            is_live: true
          }
        })
      } else {
        // Update database with error
        await supabase
          .from('builds')
          .update({
            streampixel_status: 'failed',
            error_message: uploadResult.error || 'StreamPixel upload failed',
            updated_at: new Date().toISOString()
          })
          .eq('id', buildId)

        return NextResponse.json({
          error: uploadResult.error || 'StreamPixel upload failed',
          build: {
            id: build.id,
            s3_key: s3Key,
            streampixel_status: 'failed'
          }
        }, { status: 400 })
      }

    } catch (error) {
      console.error('Error processing StreamPixel upload:', error)

      // Update database with error
      await supabase
        .from('builds')
        .update({
          streampixel_status: 'failed',
          error_message: 'Internal server error',
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      return NextResponse.json(
        { error: 'Failed to process StreamPixel upload' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error activating StreamPixel:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
