import { NextRequest, NextResponse } from 'next/server'
import { getPublicS3FileUrl } from '@/lib/aws'
import { createClient } from '@/utils/supabase/server'

// StreamPixel API configuration
const STREAMPIXEL_API_URL = 'https://api.streampixel.io/pixelStripeApi/projects/upload-file'

interface StreamPixelUploadRequest {
  apiKey: string
  userId: string
  fileUrl: string
  projectId: string
  autoRelease: boolean
}



// POST - Upload build to StreamPixel
export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for server-side auth
    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { buildId, projectId } = body

    // Validate required fields
    if (!buildId || !projectId) {
      return NextResponse.json(
        { error: 'Missing required fields: buildId, projectId' },
        { status: 400 }
      )
    }

    // Validate StreamPixel configuration
    if (!process.env.STREAMPIXEL_API_KEY || !process.env.STREAMPIXEL_USER_ID) {
      console.error('StreamPixel configuration check:', {
        apiKey: process.env.STREAMPIXEL_API_KEY ? 'Present' : 'Missing',
        userId: process.env.STREAMPIXEL_USER_ID ? 'Present' : 'Missing'
      })
      return NextResponse.json(
        {
          error: 'StreamPixel API configuration missing',
          details: {
            apiKey: process.env.STREAMPIXEL_API_KEY ? 'Present' : 'Missing',
            userId: process.env.STREAMPIXEL_USER_ID ? 'Present' : 'Missing'
          }
        },
        { status: 500 }
      )
    }

    // Verify user owns this project or is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Failed to verify user profile' },
        { status: 500 }
      )
    }

    const isAdmin = profile?.role === 'platform_admin'

    // Get project details
    let projectQuery = supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)

    if (!isAdmin) {
      projectQuery = projectQuery.eq('user_id', user.id)
    }

    const { data: project, error: projectError } = await projectQuery.single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    // Verify project has StreamPixel configuration
    if (!project.stream_project_id) {
      return NextResponse.json(
        { error: 'Project does not have StreamPixel configuration' },
        { status: 400 }
      )
    }

    // Get build details
    const { data: build, error: buildError } = await supabase
      .from('builds')
      .select('*')
      .eq('id', buildId)
      .eq('project_id', projectId)
      .single()

    if (buildError || !build) {
      return NextResponse.json(
        { error: 'Build not found' },
        { status: 404 }
      )
    }

    // Check if build is already uploaded to StreamPixel
    if (build.streampixel_build_id && build.streampixel_status === 'uploaded') {
      return NextResponse.json(
        { error: 'Build is already uploaded to StreamPixel' },
        { status: 400 }
      )
    }

    // Make the specific Backblaze B2 object public for StreamPixel access
    // console.log('Making Backblaze B2 object public for StreamPixel:', build.b2_key)
    // try {
    //   await makeS3ObjectPublic(build.b2_key)
    //   console.log('Successfully made Backblaze B2 object public')
    // } catch (aclError) {
    //   console.error('Failed to make Backblaze B2 object public:', aclError)
    //   return NextResponse.json(
    //     {
    //       error: 'Failed to make file publicly accessible',
    //       details: 'Could not set public read permissions on the file'
    //     },
    //     { status: 500 }
    //   )
    // }

    // Generate public Backblaze B2 URL for StreamPixel (no expiration)
    const fileUrl = getPublicS3FileUrl(build.s3_key)
    console.log('Generated public Backblaze B2 URL for StreamPixel:', fileUrl)

    // Prepare StreamPixel API request - use project's auto_release setting
    const streamPixelRequest: StreamPixelUploadRequest = {
      apiKey: process.env.STREAMPIXEL_API_KEY!,
      userId: process.env.STREAMPIXEL_USER_ID!,
      fileUrl: fileUrl,
      projectId: project.stream_project_id,
      autoRelease: project.auto_release // Use project's auto_release setting
    }

    console.log('StreamPixel request details:', {
      apiKey: process.env.STREAMPIXEL_API_KEY ? 'Present' : 'Missing',
      userId: process.env.STREAMPIXEL_USER_ID,
      fileUrl: fileUrl,
      projectId: project.stream_project_id,
      autoRelease: project.auto_release
    })

    // Update build status to uploading
    await supabase
      .from('builds')
      .update({
        status: 'processing',
        streampixel_status: 'uploading',
        updated_at: new Date().toISOString()
      })
      .eq('id', buildId)

    try {
      // Call StreamPixel API with correct endpoint
      console.log('StreamPixel upload request:', streamPixelRequest)
      console.log('StreamPixel API URL:', STREAMPIXEL_API_URL)

      const streamPixelResponse = await fetch(STREAMPIXEL_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(streamPixelRequest),
      })

      let streamPixelData
      try {
        const responseText = await streamPixelResponse.text()
        console.log('StreamPixel API response:', responseText)

        if (responseText.startsWith('<!DOCTYPE') || responseText.startsWith('<html')) {
          throw new Error('StreamPixel API returned HTML instead of JSON - likely an error page')
        }

        streamPixelData = JSON.parse(responseText)
      } catch (parseError) {
        console.error('Failed to parse StreamPixel response:', parseError)
        throw new Error('Invalid response from StreamPixel API')
      }

      if (streamPixelResponse.ok && streamPixelData.success) {
        // Update build with successful upload - but not live yet, waiting for webhook
        const { data: updatedBuild, error: updateError } = await supabase
          .from('builds')
          .update({
            status: 'processing', // Processing by StreamPixel, not live yet
            streampixel_build_id: streamPixelData.uploadId || 'pending',
            streampixel_status: 'processing', // Processing, not uploaded yet
            error_message: null, // Clear any previous errors
            updated_at: new Date().toISOString()
          })
          .eq('id', buildId)
          .select()
          .single()

        if (updateError) {
          console.error('Error updating build after StreamPixel upload:', updateError)
        }

        return NextResponse.json({
          message: 'Build sent to StreamPixel for processing. Final status will be updated via webhook.',
          build: updatedBuild,
          streamPixelResponse: streamPixelData
        })

      } else {
        // Handle StreamPixel API errors (including validation failures)
        let errorMessage = streamPixelData.error || streamPixelData.message || 'StreamPixel upload failed'
        let statusType = 'failed'
        let buildStatus = 'failed'

        // Check if it's a validation error (URL access issue)
        if (errorMessage.includes('Validation failed for URL')) {
          statusType = 'validation_failed'
          buildStatus = 'uploaded' // Build uploaded successfully, but StreamPixel validation failed
          errorMessage = 'File URL validation failed - StreamPixel cannot access the file. Please ensure S3 bucket has public read access.'
        }

        // Update build with error details
        await supabase
          .from('builds')
          .update({
            status: buildStatus,
            error_message: errorMessage,
            streampixel_status: statusType,
            is_current: false, // Ensure build is not marked as current if validation fails
            updated_at: new Date().toISOString()
          })
          .eq('id', buildId)

        return NextResponse.json(
          {
            error: errorMessage,
            type: statusType,
            details: streamPixelData
          },
          { status: 500 }
        )
      }

    } catch (streamPixelError: unknown) {
      console.error('Error calling StreamPixel API:', streamPixelError)

      // Determine error type and message
      let errorMessage = streamPixelError instanceof Error ? streamPixelError.message : 'Failed to connect to StreamPixel'
      let statusType = 'failed'
      let buildStatus = 'failed'

      // Check if it's a validation error (URL access issue)
      if (errorMessage.includes('Validation failed for URL')) {
        statusType = 'validation_failed'
        buildStatus = 'uploaded' // Build uploaded successfully, but StreamPixel validation failed
        errorMessage = 'File URL validation failed - StreamPixel cannot access the file. Please ensure S3 bucket has public read access.'
      }

      // Update build with specific error details
      await supabase
        .from('builds')
        .update({
          status: buildStatus,
          error_message: errorMessage,
          streampixel_status: statusType,
          is_current: false, // Ensure build is not marked as current if validation fails
          updated_at: new Date().toISOString()
        })
        .eq('id', buildId)

      return NextResponse.json(
        {
          error: 'StreamPixel upload failed',
          details: errorMessage,
          type: statusType
        },
        { status: 500 }
      )
    }

  } catch (error: unknown) {
    console.error('Error in StreamPixel upload:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
