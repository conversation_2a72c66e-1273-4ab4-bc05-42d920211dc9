// app/dashboard/page.tsx
import { redirect } from 'next/navigation'
import DashboardClient from './client'
import { createClient } from '@/utils/supabase/server'

export default async function DashboardPage() {
  const supabase = await createClient();

  // Get the user
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    redirect('/login')
  }

  // Get the profile (assuming you have a profiles table)
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  // Fetch projects for user (handle admin logic if needed)
  // If user is admin, fetch all. Otherwise, only their projects.
  let projects = []
  if (profile?.role === 'platform_admin') {
    const { data } = await supabase
      .from('projects')
      .select(`
        *,
        builds (
          id,
          filename,
          original_filename,
          version,
          created_at,
          status,
          is_current
        )
      `)
      .order('created_at', { ascending: false })
    projects = data || []
  } else {
    const { data } = await supabase
      .from('projects')
      .select(`
        *,
        builds (
          id,
          filename,
          original_filename,
          version,
          created_at,
          status,
          is_current
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
    projects = data || []
  }

  return (
    <DashboardClient
      user={user}
      profile={profile}
      projects={projects}
    />
  )
}
