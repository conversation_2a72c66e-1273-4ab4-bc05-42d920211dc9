'use client'

import { StreamPlayer } from '@/components/stream-player'
import { EnhancedConfigEditor } from '@/components/enhanced-config-editor'
import { useState, useEffect } from 'react'

export default function TestStreamPage() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])
  const [config, setConfig] = useState({
    autoConnect: false,
    touchInput: true,
    keyBoardInput: true,
    resolutionMode: 'Dynamic Resolution Mode',
    maxStreamQuality: '1080p (1920x1080)',
    primaryCodec: 'H264',
    fallBackCodec: 'VP8',
    isPasswordProtected: false,
    password: '',
    loadingMessage: 'Loading stream...',
    connectingMessage: 'Connecting to stream...',
    disconnectedMessage: 'Stream disconnected',
    reconnectingMessage: 'Reconnecting...',
    errorMessage: 'Stream error occurred',
    connectButtonText: 'Connect to Stream'
  })

  // Prevent SSR issues with StreamPixel components
  if (!isClient) {
    return (
      <div className="min-h-screen bg-gray-50 p-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading stream test page...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Stream Player Test</h1>
          <p className="text-gray-600">Test the enhanced stream player and configuration editor</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Stream Player */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Stream Player</h2>
            <StreamPlayer
              projectId="test-project-123"
              buildId="test-build-789"
              config={config}
              showControls={true}
              showHeader={true}
              showEmbedButton={true}
              className="w-full"
            />
          </div>

          {/* Configuration Editor */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Configuration Editor</h2>
            <EnhancedConfigEditor
              projectId="test-project-123"
              currentConfig={config}
              onConfigUpdate={(newConfig) => {
                setConfig(newConfig)
                console.log('Config updated:', newConfig)
              }}
              isAdmin={false}
              className="w-full"
            />
          </div>
        </div>

        {/* Password Protected Example */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Password Protected Stream</h2>
          <StreamPlayer
            projectId="test-project-protected"
            config={{
              ...config,
              isPasswordProtected: true,
              password: 'test123'
            }}
            showControls={true}
            showHeader={true}
            showEmbedButton={true}
            className="w-full max-w-2xl"
          />
        </div>

        {/* Embedded Style Example */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Embedded Style (No Header)</h2>
          <StreamPlayer
            projectId="test-project-embedded"
            config={config}
            showControls={true}
            showHeader={false}
            showEmbedButton={false}
            isEmbedded={true}
            className="w-full max-w-2xl"
          />
        </div>
      </div>
    </div>
  )
}
