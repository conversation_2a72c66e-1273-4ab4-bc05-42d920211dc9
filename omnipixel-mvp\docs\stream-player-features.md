# Stream Player Features

This document outlines the enhanced stream player features including whitelabeling, embedding, and configuration options.

## Overview

The OmniPixel stream player has been enhanced with the following key features:

- **Whitelabeled Experience**: No mention of StreamPixel or third-party branding
- **Embeddable Player**: Generate embed codes for external websites
- **Password Protection**: Optional password protection for streams
- **Configurable UI Messages**: Customize all user-facing messages
- **Advanced Stream Settings**: Full control over streaming parameters
- **Auto-Connect Toggle**: Choose between manual and automatic connection

## Whitelabeling

### Removed Branding
- No "StreamPixel" mentions in the UI
- Generic titles like "Interactive Stream" instead of branded names
- Clean, professional appearance suitable for any brand

### Customizable Messages
All user-facing messages can be customized:
- Loading message
- Connecting message
- Disconnected message
- Reconnecting message
- Error message
- Connect button text

## Embedding Feature

### Embed Code Generation
Users can generate embed codes for their streams:

```html
<iframe 
  src="https://your-domain.com/embed/project-id" 
  width="1280" 
  height="720" 
  frameborder="0" 
  allowfullscreen>
</iframe>
```

### Embed Page Features
- Dedicated `/embed/[projectId]` route
- Optimized for iframe embedding
- Responsive design
- SEO metadata for social sharing
- Security headers for password-protected streams

### Embed Security
- Cross-origin restrictions for password-protected streams
- Prevents right-click context menu
- Disables text selection and drag/drop
- Sends load events to parent frame

## Password Protection

### Configuration
- Toggle password protection on/off
- Set custom password for stream access
- Password validation before stream access

### User Experience
- Password prompt dialog for protected streams
- Clear error messages for invalid passwords
- Seamless access after successful authentication

### Security Features
- Passwords stored securely in database
- Client-side validation with server-side verification
- Protected streams cannot be embedded without authentication

## Enhanced Configuration Editor

### Stream Settings Tab
- **Auto Connect**: Toggle automatic connection on load
- **Touch Input**: Enable/disable touch controls
- **Keyboard Input**: Enable/disable keyboard controls
- **Resolution Mode**: Dynamic, Fixed, or Adaptive resolution
- **Max Stream Quality**: 4K, 1440p, 1080p, 720p, 480p options
- **Primary Codec**: H264, H265, VP8, VP9, AV1
- **Fallback Codec**: H264, VP8, VP9

### Security Tab
- **Password Protection**: Toggle password protection
- **Stream Password**: Set password for protected streams

### Messages Tab
- **Loading Message**: Customize loading text
- **Connecting Message**: Customize connecting text
- **Disconnected Message**: Customize disconnected text
- **Reconnecting Message**: Customize reconnecting text
- **Error Message**: Customize error text
- **Connect Button Text**: Customize connect button text

## Auto-Connect Feature

### Manual Connection Mode (Default)
- Shows connect button when auto-connect is disabled
- User must click to initiate stream connection
- Better for bandwidth management
- Gives users control over when to start streaming

### Automatic Connection Mode
- Automatically connects when stream loads
- Immediate streaming experience
- Better for embedded scenarios
- Configurable per project

## API Integration

### Configuration Storage
Stream configurations are stored as JSON in the database:

```json
{
  "autoConnect": false,
  "touchInput": true,
  "keyBoardInput": true,
  "resolutionMode": "Dynamic Resolution Mode",
  "maxStreamQuality": "1080p (1920x1080)",
  "primaryCodec": "H264",
  "fallBackCodec": "VP8",
  "isPasswordProtected": false,
  "password": "",
  "loadingMessage": "Loading stream...",
  "connectingMessage": "Connecting to stream...",
  "disconnectedMessage": "Stream disconnected",
  "reconnectingMessage": "Reconnecting...",
  "errorMessage": "Stream error occurred",
  "connectButtonText": "Connect to Stream"
}
```

### API Endpoints
- `PATCH /api/projects/[id]`: Update project configuration (user)
- `PATCH /api/admin/projects`: Update project configuration (admin)
- `GET /embed/[projectId]`: Embed page for streams

## Usage Examples

### Basic Stream Player
```tsx
<StreamPlayer
  projectId="project-123"
  config={projectConfig}
  showControls={true}
  showHeader={true}
  showEmbedButton={true}
/>
```

### Embedded Stream Player
```tsx
<StreamPlayer
  projectId="project-123"
  config={projectConfig}
  showControls={true}
  showHeader={false}
  showEmbedButton={false}
  isEmbedded={true}
/>
```

### Password Protected Stream
```tsx
<StreamPlayer
  projectId="project-123"
  config={{
    ...projectConfig,
    isPasswordProtected: true,
    password: "secret123"
  }}
/>
```

## Configuration Best Practices

### Performance Optimization
- Use appropriate resolution for target audience
- Choose efficient codecs (H264 for compatibility, H265 for quality)
- Enable auto-connect only when necessary

### User Experience
- Customize messages to match your brand voice
- Use clear, actionable button text
- Provide helpful error messages

### Security Considerations
- Use strong passwords for protected streams
- Regularly rotate stream passwords
- Monitor embed usage for unauthorized access

### Accessibility
- Ensure custom messages are clear and descriptive
- Test with screen readers
- Provide keyboard navigation support

## Browser Compatibility

### Supported Browsers
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Mobile Support
- iOS Safari 13+
- Chrome Mobile 80+
- Samsung Internet 12+

### Codec Support
- H264: Universal support
- H265: Modern browsers only
- VP8/VP9: Chrome, Firefox, Edge
- AV1: Latest browsers only

## Troubleshooting

### Common Issues

#### Embed Not Loading
- Check iframe src URL
- Verify project is public or password is correct
- Check browser console for errors

#### Password Protection Not Working
- Verify password is set in configuration
- Check database config field
- Ensure isPasswordProtected is true

#### Auto-Connect Not Working
- Verify autoConnect is enabled in config
- Check browser autoplay policies
- Ensure user interaction if required

#### Custom Messages Not Showing
- Verify config is saved to database
- Check component props are passed correctly
- Refresh browser cache

### Debug Tools
- Browser developer console
- Network tab for API calls
- Application tab for localStorage
- StreamPixel SDK debug logs

## Future Enhancements

### Planned Features
- Analytics integration for embed views
- Custom CSS themes for embedded players
- Advanced security options (IP restrictions)
- Multi-language message support
- Webhook notifications for embed events

### API Improvements
- Bulk configuration updates
- Configuration templates
- Version history for configurations
- Import/export configuration settings
